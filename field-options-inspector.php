<?php
/**
 * Field Options Inspector
 * 
 * This script examines the field options in the database and displays them for debugging.
 */

// Load WordPress
require_once('wp-load.php');

// Check if user is logged in and has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to access this page.');
}

// Get form ID from URL
$form_id = isset($_GET['form_id']) ? intval($_GET['form_id']) : 0;

if (!$form_id) {
    // List all forms
    global $wpdb;
    $forms_table = $wpdb->prefix . 'pfb_forms';
    $forms = $wpdb->get_results("SELECT * FROM {$forms_table} ORDER BY id DESC", ARRAY_A);
    
    echo "<h1>Field Options Inspector</h1>";
    echo "<h2>Select a Form</h2>";
    
    if (empty($forms)) {
        echo "<p>No forms found.</p>";
    } else {
        echo "<ul>";
        foreach ($forms as $form) {
            echo "<li><a href='?form_id={$form['id']}'>{$form['title']} (ID: {$form['id']})</a></li>";
        }
        echo "</ul>";
    }
    
    die();
}

// Get database connection
global $wpdb;
$fields_table = $wpdb->prefix . 'pfb_form_fields';
$forms_table = $wpdb->prefix . 'pfb_forms';

// Get form details
$form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$forms_table} WHERE id = %d", $form_id), ARRAY_A);

if (!$form) {
    die("Form with ID {$form_id} not found.");
}

// Get fields for the form
$fields = $wpdb->get_results($wpdb->prepare("SELECT * FROM {$fields_table} WHERE form_id = %d ORDER BY field_order", $form_id), ARRAY_A);

if (empty($fields)) {
    die("No fields found for form ID {$form_id}.");
}

// Process direct field option update
if (isset($_POST['action']) && $_POST['action'] === 'update_field_option') {
    $field_id = isset($_POST['field_id']) ? intval($_POST['field_id']) : 0;
    $option_key = isset($_POST['option_key']) ? sanitize_text_field($_POST['option_key']) : '';
    $option_value = isset($_POST['option_value']) ? $_POST['option_value'] : '';
    
    if ($field_id && $option_key) {
        // Get current field options
        $field = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$fields_table} WHERE id = %d", $field_id), ARRAY_A);
        
        if ($field) {
            $options = maybe_unserialize($field['field_options']);
            
            // Update option
            if ($option_key === '_all_') {
                // Replace all options
                try {
                    $new_options = json_decode($option_value, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $options = $new_options;
                    } else {
                        echo "<div style='color: red; padding: 10px; background: #ffeeee; border: 1px solid #ffaaaa; margin: 10px 0;'>Invalid JSON: " . json_last_error_msg() . "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div style='color: red; padding: 10px; background: #ffeeee; border: 1px solid #ffaaaa; margin: 10px 0;'>Error: " . $e->getMessage() . "</div>";
                }
            } else {
                // Update specific option
                $keys = explode('.', $option_key);
                
                if (count($keys) === 1) {
                    $options[$keys[0]] = $option_value;
                } else if (count($keys) === 2) {
                    if (!isset($options[$keys[0]]) || !is_array($options[$keys[0]])) {
                        $options[$keys[0]] = array();
                    }
                    $options[$keys[0]][$keys[1]] = $option_value;
                } else if (count($keys) === 3) {
                    if (!isset($options[$keys[0]]) || !is_array($options[$keys[0]])) {
                        $options[$keys[0]] = array();
                    }
                    if (!isset($options[$keys[0]][$keys[1]]) || !is_array($options[$keys[0]][$keys[1]])) {
                        $options[$keys[0]][$keys[1]] = array();
                    }
                    $options[$keys[0]][$keys[1]][$keys[2]] = $option_value;
                }
            }
            
            // Serialize and save
            $serialized_options = serialize($options);
            
            $result = $wpdb->update(
                $fields_table,
                array('field_options' => $serialized_options),
                array('id' => $field_id),
                array('%s'),
                array('%d')
            );
            
            if ($result !== false) {
                echo "<div style='color: green; padding: 10px; background: #eeffee; border: 1px solid #aaffaa; margin: 10px 0;'>Field option updated successfully!</div>";
            } else {
                echo "<div style='color: red; padding: 10px; background: #ffeeee; border: 1px solid #ffaaaa; margin: 10px 0;'>Error updating field option: " . $wpdb->last_error . "</div>";
            }
        }
    }
}

// Process direct conditional logic update
if (isset($_POST['action']) && $_POST['action'] === 'update_conditional_logic') {
    $field_id = isset($_POST['field_id']) ? intval($_POST['field_id']) : 0;
    $conditional_logic = isset($_POST['conditional_logic']) ? $_POST['conditional_logic'] : '';
    
    if ($field_id && $conditional_logic) {
        try {
            // Validate JSON
            $decoded = json_decode($conditional_logic, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("Invalid JSON: " . json_last_error_msg());
            }
            
            // Update conditional logic
            $result = $wpdb->update(
                $fields_table,
                array('conditional_logic' => $conditional_logic),
                array('id' => $field_id),
                array('%s'),
                array('%d')
            );
            
            if ($result !== false) {
                echo "<div style='color: green; padding: 10px; background: #eeffee; border: 1px solid #aaffaa; margin: 10px 0;'>Conditional logic updated successfully!</div>";
            } else {
                echo "<div style='color: red; padding: 10px; background: #ffeeee; border: 1px solid #ffaaaa; margin: 10px 0;'>Error updating conditional logic: " . $wpdb->last_error . "</div>";
            }
        } catch (Exception $e) {
            echo "<div style='color: red; padding: 10px; background: #ffeeee; border: 1px solid #ffaaaa; margin: 10px 0;'>Error: " . $e->getMessage() . "</div>";
        }
    }
}

// HTML header
echo "<!DOCTYPE html>
<html>
<head>
    <title>Field Options Inspector - Form #{$form_id}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2, h3 { color: #333; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        pre { background-color: #f5f5f5; padding: 10px; overflow: auto; }
        .field-card { border: 1px solid #ddd; margin-bottom: 20px; border-radius: 5px; overflow: hidden; }
        .field-header { background-color: #f2f2f2; padding: 10px; border-bottom: 1px solid #ddd; }
        .field-body { padding: 10px; }
        .field-section { margin-bottom: 15px; }
        .field-section h3 { margin-top: 0; }
        .option-form { margin-top: 10px; }
        .option-form input[type='text'], .option-form textarea { width: 100%; padding: 5px; }
        .option-form button { padding: 5px 10px; background-color: #4CAF50; color: white; border: none; cursor: pointer; }
        .option-form button:hover { background-color: #45a049; }
        .nav { margin-bottom: 20px; }
        .nav a { margin-right: 10px; }
    </style>
</head>
<body>";

// Navigation
echo "<div class='nav'>";
echo "<a href='?'>← Back to Forms List</a>";
echo "</div>";

// Form header
echo "<h1>Field Options Inspector</h1>";
echo "<h2>Form: {$form['title']} (ID: {$form_id})</h2>";

// Display fields
echo "<h3>Fields: " . count($fields) . "</h3>";

foreach ($fields as $field) {
    $options = maybe_unserialize($field['field_options']);
    $conditional_logic = isset($field['conditional_logic']) ? json_decode($field['conditional_logic'], true) : null;
    
    echo "<div class='field-card'>";
    echo "<div class='field-header'>";
    echo "<strong>{$field['field_label']}</strong> (ID: {$field['id']}, Type: {$field['field_type']}, Name: {$field['field_name']})";
    echo "</div>";
    echo "<div class='field-body'>";
    
    // Field options section
    echo "<div class='field-section'>";
    echo "<h3>Field Options</h3>";
    echo "<pre>" . print_r($options, true) . "</pre>";
    
    // Form to update field options
    echo "<div class='option-form'>";
    echo "<form method='post'>";
    echo "<input type='hidden' name='action' value='update_field_option'>";
    echo "<input type='hidden' name='field_id' value='{$field['id']}'>";
    echo "<p><label>Option Key (use dot notation for nested options, e.g. 'items.0.label' or '_all_' to replace all options):</label><br>";
    echo "<input type='text' name='option_key' required></p>";
    echo "<p><label>Option Value (use JSON for complex values):</label><br>";
    echo "<textarea name='option_value' rows='5'></textarea></p>";
    echo "<p><button type='submit'>Update Option</button></p>";
    echo "</form>";
    echo "</div>";
    echo "</div>";
    
    // Conditional logic section
    echo "<div class='field-section'>";
    echo "<h3>Conditional Logic</h3>";
    echo "<pre>" . print_r($conditional_logic, true) . "</pre>";
    
    // Form to update conditional logic
    echo "<div class='option-form'>";
    echo "<form method='post'>";
    echo "<input type='hidden' name='action' value='update_conditional_logic'>";
    echo "<input type='hidden' name='field_id' value='{$field['id']}'>";
    echo "<p><label>Conditional Logic (JSON):</label><br>";
    echo "<textarea name='conditional_logic' rows='10'>" . htmlspecialchars(json_encode($conditional_logic, JSON_PRETTY_PRINT)) . "</textarea></p>";
    echo "<p><button type='submit'>Update Conditional Logic</button></p>";
    echo "</form>";
    echo "</div>";
    echo "</div>";
    
    echo "</div>"; // field-body
    echo "</div>"; // field-card
}

// Database schema
echo "<h3>Database Schema</h3>";
echo "<pre>";
$schema = $wpdb->get_results("DESCRIBE {$fields_table}", ARRAY_A);
print_r($schema);
echo "</pre>";

// HTML footer
echo "</body></html>";
