<?php
/**
 * Field Options Debug Script
 * 
 * This script examines the field options in the database and displays them for debugging.
 */

// Load WordPress
require_once('wp-load.php');

// Check if user is logged in and has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to access this page.');
}

// Get form ID from URL
$form_id = isset($_GET['form_id']) ? intval($_GET['form_id']) : 0;

if (!$form_id) {
    die('Please provide a form_id parameter.');
}

// Get database connection
global $wpdb;
$fields_table = $wpdb->prefix . 'pfb_form_fields';

// Get fields for the form
$fields = $wpdb->get_results($wpdb->prepare("SELECT * FROM {$fields_table} WHERE form_id = %d", $form_id), ARRAY_A);

if (empty($fields)) {
    die("No fields found for form ID {$form_id}.");
}

echo "<h1>Field Options Debug for Form ID: {$form_id}</h1>";
echo "<p>Found " . count($fields) . " fields.</p>";

// Display field options
echo "<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>";
echo "<tr style='background-color: #f2f2f2;'>";
echo "<th>ID</th>";
echo "<th>Type</th>";
echo "<th>Label</th>";
echo "<th>Name</th>";
echo "<th>Width</th>";
echo "<th>Options (Raw)</th>";
echo "<th>Options (Unserialized)</th>";
echo "<th>Conditional Logic</th>";
echo "</tr>";

foreach ($fields as $field) {
    echo "<tr>";
    echo "<td>{$field['id']}</td>";
    echo "<td>{$field['field_type']}</td>";
    echo "<td>{$field['field_label']}</td>";
    echo "<td>{$field['field_name']}</td>";
    echo "<td>{$field['field_width']}</td>";
    
    // Raw options
    echo "<td><pre>" . htmlspecialchars($field['field_options']) . "</pre></td>";
    
    // Unserialized options
    $options = maybe_unserialize($field['field_options']);
    echo "<td><pre>" . print_r($options, true) . "</pre></td>";
    
    // Conditional logic
    $conditional_logic = isset($field['conditional_logic']) ? maybe_unserialize($field['conditional_logic']) : 'N/A';
    echo "<td><pre>" . print_r($conditional_logic, true) . "</pre></td>";
    
    echo "</tr>";
}

echo "</table>";

// Add a form to test saving field options directly
echo "<h2>Test Direct Field Options Save</h2>";
echo "<form method='post' action=''>";
echo "<input type='hidden' name='action' value='save_field_options'>";
echo "<input type='hidden' name='form_id' value='{$form_id}'>";
echo "<p>Field ID: <input type='number' name='field_id' required></p>";
echo "<p>Field Options (JSON):<br>";
echo "<textarea name='field_options' rows='10' cols='80' required></textarea></p>";
echo "<p><input type='submit' value='Save Field Options'></p>";
echo "</form>";

// Process form submission
if (isset($_POST['action']) && $_POST['action'] === 'save_field_options') {
    $field_id = isset($_POST['field_id']) ? intval($_POST['field_id']) : 0;
    $field_options_json = isset($_POST['field_options']) ? $_POST['field_options'] : '';
    
    if (!$field_id || empty($field_options_json)) {
        echo "<p style='color: red;'>Error: Field ID and options are required.</p>";
    } else {
        try {
            // Decode JSON
            $field_options = json_decode($field_options_json, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("Invalid JSON: " . json_last_error_msg());
            }
            
            // Serialize options
            $serialized_options = serialize($field_options);
            
            // Update field options in database
            $result = $wpdb->update(
                $fields_table,
                array('field_options' => $serialized_options),
                array('id' => $field_id),
                array('%s'),
                array('%d')
            );
            
            if ($result === false) {
                throw new Exception("Database error: " . $wpdb->last_error);
            }
            
            echo "<p style='color: green;'>Field options saved successfully!</p>";
            echo "<p><a href='?form_id={$form_id}'>Refresh page</a> to see the updated options.</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        }
    }
}

// Add a section to examine the AJAX save request
echo "<h2>AJAX Save Request Debug</h2>";
echo "<p>Add this code to your form to debug the AJAX save request:</p>";
echo "<pre style='background-color: #f5f5f5; padding: 10px;'>";
echo htmlspecialchars('
<script>
// Debug AJAX requests
(function($) {
    // Store the original $.ajax function
    var originalAjax = $.ajax;
    
    // Override the $.ajax function
    $.ajax = function(options) {
        // Check if this is a form save request
        if (options.data && options.data.action === "pfb_save_form") {
            // Log the form data
            console.log("PFB Save Form Data:", options.data.form_data);
            
            // Create a debug div if it doesn\'t exist
            if ($("#pfb-ajax-debug").length === 0) {
                $("body").append("<div id=\'pfb-ajax-debug\' style=\'position: fixed; top: 50px; right: 10px; width: 400px; max-height: 80vh; overflow: auto; background: white; border: 1px solid #ccc; padding: 10px; z-index: 9999;\'><h3>AJAX Debug</h3><pre id=\'pfb-ajax-debug-content\'></pre><button id=\'pfb-ajax-debug-close\' style=\'position: absolute; top: 5px; right: 5px;\'>×</button></div>");
                $("#pfb-ajax-debug-close").on("click", function() {
                    $("#pfb-ajax-debug").hide();
                });
            }
            
            // Show the debug div
            $("#pfb-ajax-debug").show();
            
            // Update the debug content
            $("#pfb-ajax-debug-content").text(JSON.stringify(options.data.form_data, null, 2));
        }
        
        // Call the original $.ajax function
        return originalAjax.apply(this, arguments);
    };
})(jQuery);
</script>
');
echo "</pre>";

// Add a section to examine the database schema
echo "<h2>Database Schema</h2>";
echo "<pre>";
$schema = $wpdb->get_results("DESCRIBE {$fields_table}", ARRAY_A);
print_r($schema);
echo "</pre>";
