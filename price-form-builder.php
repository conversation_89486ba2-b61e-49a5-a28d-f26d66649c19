<?php
/**
 * Plugin Name: Price Form Builder
 * Plugin URI: https://example.com/price-form-builder
 * Description: A beautiful modern smart dynamic form builder for price calculation with multi-language and multi-currency support.
 * Version: 1.0.0
 * Author: WordPress Developer
 * Author URI: https://example.com
 * Text Domain: price-form-builder
 * Domain Path: /languages
 * Requires at least: 5.6
 * Requires PHP: 7.4
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Define plugin constants
define('PFB_VERSION', '1.0.0');
define('PFB_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('PFB_PLUGIN_URL', plugin_dir_url(__FILE__));
define('PFB_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * The code that runs during plugin activation.
 */
function activate_price_form_builder() {
    require_once PFB_PLUGIN_DIR . 'includes/class-pfb-activator.php';
    PFB_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 */
function deactivate_price_form_builder() {
    require_once PFB_PLUGIN_DIR . 'includes/class-pfb-deactivator.php';
    PFB_Deactivator::deactivate();
}

register_activation_hook(__FILE__, 'activate_price_form_builder');
register_deactivation_hook(__FILE__, 'deactivate_price_form_builder');

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require_once PFB_PLUGIN_DIR . 'includes/class-pfb.php';

/**
 * Run database updates if needed.
 */
function pfb_maybe_update_db() {
    // Get current DB version
    $db_version = get_option('pfb_db_version', '1.0.0');

    error_log('PFB: Current DB version: ' . $db_version);

    // Force update for testing - remove this in production
    delete_option('pfb_db_version');
    $db_version = '1.0.0';
    error_log('PFB: Forcing DB update by resetting version to: ' . $db_version);

    // If DB version is less than 1.0.1, run the update
    if (version_compare($db_version, '1.0.1', '<')) {
        error_log('PFB: Running database update to version 1.0.1');
        require_once PFB_PLUGIN_DIR . 'includes/update-db.php';

        // Add field_width column
        $result = pfb_add_field_width_column();
        error_log('PFB: Field width column added: ' . ($result ? 'success' : 'failed'));

        // Update DB version
        update_option('pfb_db_version', '1.0.1');
        error_log('PFB: DB version updated to 1.0.1');
    }

    // If DB version is less than 1.0.2, run the update for conditional logic
    if (version_compare($db_version, '1.0.2', '<')) {
        error_log('PFB: Running database update to version 1.0.2');
        require_once PFB_PLUGIN_DIR . 'includes/update-db.php';

        // Add conditional_logic column
        if (function_exists('pfb_add_conditional_logic_column')) {
            $result = pfb_add_conditional_logic_column();
            error_log('PFB: Conditional logic column added: ' . ($result ? 'success' : 'failed'));
        } else {
            // Fallback: add the column directly if the function doesn't exist
            global $wpdb;
            $table_name = $wpdb->prefix . 'pfb_form_fields';
            $result = $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN conditional_logic LONGTEXT AFTER field_options");
            error_log('PFB: Conditional logic column added directly: ' . ($result !== false ? 'success' : 'failed'));
        }

        // Update DB version
        update_option('pfb_db_version', '1.0.2');
        error_log('PFB: DB version updated to 1.0.2');
    } else {
        error_log('PFB: No database update needed');
    }

    // Create the pfb_variables table if it doesn't exist
    require_once PFB_PLUGIN_DIR . 'includes/create-variables-table.php';
    pfb_create_variables_table();
}

/**
 * Load the form save handler
 */
require_once PFB_PLUGIN_DIR . 'includes/class-pfb-form-save-handler.php';

// Initialize the form save handler
$pfb_form_save_handler = new PFB_Form_Save_Handler();

/**
 * Begins execution of the plugin.
 */
function run_price_form_builder() {
    // Run DB updates if needed
    pfb_maybe_update_db();

    $plugin = new PFB();
    $plugin->run();
}

run_price_form_builder();
