/**
 * Debug script for Price Form Builder
 */
(function($) {
    'use strict';

    // Add debug logging
    console.log('PFB Debug: Script loaded');

    $(document).ready(function() {
        console.log('PFB Debug: Document ready');

        // Debug form loading
        if ($('#pfb-form-editor-form').length) {
            console.log('PFB Debug: Form editor detected');

            // Check if we're editing an existing form
            const urlParams = new URLSearchParams(window.location.search);
            const formId = urlParams.get('form_id');

            if (formId) {
                console.log('PFB Debug: Editing form with ID:', formId);
                
                // Add a button to manually load the form
                $('.pfb-admin-header').append(
                    '<button id="pfb-debug-load-form" class="pfb-btn pfb-btn-secondary" style="margin-left: 10px;">Debug: Load Form</button>'
                );
                
                // Add a button to manually add a field
                $('.pfb-admin-header').append(
                    '<button id="pfb-debug-add-field" class="pfb-btn pfb-btn-secondary" style="margin-left: 10px;">Debug: Add Text Field</button>'
                );

                // Handle manual form loading
                $('#pfb-debug-load-form').on('click', function() {
                    console.log('PFB Debug: Manually loading form');
                    
                    // Clear existing fields
                    $('.pfb-form-fields').empty();
                    
                    // Make AJAX request to get form data
                    $.ajax({
                        url: pfb_data.ajax_url,
                        type: 'GET',
                        data: {
                            action: 'pfb_get_form',
                            nonce: pfb_data.nonce,
                            form_id: formId
                        },
                        success: function(response) {
                            console.log('PFB Debug: Form data received:', response);
                            
                            if (response.success && response.data && response.data.form) {
                                const form = response.data.form;
                                
                                // Set form data
                                $('#form_title').val(form.title || '');
                                $('#form_description').val(form.description || '');
                                $('#form_status').val(form.status || 'draft');
                                
                                // Check if we have fields
                                if (form.fields && Array.isArray(form.fields) && form.fields.length > 0) {
                                    console.log('PFB Debug: Form has', form.fields.length, 'fields');
                                    
                                    // Process each field manually
                                    form.fields.forEach(function(field, index) {
                                        console.log('PFB Debug: Processing field', index + 1, 'of', form.fields.length, ':', field);
                                        
                                        // Generate a unique ID for the field
                                        const fieldId = 'field_' + Date.now() + '_' + Math.floor(Math.random() * 1000) + '_' + index;
                                        
                                        // Create field HTML
                                        const fieldHtml = `
                                            <div id="${fieldId}" class="pfb-field" data-type="${field.field_type}" data-field-id="${fieldId}">
                                                <div class="pfb-field-header">
                                                    <div class="pfb-field-title">${field.field_label}</div>
                                                    <div class="pfb-field-actions">
                                                        <div class="pfb-field-action pfb-field-move-up" title="Move Up"><span class="dashicons dashicons-arrow-up-alt2"></span></div>
                                                        <div class="pfb-field-action pfb-field-move-down" title="Move Down"><span class="dashicons dashicons-arrow-down-alt2"></span></div>
                                                        <div class="pfb-field-action pfb-field-edit" title="Edit"><span class="dashicons dashicons-edit"></span></div>
                                                        <div class="pfb-field-action pfb-field-duplicate" title="Duplicate"><span class="dashicons dashicons-admin-page"></span></div>
                                                        <div class="pfb-field-action pfb-field-delete" title="Delete"><span class="dashicons dashicons-trash"></span></div>
                                                    </div>
                                                </div>
                                                <div class="pfb-field-preview">
                                                    ${getFieldPreview(field.field_type)}
                                                </div>
                                                <div class="pfb-field-settings">
                                                    ${getFieldSettings(field.field_type, fieldId)}
                                                </div>
                                            </div>
                                        `;
                                        
                                        // Add field to form
                                        $('.pfb-form-fields').append(fieldHtml);
                                        
                                        // Set field data
                                        const $field = $('#' + fieldId);
                                        $field.find('.pfb-field-label-input').val(field.field_label);
                                        $field.find('.pfb-field-name-input').val(field.field_name);
                                        $field.find('.pfb-field-required-input').prop('checked', field.field_required == 1);
                                    });
                                } else {
                                    console.log('PFB Debug: No fields found in form data');
                                    $('.pfb-form-fields').html('<div class="pfb-empty-form-message">No fields found. Add fields using the buttons above.</div>');
                                }
                            } else {
                                console.error('PFB Debug: Failed to load form data');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('PFB Debug: AJAX error:', error);
                        }
                    });
                });
                
                // Handle manual field addition
                $('#pfb-debug-add-field').on('click', function() {
                    console.log('PFB Debug: Manually adding text field');
                    
                    // Generate a unique ID for the field
                    const fieldId = 'field_' + Date.now();
                    
                    // Create field HTML
                    const fieldHtml = `
                        <div id="${fieldId}" class="pfb-field" data-type="text" data-field-id="${fieldId}">
                            <div class="pfb-field-header">
                                <div class="pfb-field-title">Text Input</div>
                                <div class="pfb-field-actions">
                                    <div class="pfb-field-action pfb-field-move-up" title="Move Up"><span class="dashicons dashicons-arrow-up-alt2"></span></div>
                                    <div class="pfb-field-action pfb-field-move-down" title="Move Down"><span class="dashicons dashicons-arrow-down-alt2"></span></div>
                                    <div class="pfb-field-action pfb-field-edit" title="Edit"><span class="dashicons dashicons-edit"></span></div>
                                    <div class="pfb-field-action pfb-field-duplicate" title="Duplicate"><span class="dashicons dashicons-admin-page"></span></div>
                                    <div class="pfb-field-action pfb-field-delete" title="Delete"><span class="dashicons dashicons-trash"></span></div>
                                </div>
                            </div>
                            <div class="pfb-field-preview">
                                <input type="text" class="pfb-form-control" placeholder="Text Input" disabled>
                            </div>
                            <div class="pfb-field-settings">
                                <div class="pfb-form-group">
                                    <label>Field Label</label>
                                    <input type="text" class="pfb-form-control pfb-field-label-input" value="Text Input">
                                </div>
                                <div class="pfb-form-group">
                                    <label>Field Name</label>
                                    <input type="text" class="pfb-form-control pfb-field-name-input" value="text_field_${Date.now()}">
                                </div>
                                <div class="pfb-form-group">
                                    <label>
                                        <input type="checkbox" class="pfb-field-required-input"> Required Field
                                    </label>
                                </div>
                                <div class="pfb-form-group">
                                    <button type="button" class="pfb-btn pfb-btn-primary pfb-save-field-settings">Save</button>
                                    <button type="button" class="pfb-btn pfb-btn-secondary pfb-cancel-field-settings">Cancel</button>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Add field to form
                    $('.pfb-form-fields').append(fieldHtml);
                    
                    // Hide empty message if it's visible
                    $('.pfb-empty-form-message').hide();
                });
            }
        }
    });
    
    // Helper function to get field preview HTML
    function getFieldPreview(type) {
        switch (type) {
            case 'text':
                return '<input type="text" class="pfb-form-control" placeholder="Text Input" disabled>';
            case 'number':
                return '<input type="number" class="pfb-form-control" placeholder="0" disabled>';
            case 'dropdown':
                return '<select class="pfb-form-control" disabled><option>Option 1</option></select>';
            case 'radio':
                return '<div class="pfb-radio"><label><input type="radio" disabled> Option 1</label></div>';
            case 'checkbox':
                return '<div class="pfb-checkbox"><label><input type="checkbox" disabled> Option 1</label></div>';
            case 'slider':
                return '<input type="range" class="pfb-slider-control" disabled>';
            case 'total':
                return '<div class="pfb-total-preview">$0.00</div>';
            default:
                return '<div class="pfb-field-preview-placeholder">Field Preview</div>';
        }
    }
    
    // Helper function to get field settings HTML
    function getFieldSettings(type, id) {
        let html = `
            <div class="pfb-form-group">
                <label>Field Label</label>
                <input type="text" class="pfb-form-control pfb-field-label-input" value="${type.charAt(0).toUpperCase() + type.slice(1)}">
            </div>
            <div class="pfb-form-group">
                <label>Field Name</label>
                <input type="text" class="pfb-form-control pfb-field-name-input" value="${type}_field_${Date.now()}">
            </div>
            <div class="pfb-form-group">
                <label>
                    <input type="checkbox" class="pfb-field-required-input"> Required Field
                </label>
            </div>
        `;
        
        // Add field-specific settings
        
        // Add save/cancel buttons
        html += `
            <div class="pfb-form-group">
                <button type="button" class="pfb-btn pfb-btn-primary pfb-save-field-settings">Save</button>
                <button type="button" class="pfb-btn pfb-btn-secondary pfb-cancel-field-settings">Cancel</button>
            </div>
        `;
        
        return html;
    }

})(jQuery);
