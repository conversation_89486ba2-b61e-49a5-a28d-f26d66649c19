/**
 * Conditional Logic Editor
 * 
 * Handles the conditional logic UI in the form editor.
 */
(function($) {
    'use strict';
    
    /**
     * Conditional Logic Editor
     */
    var PFB_ConditionalLogicEditor = {
        
        /**
         * Initialize the editor
         */
        init: function() {
            console.log('Initializing conditional logic editor');
            
            // Add event listeners
            this.bindEvents();
            
            // Initialize existing conditional logic
            this.initExistingLogic();
        },
        
        /**
         * Bind events
         */
        bindEvents: function() {
            // Toggle conditional logic
            $(document).on('click', '.pfb-toggle-conditional-logic', function(e) {
                e.preventDefault();
                
                var $field = $(this).closest('.pfb-field');
                var fieldId = $field.data('field-id');
                
                PFB_ConditionalLogicEditor.toggleConditionalLogic($field);
            });
            
            // Add rule
            $(document).on('click', '.pfb-add-rule', function(e) {
                e.preventDefault();
                
                var $field = $(this).closest('.pfb-field');
                PFB_ConditionalLogicEditor.addRule($field);
            });
            
            // Remove rule
            $(document).on('click', '.pfb-remove-rule', function(e) {
                e.preventDefault();
                
                var $rule = $(this).closest('.pfb-rule');
                PFB_ConditionalLogicEditor.removeRule($rule);
            });
            
            // Change logic type
            $(document).on('change', '.pfb-logic-type', function() {
                var $field = $(this).closest('.pfb-field');
                PFB_ConditionalLogicEditor.updateConditionalLogic($field);
            });
            
            // Change rule field
            $(document).on('change', '.pfb-rule-field', function() {
                var $rule = $(this).closest('.pfb-rule');
                PFB_ConditionalLogicEditor.updateRuleField($rule);
            });
            
            // Change rule operator
            $(document).on('change', '.pfb-rule-operator', function() {
                var $rule = $(this).closest('.pfb-rule');
                PFB_ConditionalLogicEditor.updateRuleOperator($rule);
            });
            
            // Change rule value
            $(document).on('change', '.pfb-rule-value', function() {
                var $rule = $(this).closest('.pfb-rule');
                PFB_ConditionalLogicEditor.updateRuleValue($rule);
            });
        },
        
        /**
         * Initialize existing conditional logic
         */
        initExistingLogic: function() {
            $('.pfb-field').each(function() {
                var $field = $(this);
                var conditionalLogic = $field.data('conditional-logic');
                
                if (conditionalLogic && conditionalLogic.enabled) {
                    // Show conditional logic UI
                    $field.find('.pfb-conditional-logic-container').show();
                    $field.find('.pfb-toggle-conditional-logic').prop('checked', true);
                    
                    // Set logic type
                    $field.find('.pfb-logic-type').val(conditionalLogic.logic_type || 'all');
                    
                    // Add rules
                    if (conditionalLogic.rules && conditionalLogic.rules.length) {
                        // Clear existing rules
                        $field.find('.pfb-rules-container').empty();
                        
                        // Add each rule
                        $.each(conditionalLogic.rules, function(i, rule) {
                            PFB_ConditionalLogicEditor.addRule($field, rule);
                        });
                    }
                }
            });
        },
        
        /**
         * Toggle conditional logic
         */
        toggleConditionalLogic: function($field) {
            var $toggle = $field.find('.pfb-toggle-conditional-logic');
            var $container = $field.find('.pfb-conditional-logic-container');
            
            if ($toggle.prop('checked')) {
                $container.slideDown(200);
                
                // Initialize conditional logic if not already
                var conditionalLogic = $field.data('conditional-logic');
                if (!conditionalLogic) {
                    conditionalLogic = {
                        enabled: true,
                        logic_type: 'all',
                        rules: []
                    };
                    
                    $field.data('conditional-logic', conditionalLogic);
                    
                    // Add first rule
                    this.addRule($field);
                } else {
                    conditionalLogic.enabled = true;
                    $field.data('conditional-logic', conditionalLogic);
                }
            } else {
                $container.slideUp(200);
                
                // Disable conditional logic
                var conditionalLogic = $field.data('conditional-logic');
                if (conditionalLogic) {
                    conditionalLogic.enabled = false;
                    $field.data('conditional-logic', conditionalLogic);
                }
            }
        },
        
        /**
         * Add a rule
         */
        addRule: function($field, rule) {
            var $rulesContainer = $field.find('.pfb-rules-container');
            var ruleIndex = $rulesContainer.children().length;
            
            // Create rule template
            var $rule = $(this.getRuleTemplate(ruleIndex));
            
            // Add rule to container
            $rulesContainer.append($rule);
            
            // Populate field dropdown
            this.populateFieldDropdown($rule);
            
            // Set rule values if provided
            if (rule) {
                $rule.find('.pfb-rule-field').val(rule.field);
                $rule.find('.pfb-rule-operator').val(rule.operator);
                $rule.find('.pfb-rule-value').val(rule.value);
                
                // Store field name if available
                if (rule.field_name) {
                    $rule.data('field-name', rule.field_name);
                }
            }
            
            // Update conditional logic
            this.updateConditionalLogic($field);
        },
        
        /**
         * Remove a rule
         */
        removeRule: function($rule) {
            var $field = $rule.closest('.pfb-field');
            
            // Remove rule
            $rule.remove();
            
            // Update conditional logic
            this.updateConditionalLogic($field);
        },
        
        /**
         * Update conditional logic
         */
        updateConditionalLogic: function($field) {
            var conditionalLogic = {
                enabled: $field.find('.pfb-toggle-conditional-logic').prop('checked'),
                logic_type: $field.find('.pfb-logic-type').val(),
                rules: []
            };
            
            // Get rules
            $field.find('.pfb-rule').each(function() {
                var $rule = $(this);
                var rule = {
                    field: $rule.find('.pfb-rule-field').val(),
                    operator: $rule.find('.pfb-rule-operator').val(),
                    value: $rule.find('.pfb-rule-value').val()
                };
                
                // Add field name if available
                var fieldName = $rule.data('field-name');
                if (fieldName) {
                    rule.field_name = fieldName;
                }
                
                conditionalLogic.rules.push(rule);
            });
            
            // Store conditional logic
            $field.data('conditional-logic', conditionalLogic);
            
            console.log('Updated conditional logic for field', $field.data('field-id'), ':', conditionalLogic);
        },
        
        /**
         * Update rule field
         */
        updateRuleField: function($rule) {
            var $field = $rule.closest('.pfb-field');
            var $fieldSelect = $rule.find('.pfb-rule-field');
            var fieldId = $fieldSelect.val();
            
            // Get field name
            var fieldName = $fieldSelect.find('option:selected').text();
            $rule.data('field-name', fieldName);
            
            // Update conditional logic
            this.updateConditionalLogic($field);
        },
        
        /**
         * Update rule operator
         */
        updateRuleOperator: function($rule) {
            var $field = $rule.closest('.pfb-field');
            
            // Update conditional logic
            this.updateConditionalLogic($field);
        },
        
        /**
         * Update rule value
         */
        updateRuleValue: function($rule) {
            var $field = $rule.closest('.pfb-field');
            
            // Update conditional logic
            this.updateConditionalLogic($field);
        },
        
        /**
         * Populate field dropdown
         */
        populateFieldDropdown: function($rule) {
            var $field = $rule.closest('.pfb-field');
            var $fieldSelect = $rule.find('.pfb-rule-field');
            var currentFieldId = $field.data('field-id');
            
            // Clear dropdown
            $fieldSelect.empty();
            
            // Add option for each field
            $('.pfb-field').each(function() {
                var $otherField = $(this);
                var fieldId = $otherField.data('field-id');
                var fieldLabel = $otherField.find('.pfb-field-label').text();
                var fieldType = $otherField.data('type');
                
                // Skip current field and fields that can't be used for conditions
                if (fieldId === currentFieldId || 
                    fieldType === 'submit' || 
                    fieldType === 'html' || 
                    fieldType === 'divider') {
                    return;
                }
                
                $fieldSelect.append($('<option></option>')
                    .attr('value', fieldId)
                    .text(fieldLabel));
            });
        },
        
        /**
         * Get rule template
         */
        getRuleTemplate: function(index) {
            return `
                <div class="pfb-rule" data-rule-index="${index}">
                    <div class="pfb-rule-field-container">
                        <select class="pfb-rule-field">
                            <!-- Options will be populated dynamically -->
                        </select>
                    </div>
                    <div class="pfb-rule-operator-container">
                        <select class="pfb-rule-operator">
                            <option value="is">is</option>
                            <option value="is_not">is not</option>
                            <option value="greater_than">greater than</option>
                            <option value="less_than">less than</option>
                            <option value="contains">contains</option>
                            <option value="starts_with">starts with</option>
                            <option value="ends_with">ends with</option>
                            <option value="is_empty">is empty</option>
                            <option value="is_not_empty">is not empty</option>
                        </select>
                    </div>
                    <div class="pfb-rule-value-container">
                        <input type="text" class="pfb-rule-value" placeholder="Value">
                    </div>
                    <div class="pfb-rule-actions">
                        <button type="button" class="pfb-remove-rule button">Remove</button>
                    </div>
                </div>
            `;
        }
    };
    
    // Initialize on document ready
    $(document).ready(function() {
        PFB_ConditionalLogicEditor.init();
    });
    
})(jQuery);
