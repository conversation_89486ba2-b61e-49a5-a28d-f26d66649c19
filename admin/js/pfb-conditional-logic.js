/**
 * JavaScript for handling conditional logic in the form editor.
 *
 * @since      1.0.2
 */

(function($) {
    'use strict';

    // Initialize conditional logic UI
    function initConditionalLogic() {
        // Add conditional logic toggle to field settings
        $('.pfb-field-settings').each(function() {
            if (!$(this).find('.pfb-conditional-logic-toggle').length) {
                addConditionalLogicToggle($(this));
            }
        });

        // Initialize existing conditional logic
        $('.pfb-conditional-logic-container').each(function() {
            initExistingRules($(this));
        });

        // Bind events
        bindConditionalLogicEvents();
    }

    // Add conditional logic toggle to field settings
    function addConditionalLogicToggle(fieldSettings) {
        // Skip for total fields as they should always be visible
        if (fieldSettings.closest('.pfb-field').data('type') === 'total') {
            return;
        }

        const fieldElement = fieldSettings.closest('.pfb-field');
        const fieldId = fieldElement.attr('id') || fieldElement.data('field-id');
        const fieldType = fieldElement.data('type');

        console.log('Adding conditional logic toggle for field:', fieldId, 'type:', fieldType);

        // Check if the toggle already exists
        if (fieldSettings.find('.pfb-conditional-logic-toggle').length > 0) {
            console.log('Conditional logic toggle already exists for field:', fieldId);

            // Just populate the field dropdown
            populateFieldDropdown(fieldSettings.find('.pfb-rule-field'), fieldId);
            return;
        }

        // Create toggle HTML
        const toggleHtml = `
            <div class="pfb-form-group pfb-conditional-logic-toggle">
                <label>
                    <input type="checkbox" class="pfb-enable-conditional-logic" data-field-id="${fieldId}">
                    ${pfbAdminL10n.conditionalLogic}
                </label>
                <div class="pfb-conditional-logic-container" style="display: none;">
                    <p>${pfbAdminL10n.showThisFieldWhen}</p>
                    <div class="pfb-conditional-rules">
                        <div class="pfb-conditional-rule">
                            <select class="pfb-rule-field">
                                <option value="">${pfbAdminL10n.selectField}</option>
                            </select>
                            <select class="pfb-rule-operator">
                                <option value="is">${pfbAdminL10n.is}</option>
                                <option value="is_not">${pfbAdminL10n.isNot}</option>
                                <option value="greater_than">${pfbAdminL10n.greaterThan}</option>
                                <option value="less_than">${pfbAdminL10n.lessThan}</option>
                                <option value="contains">${pfbAdminL10n.contains}</option>
                            </select>
                            <input type="text" class="pfb-rule-value" placeholder="${pfbAdminL10n.value}">
                            <button type="button" class="pfb-remove-rule pfb-btn pfb-btn-icon">
                                <span class="dashicons dashicons-no-alt"></span>
                            </button>
                        </div>
                    </div>
                    <div class="pfb-conditional-logic-actions">
                        <button type="button" class="pfb-add-rule pfb-btn pfb-btn-secondary">
                            <span class="dashicons dashicons-plus"></span> ${pfbAdminL10n.addRule}
                        </button>
                        <select class="pfb-logic-type">
                            <option value="all">${pfbAdminL10n.allConditions}</option>
                            <option value="any">${pfbAdminL10n.anyCondition}</option>
                        </select>
                    </div>
                </div>
            </div>
        `;

        // Add toggle to field settings before the save button
        const $saveButton = fieldSettings.find('.pfb-save-field-settings').closest('.pfb-form-group');
        if ($saveButton.length) {
            $saveButton.before(toggleHtml);
        } else {
            // Fallback to appending if save button not found
            fieldSettings.append(toggleHtml);
        }

        // Populate field dropdown
        populateFieldDropdown(fieldSettings.find('.pfb-rule-field'), fieldId);
    }

    // Populate field dropdown with available fields
    function populateFieldDropdown(dropdown, currentFieldId) {
        // Clear existing options except the first one
        dropdown.find('option:not(:first)').remove();

        console.log('Populating field dropdown for field:', currentFieldId);

        // Get all fields
        $('.pfb-field').each(function() {
            const fieldElement = $(this);
            const fieldId = fieldElement.attr('id') || fieldElement.data('field-id');
            const fieldType = fieldElement.data('type');

            // Get field label from input or title
            const fieldLabel = fieldElement.find('.pfb-field-label-input').val() ||
                              fieldElement.find('.pfb-field-title').text().trim();

            // Get field name from input
            const fieldName = fieldElement.find('.pfb-field-name-input').val() || '';

            // Skip current field and fields that can't be used in conditions
            if (fieldId !== currentFieldId &&
                fieldType !== 'total' &&
                fieldType !== 'subtotal' &&
                fieldType !== 'html' &&
                fieldType !== 'divider') {

                console.log(`Adding field to dropdown: id=${fieldId}, name=${fieldName}, label=${fieldLabel}, type=${fieldType}`);

                // Create option with data attributes for field name and type
                dropdown.append(
                    `<option value="${fieldId}"
                             data-field-name="${fieldName}"
                             data-field-type="${fieldType}">
                        ${fieldLabel}
                     </option>`
                );
            }
        });
    }

    // Initialize existing conditional logic rules
    function initExistingRules(container) {
        const fieldElement = container.closest('.pfb-field');
        const fieldId = fieldElement.attr('id') || fieldElement.data('field-id');
        const fieldType = fieldElement.data('type');
        let conditionalLogic = fieldElement.data('conditional-logic');

        console.log('Initializing existing conditional logic for field:', fieldId, 'type:', fieldType);

        // If conditionalLogic is a string, try to parse it
        if (typeof conditionalLogic === 'string' && conditionalLogic.trim() !== '') {
            try {
                conditionalLogic = JSON.parse(conditionalLogic);
                console.log('Parsed conditional logic from string:', conditionalLogic);
            } catch (e) {
                console.error('Error parsing conditional logic:', e);
            }
        }

        // Debug log the conditional logic
        console.log('Conditional logic data:', conditionalLogic);

        if (conditionalLogic) {
            // Enable conditional logic if it's enabled or has rules
            const isEnabled = conditionalLogic.enabled ||
                             (conditionalLogic.rules && conditionalLogic.rules.length > 0);

            if (isEnabled) {
                container.closest('.pfb-conditional-logic-toggle')
                    .find('.pfb-enable-conditional-logic')
                    .prop('checked', true);
                container.show();
            }

            // Set logic type
            const logicType = conditionalLogic.logic_type || 'all';
            container.find('.pfb-logic-type').val(logicType);
            console.log('Set logic type to:', logicType);

            // Clear existing rules
            container.find('.pfb-conditional-rules').empty();

            // Add rules
            if (conditionalLogic.rules && conditionalLogic.rules.length) {
                console.log('Adding', conditionalLogic.rules.length, 'rules');
                conditionalLogic.rules.forEach(function(rule, index) {
                    console.log('Adding rule', index + 1, ':', rule);
                    addRuleRow(container, rule);
                });
            } else {
                // Add default rule
                console.log('No rules found, adding default rule');
                addRuleRow(container);
            }
        } else {
            console.log('No conditional logic data found');
            // Add default rule
            addRuleRow(container);
        }
    }

    // Add a new rule row
    function addRuleRow(container, rule = null) {
        const fieldElement = container.closest('.pfb-field');
        const fieldId = fieldElement.attr('id') || fieldElement.data('field-id');

        console.log('Adding rule row for field:', fieldId, 'with rule:', rule);

        // Create rule HTML
        const ruleHtml = `
            <div class="pfb-conditional-rule">
                <select class="pfb-rule-field">
                    <option value="">${pfbAdminL10n.selectField}</option>
                </select>
                <select class="pfb-rule-operator">
                    <option value="is">${pfbAdminL10n.is}</option>
                    <option value="is_not">${pfbAdminL10n.isNot}</option>
                    <option value="greater_than">${pfbAdminL10n.greaterThan}</option>
                    <option value="less_than">${pfbAdminL10n.lessThan}</option>
                    <option value="contains">${pfbAdminL10n.contains}</option>
                </select>
                <input type="text" class="pfb-rule-value" placeholder="${pfbAdminL10n.value}">
                <button type="button" class="pfb-remove-rule pfb-btn pfb-btn-icon">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>
            </div>
        `;

        // Add rule to container
        const ruleElement = $(ruleHtml);
        container.find('.pfb-conditional-rules').append(ruleElement);

        // Populate field dropdown
        populateFieldDropdown(ruleElement.find('.pfb-rule-field'), fieldId);

        // Set values if rule is provided
        if (rule) {
            console.log('Setting rule values:', rule);

            // First set the field value
            const fieldSelect = ruleElement.find('.pfb-rule-field');

            // Check if the field ID is valid (not 0 or empty)
            if (!rule.field || rule.field === '0' || rule.field === 0) {
                console.error('Invalid field ID in rule:', rule);

                // Try to find the field by name
                if (rule.field_name) {
                    // Look for a field with this name in the form
                    let foundField = false;
                    $('.pfb-field').each(function() {
                        const $f = $(this);
                        const fName = $f.find('.pfb-field-name-input').val();
                        if (fName === rule.field_name) {
                            const fId = $f.attr('id');
                            console.log('Found field by name:', rule.field_name, 'ID:', fId);
                            rule.field = fId;
                            foundField = true;
                            return false; // Break the loop
                        }
                    });

                    if (!foundField) {
                        console.log('Could not find field by name:', rule.field_name);
                    }
                }
            }

            // Now try to set the field value
            fieldSelect.val(rule.field);

            // If field wasn't found by ID but we have field_name, try to find by name
            if ((!fieldSelect.val() || fieldSelect.val() !== rule.field) && rule.field_name) {
                // Find option by data-field-name attribute
                const matchingOption = fieldSelect.find(`option[data-field-name="${rule.field_name}"]`);
                if (matchingOption.length) {
                    console.log('Found field by name in dropdown:', rule.field_name);
                    fieldSelect.val(matchingOption.val());
                }
            }

            // Set operator and value
            ruleElement.find('.pfb-rule-operator').val(rule.operator || 'is');
            ruleElement.find('.pfb-rule-value').val(rule.value || '');

            // Store field_name as data attribute if available
            if (rule.field_name) {
                const selectedOption = ruleElement.find('.pfb-rule-field option:selected');
                if (!selectedOption.data('field-name')) {
                    selectedOption.attr('data-field-name', rule.field_name);
                }
                console.log('Field name for rule:', rule.field_name);
            }
        }
    }

    // Bind events for conditional logic
    function bindConditionalLogicEvents() {
        // Toggle conditional logic container
        $(document).on('change', '.pfb-enable-conditional-logic', function() {
            const container = $(this).closest('.pfb-conditional-logic-toggle').find('.pfb-conditional-logic-container');

            if ($(this).is(':checked')) {
                container.slideDown();

                // Add default rule if none exists
                if (container.find('.pfb-conditional-rule').length === 0) {
                    addRuleRow(container);
                }
            } else {
                container.slideUp();
            }
        });

        // Add rule
        $(document).on('click', '.pfb-add-rule', function() {
            const container = $(this).closest('.pfb-conditional-logic-container');
            addRuleRow(container);
        });

        // Remove rule
        $(document).on('click', '.pfb-remove-rule', function() {
            const rulesContainer = $(this).closest('.pfb-conditional-rules');
            $(this).closest('.pfb-conditional-rule').remove();

            // Add default rule if all rules are removed
            if (rulesContainer.find('.pfb-conditional-rule').length === 0) {
                addRuleRow(rulesContainer.closest('.pfb-conditional-logic-container'));
            }
        });
    }

    // Get conditional logic data from UI
    function getConditionalLogicData(fieldElement) {
        const fieldId = fieldElement.attr('id') || fieldElement.data('field-id');
        const fieldType = fieldElement.data('type');
        console.log('Getting conditional logic data for field:', fieldId, 'type:', fieldType);

        const conditionalLogicToggle = fieldElement.find('.pfb-conditional-logic-toggle');

        if (!conditionalLogicToggle.length) {
            console.log('No conditional logic toggle found for field:', fieldId);
            // Return empty object instead of null to ensure it's saved to database
            return {
                enabled: false,
                logic_type: 'all',
                rules: []
            };
        }

        const isEnabled = conditionalLogicToggle.find('.pfb-enable-conditional-logic').is(':checked');
        console.log('Conditional logic enabled for field', fieldId, ':', isEnabled);

        // Always return an object even if not enabled
        if (!isEnabled) {
            return {
                enabled: false,
                logic_type: 'all',
                rules: []
            };
        }

        const container = conditionalLogicToggle.find('.pfb-conditional-logic-container');
        const logicType = container.find('.pfb-logic-type').val() || 'all';
        const rules = [];

        container.find('.pfb-conditional-rule').each(function() {
            const $rule = $(this);
            const field = $rule.find('.pfb-rule-field').val();
            const operator = $rule.find('.pfb-rule-operator').val();
            const value = $rule.find('.pfb-rule-value').val();

            // Only add rules that have a field and operator
            if (field && operator) {
                // Get the field name from the selected option
                const $selectedOption = $rule.find('.pfb-rule-field option:selected');
                const fieldName = $selectedOption.data('field-name') || '';

                // Get the field label for better debugging
                const fieldLabel = $selectedOption.text() || '';

                console.log(`Rule: field=${field}, field_name=${fieldName}, label=${fieldLabel}, operator=${operator}, value=${value}`);

                rules.push({
                    field: field,
                    field_name: fieldName,
                    field_label: fieldLabel,
                    operator: operator,
                    value: value || ''
                });
            }
        });

        // Return object even if no rules
        const result = {
            enabled: true,
            logic_type: logicType,
            rules: rules
        };

        console.log('Conditional logic data for field:', fieldId, ':', JSON.stringify(result));

        return result;
    }

    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize conditional logic when form builder is ready
        if ($('.pfb-form-fields').length) {
            // Delay initialization slightly to ensure all fields are loaded
            setTimeout(function() {
                console.log('Initializing conditional logic for all fields');
                initConditionalLogic();
            }, 500);
        }

        // Initialize conditional logic when a new field is added
        $(document).on('pfb:field_added', function(e, fieldElement) {
            const fieldSettings = fieldElement.find('.pfb-field-settings');
            console.log('New field added, adding conditional logic toggle');
            addConditionalLogicToggle(fieldSettings);
        });
    });

    /**
     * Initialize conditional logic for a specific field
     */
    function initField($field) {
        console.log('PFB Conditional Logic: Initializing field', $field.attr('id'));

        // Get the field settings panel
        const fieldSettings = $field.find('.pfb-field-settings');

        if (!fieldSettings.length) {
            console.error('PFB Conditional Logic: Field settings not found for field', $field.attr('id'));
            return;
        }

        // Add conditional logic toggle to the field settings
        addConditionalLogicToggle(fieldSettings);

        // Bind events for this field
        bindFieldEvents($field);
    }

    /**
     * Bind events for a specific field
     */
    function bindFieldEvents($field) {
        const fieldSettings = $field.find('.pfb-field-settings');
        const $toggle = fieldSettings.find('.pfb-conditional-logic-toggle');

        if (!$toggle.length) {
            console.error('PFB Conditional Logic: Toggle not found for field', $field.attr('id'));
            return;
        }

        // Enable/disable conditional logic
        $toggle.find('.pfb-enable-conditional-logic').off('change').on('change', function() {
            const $container = $toggle.find('.pfb-conditional-logic-container');

            if ($(this).is(':checked')) {
                $container.slideDown(200);

                // Make sure we have at least one rule
                if ($container.find('.pfb-conditional-rule').length === 0) {
                    addRuleRow($container);
                }
            } else {
                $container.slideUp(200);
            }
        });

        // Add rule
        $toggle.find('.pfb-add-rule').off('click').on('click', function() {
            const $container = $toggle.find('.pfb-conditional-logic-container');
            addRuleRow($container);
        });

        // Remove rule (using event delegation)
        $toggle.off('click', '.pfb-remove-rule').on('click', '.pfb-remove-rule', function() {
            $(this).closest('.pfb-conditional-rule').remove();
        });
    }

    // Expose functions to global scope
    window.PFBConditionalLogic = {
        init: initConditionalLogic,
        getConditionalLogicData: getConditionalLogicData,
        initField: initField,
        setConditionalLogicData: function($field, conditionalLogic) {
            if (!conditionalLogic) return;

            const container = $field.find('.pfb-conditional-logic-container');
            if (!container.length) return;

            // Store the conditional logic data on the field element
            $field.data('conditional-logic', conditionalLogic);

            // Initialize the UI
            initExistingRules(container);
        }
    };

})(jQuery);
