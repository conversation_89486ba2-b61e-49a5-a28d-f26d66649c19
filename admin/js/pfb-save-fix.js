/**
 * Emergency fix for save button functionality
 */
jQuery(document).ready(function($) {
    console.log('PFB Save Fix: Script loaded');

    // Wait a moment to ensure all other scripts have loaded
    setTimeout(function() {
        console.log('PFB Save Fix: Adding direct save handlers');

        // Remove ALL existing click handlers from both save buttons
        $('#pfb-save-form, #pfb-save-form-bottom').off('click');

        // Direct save handler for top button
        $('#pfb-save-form').on('click', function(e) {
            console.log('PFB Save Fix: Top save button clicked');
            e.preventDefault();
            e.stopPropagation();

            // Use the original getFormData function from pfb-admin.js
            let formData;
            if (typeof window.getFormData === 'function') {
                console.log('PFB Save Fix: Using original getFormData function');
                formData = window.getFormData();
            } else if (typeof getFormData === 'function') {
                console.log('PFB Save Fix: Using global getFormData function');
                formData = getFormData();
            } else {
                console.error('PFB Save Fix: Original getFormData function not found, using fallback');
                alert('Error: Could not find the original getFormData function. Please contact support.');
                return false;
            }

            // Save directly
            saveFormDirect(formData);

            return false;
        });

        // Direct save handler for bottom button
        $('#pfb-save-form-bottom').on('click', function(e) {
            console.log('PFB Save Fix: Bottom save button clicked');
            e.preventDefault();
            e.stopPropagation();

            // Trigger the top save button
            $('#pfb-save-form').trigger('click');

            return false;
        });

        console.log('PFB Save Fix: Direct save handlers added');

        // Add a visual indicator that the emergency fix is active
        $('<div style="position: fixed; top: 32px; right: 0; background: #ff5722; color: white; padding: 5px 10px; z-index: 9999;">Emergency Save Fix Active</div>')
            .appendTo('body');
    }, 1000);

    /**
     * Validate form data before saving
     */
    function validateFormData(formData) {
        console.log('PFB Save Fix: Validating form data');

        // Check if form has a title
        if (!formData.title) {
            return {
                valid: false,
                message: 'Form title is required'
            };
        }

        // Check if form has fields
        if (!formData.fields || formData.fields.length === 0) {
            return {
                valid: false,
                message: 'Form must have at least one field'
            };
        }

        // Check if all fields have names
        for (let i = 0; i < formData.fields.length; i++) {
            const field = formData.fields[i];
            if (!field.name) {
                return {
                    valid: false,
                    message: 'All fields must have a name'
                };
            }
        }

        // Check conditional logic rules
        for (let i = 0; i < formData.fields.length; i++) {
            const field = formData.fields[i];

            if (field.conditional_logic && field.conditional_logic.enabled) {
                // Check if rules exist
                if (!field.conditional_logic.rules || field.conditional_logic.rules.length === 0) {
                    return {
                        valid: false,
                        message: 'Field "' + field.label + '" has conditional logic enabled but no rules'
                    };
                }

                // Check if all rules have a field and operator
                for (let j = 0; j < field.conditional_logic.rules.length; j++) {
                    const rule = field.conditional_logic.rules[j];

                    if (!rule.field) {
                        return {
                            valid: false,
                            message: 'Field "' + field.label + '" has a conditional rule with no target field'
                        };
                    }

                    if (!rule.operator) {
                        return {
                            valid: false,
                            message: 'Field "' + field.label + '" has a conditional rule with no operator'
                        };
                    }
                }
            }
        }

        return {
            valid: true
        };
    }

    /**
     * Save form data directly
     */
    function saveFormDirect(formData) {
        console.log('PFB Save Fix: Saving form directly');

        // Show a saving indicator
        const $savingIndicator = $('<div id="pfb-saving-indicator" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 5px; z-index: 10000;">Saving form...</div>')
            .appendTo('body');

        // Disable save buttons
        $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', true).text('Saving...');

        // Log the form data for debugging
        console.log('PFB Save Fix: Form data to save:', formData);

        // Save via AJAX
        $.ajax({
            url: pfb_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pfb_save_form',
                nonce: pfb_data.nonce,
                form_data: formData
            },
            success: function(response) {
                console.log('PFB Save Fix: Save response:', response);

                if (response.success) {
                    // Show success message
                    $savingIndicator.text('Form saved successfully!').css('background', 'rgba(0,128,0,0.8)');

                    // Get form ID from URL or response
                    const urlParams = new URLSearchParams(window.location.search);
                    const formId = urlParams.get('form_id');

                    // Redirect to edit page if new form
                    if (!formId && response.data && response.data.form_id) {
                        $savingIndicator.text('Form saved successfully! Redirecting...');
                        setTimeout(function() {
                            window.location.href = 'admin.php?page=pfb-form-editor&form_id=' + response.data.form_id;
                        }, 1000);
                    } else {
                        // Remove the indicator after a delay
                        setTimeout(function() {
                            $savingIndicator.fadeOut(500, function() {
                                $(this).remove();
                            });
                        }, 2000);
                    }
                } else {
                    $savingIndicator.text('Error saving form: ' + (response.data ? response.data.message : 'Unknown error')).css('background', 'rgba(200,0,0,0.8)');

                    // Remove the indicator after a delay
                    setTimeout(function() {
                        $savingIndicator.fadeOut(500, function() {
                            $(this).remove();
                        });
                    }, 5000);
                }
            },
            error: function(xhr, status, error) {
                console.error('PFB Save Fix: AJAX error:', status, error);
                $savingIndicator.text('Error saving form: ' + error).css('background', 'rgba(200,0,0,0.8)');

                // Remove the indicator after a delay
                setTimeout(function() {
                    $savingIndicator.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 5000);
            },
            complete: function() {
                // Re-enable save buttons
                $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');
            }
        });
    }
});
