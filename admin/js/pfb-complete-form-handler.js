/**
 * Complete Form Handler
 * 
 * Handles form saving and loading with proper field options support.
 */
(function($) {
    'use strict';
    
    /**
     * Complete Form Handler
     */
    var PFB_CompleteFormHandler = {
        
        /**
         * Initialize the handler
         */
        init: function() {
            console.log('PFB Complete Form Handler: Initializing');
            
            // Override save form function
            $('#pfb-save-form, #pfb-save-form-bottom').off('click').on('click', function(e) {
                e.preventDefault();
                PFB_CompleteFormHandler.saveForm();
            });
        },
        
        /**
         * Save form with complete field options handling
         */
        saveForm: function() {
            console.log('PFB Complete Form Handler: Starting form save');
            
            // Show saving indicator
            const $savingIndicator = $('<div id="pfb-saving-indicator" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 5px; z-index: 10000;">Saving form with field options...</div>')
                .appendTo('body');
            
            // Disable save buttons
            $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', true).text('Saving...');
            
            try {
                // Collect form data
                const formData = PFB_CompleteFormHandler.collectFormData();
                
                console.log('PFB Complete Form Handler: Form data collected:', formData);
                
                // Save via AJAX
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'pfb_save_form_complete',
                        nonce: pfb_data.nonce,
                        form_data: formData
                    },
                    success: function(response) {
                        console.log('PFB Complete Form Handler: Save response:', response);
                        
                        if (response.success) {
                            $savingIndicator.text('Form saved successfully!').css('background', 'rgba(0,128,0,0.8)');
                            
                            // Get form ID from URL or response
                            const urlParams = new URLSearchParams(window.location.search);
                            const formId = urlParams.get('form_id');
                            
                            // Redirect to edit page if new form
                            if (!formId && response.data && response.data.form_id) {
                                $savingIndicator.text('Form saved successfully! Redirecting...');
                                setTimeout(function() {
                                    window.location.href = 'admin.php?page=pfb-form-editor&form_id=' + response.data.form_id;
                                }, 1000);
                            } else {
                                // Remove the indicator after a delay
                                setTimeout(function() {
                                    $savingIndicator.fadeOut(500, function() {
                                        $(this).remove();
                                    });
                                }, 2000);
                            }
                        } else {
                            const errorMessage = response.data && response.data.message
                                ? response.data.message
                                : 'Unknown error';
                            
                            console.error('PFB Complete Form Handler: Error response:', errorMessage);
                            $savingIndicator.text('Error saving form: ' + errorMessage).css('background', 'rgba(200,0,0,0.8)');
                            
                            // Remove the indicator after a delay
                            setTimeout(function() {
                                $savingIndicator.fadeOut(500, function() {
                                    $(this).remove();
                                });
                            }, 5000);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('PFB Complete Form Handler: AJAX error:', status, error);
                        console.error('PFB Complete Form Handler: Response text:', xhr.responseText);
                        
                        $savingIndicator.text('Error saving form: ' + error).css('background', 'rgba(200,0,0,0.8)');
                        
                        // Remove the indicator after a delay
                        setTimeout(function() {
                            $savingIndicator.fadeOut(500, function() {
                                $(this).remove();
                            });
                        }, 5000);
                    },
                    complete: function() {
                        // Re-enable save buttons
                        $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');
                    }
                });
            } catch (error) {
                console.error('PFB Complete Form Handler: Error collecting form data:', error);
                $savingIndicator.text('Error collecting form data: ' + error.message).css('background', 'rgba(200,0,0,0.8)');
                
                // Re-enable save buttons
                $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');
                
                // Remove the indicator after a delay
                setTimeout(function() {
                    $savingIndicator.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 5000);
            }
        },
        
        /**
         * Collect complete form data
         */
        collectFormData: function() {
            console.log('PFB Complete Form Handler: Collecting form data');
            
            // Get form ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const formId = urlParams.get('form_id');
            
            // Basic form data
            const formData = {
                id: formId || null,
                title: $('#form_title').val() || 'Untitled Form',
                description: $('#form_description').val() || '',
                status: $('#form_status').val() || 'draft',
                settings: {
                    template: $('#form_template').val() || 'default',
                    show_currency_selector: $('#form_show_currency_selector').val() || '1',
                    submit_button_text: $('#form_submit_button_text').val() || 'Calculate Price'
                },
                fields: []
            };
            
            // Collect fields
            $('.pfb-field').each(function(index) {
                const $field = $(this);
                const fieldId = $field.attr('id');
                const fieldType = $field.data('type');
                
                console.log(`PFB Complete Form Handler: Processing field #${index + 1}: ${fieldType} (${fieldId})`);
                
                if (!fieldType) {
                    console.log(`PFB Complete Form Handler: Skipping field #${index + 1}, no type specified`);
                    return;
                }
                
                // Basic field data
                const field = {
                    id: fieldId,
                    type: fieldType,
                    label: $field.find('.pfb-field-label-input').val() || `Field ${index + 1}`,
                    name: $field.find('.pfb-field-name-input').val() || `field_${index + 1}`,
                    required: $field.find('.pfb-field-required-input').is(':checked'),
                    width: $field.find('.pfb-field-width-input').val() || '100',
                    options: {}
                };
                
                // Collect field options based on field type
                switch (fieldType) {
                    case 'select':
                    case 'dropdown':
                    case 'radio':
                    case 'checkbox':
                        field.options.items = [];
                        
                        $field.find('.pfb-field-option').each(function() {
                            const $option = $(this);
                            const label = $option.find('.pfb-option-label').val();
                            const value = $option.find('.pfb-option-value').val();
                            const variable = $option.find('.pfb-option-variable').val() || '';
                            
                            if (label && value) {
                                field.options.items.push({
                                    label: label,
                                    value: value,
                                    variable: variable
                                });
                            }
                        });
                        
                        // Ensure at least one option exists
                        if (field.options.items.length === 0) {
                            field.options.items.push({
                                label: 'Option 1',
                                value: 'option_1',
                                variable: ''
                            });
                        }
                        
                        console.log(`PFB Complete Form Handler: Field ${fieldId} has ${field.options.items.length} options:`, field.options.items);
                        break;
                        
                    case 'number':
                        field.options.min = $field.find('.pfb-field-min').val() || '0';
                        field.options.max = $field.find('.pfb-field-max').val() || '100';
                        field.options.step = $field.find('.pfb-field-step').val() || '1';
                        break;
                        
                    case 'slider':
                        field.options.min = parseInt($field.find('.pfb-slider-min-input').val() || '0');
                        field.options.max = parseInt($field.find('.pfb-slider-max-input').val() || '100');
                        field.options.step = parseInt($field.find('.pfb-slider-step-input').val() || '1');
                        field.options.default = parseInt($field.find('.pfb-slider-default-input').val() || '50');
                        break;
                        
                    case 'price':
                    case 'total':
                        field.options.formula = $field.find('.pfb-field-formula-input').val() || '';
                        
                        if (fieldType === 'price') {
                            field.options.price = $field.find('.pfb-field-price').val() || '0';
                        }
                        break;
                        
                    case 'subtotal':
                        field.options.lines = [];
                        
                        $field.find('.pfb-subtotal-line').each(function() {
                            const $line = $(this);
                            field.options.lines.push({
                                label: $line.find('.pfb-subtotal-line-label, .pfb-line-label').val() || 'Line',
                                formula: $line.find('.pfb-subtotal-line-formula, .pfb-line-formula').val() || ''
                            });
                        });
                        
                        // Ensure at least one line exists
                        if (field.options.lines.length === 0) {
                            field.options.lines.push({
                                label: 'Line 1',
                                formula: ''
                            });
                        }
                        
                        field.options.empty_value = $field.find('.pfb-subtotal-empty-value-input').val() || '---';
                        break;
                        
                    case 'text':
                        // Handle hidden field properties
                        if ($field.find('.pfb-field-hidden-input').is(':checked')) {
                            field.hidden = true;
                            field.variable = $field.find('.pfb-hidden-field-variable').val() || '';
                            field.default_value = $field.find('.pfb-hidden-field-value').val() || '';
                        }
                        break;
                }
                
                // Common options
                if ($field.find('.pfb-field-placeholder').length) {
                    field.options.placeholder = $field.find('.pfb-field-placeholder').val() || '';
                }
                
                if ($field.find('.pfb-field-default').length) {
                    field.options.default = $field.find('.pfb-field-default').val() || '';
                }
                
                if ($field.find('.pfb-field-description').length) {
                    field.options.description = $field.find('.pfb-field-description').val() || '';
                }
                
                // Collect conditional logic
                if ($field.find('.pfb-enable-conditional-logic').length) {
                    const isEnabled = $field.find('.pfb-enable-conditional-logic').is(':checked');
                    
                    field.conditional_logic = {
                        enabled: isEnabled,
                        logic_type: $field.find('.pfb-logic-type').val() || 'all',
                        rules: []
                    };
                    
                    if (isEnabled) {
                        $field.find('.pfb-conditional-rule').each(function() {
                            const $rule = $(this);
                            const ruleField = $rule.find('.pfb-rule-field').val();
                            const ruleOperator = $rule.find('.pfb-rule-operator').val();
                            const ruleValue = $rule.find('.pfb-rule-value').val();
                            
                            if (ruleField && ruleOperator) {
                                field.conditional_logic.rules.push({
                                    field: ruleField,
                                    field_name: $rule.find('.pfb-rule-field option:selected').data('field-name') || '',
                                    operator: ruleOperator,
                                    value: ruleValue || ''
                                });
                            }
                        });
                    }
                }
                
                // Add field to form data
                formData.fields.push(field);
            });
            
            console.log('PFB Complete Form Handler: Complete form data collected:', formData);
            return formData;
        }
    };
    
    // Initialize on document ready
    $(document).ready(function() {
        PFB_CompleteFormHandler.init();
    });
    
})(jQuery);
