/**
 * JavaScript for handling conditional formulas in the form editor.
 *
 * @since      1.0.2
 */

(function($) {
    'use strict';

    // Initialize conditional formula UI
    function initConditionalFormula() {
        // Add conditional formula toggle to total and subtotal field settings
        $('.pfb-field[data-type="total"] .pfb-field-settings, .pfb-field[data-type="subtotal"] .pfb-field-settings').each(function() {
            if (!$(this).find('.pfb-conditional-formula-toggle').length) {
                addConditionalFormulaToggle($(this));
            }
        });

        // Initialize existing conditional formulas
        $('.pfb-conditional-formula-container').each(function() {
            initExistingFormulas($(this));
        });

        // Bind events
        bindConditionalFormulaEvents();
    }

    // Add conditional formula toggle to field settings
    function addConditionalFormulaToggle(fieldSettings) {
        const fieldElement = fieldSettings.closest('.pfb-field');
        const fieldId = fieldElement.attr('id') || fieldElement.data('field-id');
        const fieldType = fieldElement.data('type');

        console.log('Adding conditional formula toggle for field:', fieldId);

        // Create toggle HTML
        const toggleHtml = `
            <div class="pfb-form-group pfb-conditional-formula-toggle">
                <label>
                    <input type="checkbox" class="pfb-enable-conditional-formula" data-field-id="${fieldId}">
                    ${pfb_data.i18n.conditionalFormula}
                </label>
                <div class="pfb-conditional-formula-container" style="display: none;">
                    <div class="pfb-conditional-formula">
                        <div class="pfb-conditional-formula-row">
                            <label>${pfb_data.i18n.ifCondition}</label>
                            <div class="pfb-conditional-formula-condition">
                                <select class="pfb-condition-field">
                                    <option value="">${pfb_data.i18n.selectField}</option>
                                </select>
                                <select class="pfb-condition-operator">
                                    <option value="is">${pfb_data.i18n.is}</option>
                                    <option value="is_not">${pfb_data.i18n.isNot}</option>
                                    <option value="greater_than">${pfb_data.i18n.greaterThan}</option>
                                    <option value="less_than">${pfb_data.i18n.lessThan}</option>
                                    <option value="contains">${pfb_data.i18n.contains}</option>
                                </select>
                                <input type="text" class="pfb-condition-value" placeholder="${pfb_data.i18n.value}">
                            </div>
                        </div>
                        <div class="pfb-conditional-formula-row">
                            <label>${pfb_data.i18n.thenFormula}</label>
                            <div class="pfb-formula-editor pfb-then-formula">
                                <textarea class="pfb-formula-input" placeholder="${pfb_data.i18n.formula}"></textarea>
                                <div class="pfb-formula-builder-toggle">
                                    <button type="button" class="pfb-btn pfb-btn-secondary pfb-open-formula-builder">
                                        <span class="dashicons dashicons-editor-code"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="pfb-conditional-formula-row">
                            <label>${pfb_data.i18n.elseFormula}</label>
                            <div class="pfb-formula-editor pfb-else-formula">
                                <textarea class="pfb-formula-input" placeholder="${pfb_data.i18n.formula}"></textarea>
                                <div class="pfb-formula-builder-toggle">
                                    <button type="button" class="pfb-btn pfb-btn-secondary pfb-open-formula-builder">
                                        <span class="dashicons dashicons-editor-code"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add toggle to field settings
        fieldSettings.append(toggleHtml);

        // Populate field dropdown
        populateFieldDropdown(fieldSettings.find('.pfb-condition-field'), fieldId);
    }

    // Populate field dropdown with available fields
    function populateFieldDropdown(dropdown, currentFieldId) {
        // Clear existing options except the first one
        dropdown.find('option:not(:first)').remove();

        console.log('Populating field dropdown for conditional formula field:', currentFieldId);

        // Get all fields
        $('.pfb-field').each(function() {
            const fieldElement = $(this);
            const fieldId = fieldElement.attr('id') || fieldElement.data('field-id');
            const fieldType = fieldElement.data('type');
            const fieldLabel = fieldElement.find('.pfb-field-label-input').val() || fieldElement.find('.pfb-field-title').text().trim();

            console.log('Found field for conditional formula:', fieldId, 'with label:', fieldLabel);

            // Skip current field and total/subtotal fields
            if (fieldId !== currentFieldId && fieldType !== 'total' && fieldType !== 'subtotal') {
                dropdown.append(`<option value="${fieldId}">${fieldLabel}</option>`);
            }
        });
    }

    // Initialize existing conditional formulas
    function initExistingFormulas(container) {
        const fieldElement = container.closest('.pfb-field');
        const fieldId = fieldElement.attr('id') || fieldElement.data('field-id');
        const fieldOptions = fieldElement.data('field-options') || {};

        console.log('Initializing existing conditional formula for field:', fieldId, 'with options:', fieldOptions);

        if (fieldOptions.conditional_formula) {
            // Enable conditional formula
            container.closest('.pfb-conditional-formula-toggle').find('.pfb-enable-conditional-formula').prop('checked', true);
            container.show();

            // Set condition
            if (fieldOptions.conditional_formula.condition) {
                const condition = fieldOptions.conditional_formula.condition;
                container.find('.pfb-condition-field').val(condition.field);
                container.find('.pfb-condition-operator').val(condition.operator);
                container.find('.pfb-condition-value').val(condition.value);
            }

            // Set formulas
            if (fieldOptions.conditional_formula.then_formula) {
                container.find('.pfb-then-formula .pfb-formula-input').val(fieldOptions.conditional_formula.then_formula);
            }

            if (fieldOptions.conditional_formula.else_formula) {
                container.find('.pfb-else-formula .pfb-formula-input').val(fieldOptions.conditional_formula.else_formula);
            }
        }
    }

    // Bind events for conditional formula
    function bindConditionalFormulaEvents() {
        // Toggle conditional formula container
        $(document).on('change', '.pfb-enable-conditional-formula', function() {
            const container = $(this).closest('.pfb-conditional-formula-toggle').find('.pfb-conditional-formula-container');

            if ($(this).is(':checked')) {
                container.slideDown();
            } else {
                container.slideUp();
            }
        });

        // Open formula builder
        $(document).on('click', '.pfb-open-formula-builder', function() {
            const formulaInput = $(this).closest('.pfb-formula-editor').find('.pfb-formula-input');

            // Store the current formula input element in a data attribute
            $('#pfb-formula-builder').data('target-input', formulaInput);

            // Set the current formula in the builder
            $('#pfb-formula-builder-input').val(formulaInput.val());

            // Show the formula builder
            $('#pfb-formula-builder').show();
        });
    }

    // Get conditional formula data from UI
    function getConditionalFormulaData(fieldElement) {
        const conditionalFormulaToggle = fieldElement.find('.pfb-conditional-formula-toggle');

        if (!conditionalFormulaToggle.length) {
            console.log('No conditional formula toggle found for field:', fieldElement.attr('id'));
            return null;
        }

        const isEnabled = conditionalFormulaToggle.find('.pfb-enable-conditional-formula').is(':checked');

        if (!isEnabled) {
            return null;
        }

        const container = conditionalFormulaToggle.find('.pfb-conditional-formula-container');

        // Get condition
        const field = container.find('.pfb-condition-field').val();
        const operator = container.find('.pfb-condition-operator').val();
        const value = container.find('.pfb-condition-value').val();

        // Get formulas
        const thenFormula = container.find('.pfb-then-formula .pfb-formula-input').val();
        const elseFormula = container.find('.pfb-else-formula .pfb-formula-input').val();

        if (!field || !operator || !thenFormula || !elseFormula) {
            return null;
        }

        console.log('Getting conditional formula data for field:', fieldElement.attr('id'), 'with condition:', field, operator, value);

        return {
            condition: {
                field: field,
                operator: operator,
                value: value
            },
            then_formula: thenFormula,
            else_formula: elseFormula
        };
    }

    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize conditional formula when form builder is ready
        if ($('.pfb-form-fields').length) {
            initConditionalFormula();
        }

        // Initialize conditional formula when a new field is added
        $(document).on('pfb:field_added', function(e, fieldElement) {
            const fieldType = fieldElement.data('type');

            if (fieldType === 'total' || fieldType === 'subtotal') {
                const fieldSettings = fieldElement.find('.pfb-field-settings');
                addConditionalFormulaToggle(fieldSettings);
            }
        });

        // Save conditional formula data when form is saved
        $(document).on('pfb:before_save_form', function(e, formData) {
            $('.pfb-field[data-type="total"], .pfb-field[data-type="subtotal"]').each(function() {
                const fieldId = $(this).data('field-id');
                const fieldIndex = formData.fields.findIndex(field => field.id === fieldId);

                if (fieldIndex !== -1) {
                    const conditionalFormula = getConditionalFormulaData($(this));

                    if (conditionalFormula) {
                        // Initialize field_options if it doesn't exist
                        if (!formData.fields[fieldIndex].field_options) {
                            formData.fields[fieldIndex].field_options = {};
                        }

                        // Add conditional formula to field options
                        formData.fields[fieldIndex].field_options.conditional_formula = conditionalFormula;
                    } else {
                        // Remove conditional formula if it exists
                        if (formData.fields[fieldIndex].field_options && formData.fields[fieldIndex].field_options.conditional_formula) {
                            delete formData.fields[fieldIndex].field_options.conditional_formula;
                        }
                    }
                }
            });
        });
    });

    // Expose functions to global scope
    window.PFBConditionalFormula = {
        init: initConditionalFormula,
        getConditionalFormulaData: getConditionalFormulaData
    };

})(jQuery);
