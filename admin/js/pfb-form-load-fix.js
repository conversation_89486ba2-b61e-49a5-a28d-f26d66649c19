/**
 * Form Load Fix
 * A script to fix form loading issues
 */
(function($) {
    'use strict';

    // Wait for document ready
    $(document).ready(function() {
        console.log('PFB Form Load Fix: Initializing...');
        
        // Wait a moment to ensure all other scripts have loaded
        setTimeout(initFormLoadFix, 1000);
    });
    
    /**
     * Initialize the form load fix
     */
    function initFormLoadFix() {
        console.log('PFB Form Load Fix: Setting up load handlers');
        
        // Add visual indicator
        $('<div id="pfb-form-load-fix-indicator" style="position: fixed; top: 32px; right: 150px; background: #2196F3; color: white; padding: 5px 10px; z-index: 9999;">Form Load Fix Active</div>')
            .appendTo('body');
            
        // Check if we're on the form editor page with a form ID
        const urlParams = new URLSearchParams(window.location.search);
        const formId = urlParams.get('form_id');

        if (formId) {
            console.log('PFB Form Load Fix: Form ID detected in URL, will load form:', formId);
            
            // Add a debug button to manually load the form
            $('<button id="pfb-debug-load-form" style="position: fixed; top: 70px; right: 20px; background: #4CAF50; color: white; padding: 10px; z-index: 9999; border: none; cursor: pointer;">Debug: Load Form</button>')
                .appendTo('body')
                .on('click', function() {
                    console.log('PFB Form Load Fix: Manual load button clicked');
                    loadFormFixed(formId);
                });
                
            // Try to load the form after a delay
            setTimeout(function() {
                loadFormFixed(formId);
            }, 2000);
        }
    }
    
    /**
     * Load form with improved error handling
     */
    function loadFormFixed(formId) {
        console.log('PFB Form Load Fix: Loading form with ID:', formId);

        // Show loading indicator
        const $loadingMessage = $('<div id="pfb-loading-indicator" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 5px; z-index: 10000;">Loading form data...</div>');
        $('body').append($loadingMessage);
        
        // Clear existing fields
        $('.pfb-form-fields').empty();

        $.ajax({
            url: ajaxurl,
            type: 'GET',
            data: {
                action: 'pfb_get_form',
                nonce: pfb_data.nonce,
                form_id: formId
            },
            beforeSend: function() {
                console.log('PFB Form Load Fix: Sending AJAX request to load form');
            },
            success: function(response) {
                console.log('PFB Form Load Fix: Received form data response:', response);
                
                if (response.success && response.data && response.data.form) {
                    const form = response.data.form;
                    console.log('PFB Form Load Fix: Form data loaded:', form);
                    
                    try {
                        // Set form data
                        $('#form_title').val(form.title || '');
                        $('#form_description').val(form.description || '');
                        $('#form_status').val(form.status || 'draft');
                        
                        // Load form settings if available
                        if (form.settings) {
                            console.log('PFB Form Load Fix: Form settings:', form.settings);
                            
                            if (form.settings.template) {
                                $('#form_template').val(form.settings.template);
                            }
                            if (form.settings.show_currency_selector !== undefined) {
                                $('#form_show_currency_selector').val(form.settings.show_currency_selector);
                            }
                            if (form.settings.submit_button_text) {
                                $('#form_submit_button_text').val(form.settings.submit_button_text);
                            }
                        }
                        
                        // Check if we have fields
                        if (!form.fields || !Array.isArray(form.fields) || form.fields.length === 0) {
                            console.log('PFB Form Load Fix: No fields found in form data');
                            $('.pfb-form-fields').html('<div class="pfb-empty-form-message">No fields found. Add fields using the buttons above.</div>');
                            $loadingMessage.text('Form loaded successfully (no fields)').css('background', 'rgba(0,128,0,0.8)');
                            setTimeout(function() {
                                $loadingMessage.fadeOut(500, function() {
                                    $(this).remove();
                                });
                            }, 2000);
                            return;
                        }
                        
                        // Process fields one by one
                        processFields(form.fields, 0, $loadingMessage);
                        
                    } catch (error) {
                        console.error('PFB Form Load Fix: Error setting form data:', error);
                        $loadingMessage.text('Error loading form: ' + error.message).css('background', 'rgba(200,0,0,0.8)');
                        setTimeout(function() {
                            $loadingMessage.fadeOut(500, function() {
                                $(this).remove();
                            });
                        }, 5000);
                    }
                } else {
                    console.error('PFB Form Load Fix: Failed to load form data:', response);
                    $loadingMessage.text('Error loading form: ' + (response.data && response.data.message ? response.data.message : 'Unknown error')).css('background', 'rgba(200,0,0,0.8)');
                    setTimeout(function() {
                        $loadingMessage.fadeOut(500, function() {
                            $(this).remove();
                        });
                    }, 5000);
                }
            },
            error: function(xhr, status, error) {
                console.error('PFB Form Load Fix: AJAX error loading form:', status, error);
                console.error('PFB Form Load Fix: Response text:', xhr.responseText);
                $loadingMessage.text('Error loading form: ' + error).css('background', 'rgba(200,0,0,0.8)');
                setTimeout(function() {
                    $loadingMessage.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 5000);
            }
        });
    }
    
    /**
     * Process fields one by one to avoid browser freezing
     */
    function processFields(fields, index, $loadingMessage) {
        if (index >= fields.length) {
            console.log('PFB Form Load Fix: All fields processed successfully');
            $loadingMessage.text('Form loaded successfully').css('background', 'rgba(0,128,0,0.8)');
            setTimeout(function() {
                $loadingMessage.fadeOut(500, function() {
                    $(this).remove();
                });
            }, 2000);
            
            // Initialize conditional logic for all fields after they're loaded
            if (typeof window.PFBConditionalLogic !== 'undefined' && typeof window.PFBConditionalLogic.init === 'function') {
                console.log('PFB Form Load Fix: Initializing conditional logic after loading all fields');
                window.PFBConditionalLogic.init();
            }
            return;
        }
        
        const field = fields[index];
        console.log('PFB Form Load Fix: Processing field ' + (index + 1) + ' of ' + fields.length + ':', field);
        $loadingMessage.text('Loading form... (' + (index + 1) + '/' + fields.length + ' fields)');
        
        try {
            // Generate a unique ID for the field
            const fieldId = 'field_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
            
            // Create the field element
            const $field = window.PFBAdmin.createField(field.field_type, fieldId);
            
            if (!$field || !$field.length) {
                console.error('PFB Form Load Fix: Failed to create field element for field:', field);
                index++;
                setTimeout(function() { processFields(fields, index, $loadingMessage); }, 10);
                return;
            }
            
            // Set field data
            $field.find('.pfb-field-label-input').val(field.field_label);
            $field.find('.pfb-field-name-input').val(field.field_name);
            $field.find('.pfb-field-required-input').prop('checked', field.field_required == 1);
            
            // Set field width
            let fieldWidth = '100';
            if (field.field_width !== undefined && field.field_width !== null) {
                fieldWidth = field.field_width.toString().replace('%', '');
            } else if (field.field_options && field.field_options.width) {
                fieldWidth = field.field_options.width.toString().replace('%', '');
            }
            
            // Set the width in the dropdown and as a data attribute
            const $widthDropdown = $field.find('.pfb-field-width-input');
            if ($widthDropdown.length > 0) {
                $widthDropdown.val(fieldWidth);
            }
            
            // Store the width as a data attribute
            $field.attr('data-width', fieldWidth);
            
            // Add width indicator if not 100%
            if (fieldWidth !== '100') {
                $field.find('.pfb-field-title').after(`<span class="pfb-field-width-indicator">Width: ${fieldWidth}%</span>`);
            }
            
            // Update the field title with the label
            $field.find('.pfb-field-title').text(field.field_label);
            
            // Set field-specific options
            if (field.field_options) {
                window.PFBAdmin.setFieldOptions($field, field);
            }
            
            // Handle conditional logic if available
            if (field.conditional_logic) {
                window.PFBAdmin.setConditionalLogic($field, field.conditional_logic);
            }
            
            // Add the field to the form
            $('.pfb-form-fields').append($field);
            
            // Move to the next field
            index++;
            setTimeout(function() { processFields(fields, index, $loadingMessage); }, 10);
            
        } catch (error) {
            console.error('PFB Form Load Fix: Error processing field:', error);
            index++;
            setTimeout(function() { processFields(fields, index, $loadingMessage); }, 10);
        }
    }
    
    // Expose functions to the global PFBAdmin object
    if (!window.PFBAdmin) {
        window.PFBAdmin = {};
    }
    
    window.PFBAdmin.loadFormFixed = loadFormFixed;
    
    // Add helper functions if they don't exist
    if (!window.PFBAdmin.createField) {
        window.PFBAdmin.createField = function(type, id) {
            console.log('PFB Form Load Fix: Using fallback createField function');
            
            // Basic field HTML
            const html = `
                <div id="${id}" class="pfb-field" data-type="${type}" data-field-id="${id}">
                    <div class="pfb-field-header">
                        <div class="pfb-field-title">${type.charAt(0).toUpperCase() + type.slice(1)}</div>
                        <div class="pfb-field-actions">
                            <div class="pfb-field-action pfb-field-move-up" title="Move Up"><span class="dashicons dashicons-arrow-up-alt2"></span></div>
                            <div class="pfb-field-action pfb-field-move-down" title="Move Down"><span class="dashicons dashicons-arrow-down-alt2"></span></div>
                            <div class="pfb-field-action pfb-field-edit" title="Edit"><span class="dashicons dashicons-edit"></span></div>
                            <div class="pfb-field-action pfb-field-duplicate" title="Duplicate"><span class="dashicons dashicons-admin-page"></span></div>
                            <div class="pfb-field-action pfb-field-delete" title="Delete"><span class="dashicons dashicons-trash"></span></div>
                        </div>
                    </div>
                    <div class="pfb-field-preview"></div>
                    <div class="pfb-field-settings">
                        <div class="pfb-form-group">
                            <label>Field Label</label>
                            <input type="text" class="pfb-form-control pfb-field-label-input">
                        </div>
                        <div class="pfb-form-group">
                            <label>Field Name</label>
                            <input type="text" class="pfb-form-control pfb-field-name-input">
                        </div>
                        <div class="pfb-form-group">
                            <label>
                                <input type="checkbox" class="pfb-field-required-input">
                                Required
                            </label>
                        </div>
                        <div class="pfb-form-group">
                            <label>Field Width</label>
                            <select class="pfb-form-control pfb-field-width-input">
                                <option value="25">25%</option>
                                <option value="33">33%</option>
                                <option value="50">50%</option>
                                <option value="66">66%</option>
                                <option value="75">75%</option>
                                <option value="100" selected>100%</option>
                            </select>
                        </div>
                        <div class="pfb-form-group">
                            <button type="button" class="pfb-btn pfb-btn-primary pfb-save-field-settings">Save Settings</button>
                            <button type="button" class="pfb-btn pfb-btn-secondary pfb-cancel-field-settings">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
            
            return $(html);
        };
    }
    
    if (!window.PFBAdmin.setFieldOptions) {
        window.PFBAdmin.setFieldOptions = function($field, field) {
            console.log('PFB Form Load Fix: Using fallback setFieldOptions function');
            // This is a simplified version - the real implementation would be more complex
            return;
        };
    }
    
    if (!window.PFBAdmin.setConditionalLogic) {
        window.PFBAdmin.setConditionalLogic = function($field, conditionalLogic) {
            console.log('PFB Form Load Fix: Using fallback setConditionalLogic function');
            // This is a simplified version - the real implementation would be more complex
            return;
        };
    }
    
})(jQuery);
