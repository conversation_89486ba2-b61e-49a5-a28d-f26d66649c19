/**
 * Field Options Diagnostic
 * 
 * This script diagnoses field options issues by intercepting the save process
 * and logging exactly what's happening.
 */
(function($) {
    'use strict';
    
    /**
     * Field Options Diagnostic
     */
    var PFB_FieldOptionsDiagnostic = {
        
        /**
         * Initialize diagnostic
         */
        init: function() {
            console.log('PFB Field Options Diagnostic: Initializing');
            
            // Override the save form function to add diagnostics
            this.interceptSaveFunction();
            
            // Add diagnostic button
            this.addDiagnosticButton();
            
            // Monitor field creation
            this.monitorFieldCreation();
        },
        
        /**
         * Intercept save function
         */
        interceptSaveFunction: function() {
            // Store original save function
            if (typeof window.PFBAdmin !== 'undefined' && typeof window.PFBAdmin.saveForm === 'function') {
                window.PFBAdmin.originalSaveForm = window.PFBAdmin.saveForm;
                
                // Override with diagnostic version
                window.PFBAdmin.saveForm = function() {
                    console.log('PFB Field Options Diagnostic: Save form called');
                    PFB_FieldOptionsDiagnostic.diagnoseSave();
                    
                    // Call original function
                    return window.PFBAdmin.originalSaveForm.apply(this, arguments);
                };
            }
        },
        
        /**
         * Add diagnostic button
         */
        addDiagnosticButton: function() {
            const diagnosticButton = `
                <button type="button" id="pfb-diagnose-fields" class="pfb-btn pfb-btn-secondary" style="margin-left: 10px;">
                    <span class="dashicons dashicons-search"></span> Diagnose Field Options
                </button>
            `;
            
            $('#pfb-save-form').after(diagnosticButton);
            
            // Add click handler
            $(document).on('click', '#pfb-diagnose-fields', function(e) {
                e.preventDefault();
                PFB_FieldOptionsDiagnostic.runDiagnostic();
            });
        },
        
        /**
         * Monitor field creation
         */
        monitorFieldCreation: function() {
            // Monitor when fields are added
            $(document).on('click', '.pfb-add-field-btn', function() {
                const fieldType = $(this).data('type');
                console.log('PFB Field Options Diagnostic: Adding field of type:', fieldType);
                
                // Monitor the field after it's created
                setTimeout(function() {
                    PFB_FieldOptionsDiagnostic.checkLastField();
                }, 500);
            });
        },
        
        /**
         * Check the last field that was added
         */
        checkLastField: function() {
            const $lastField = $('.pfb-field').last();
            const fieldType = $lastField.data('type');
            
            console.log('PFB Field Options Diagnostic: Checking last field:', fieldType);
            
            if (fieldType === 'dropdown' || fieldType === 'select' || fieldType === 'radio' || fieldType === 'checkbox') {
                const $optionsContainer = $lastField.find('.pfb-field-options');
                const $options = $lastField.find('.pfb-field-option');
                
                console.log('PFB Field Options Diagnostic: Choice field detected');
                console.log('PFB Field Options Diagnostic: Options container found:', $optionsContainer.length > 0);
                console.log('PFB Field Options Diagnostic: Number of options:', $options.length);
                
                if ($options.length > 0) {
                    $options.each(function(index) {
                        const $option = $(this);
                        const label = $option.find('.pfb-option-label').val();
                        const value = $option.find('.pfb-option-value').val();
                        console.log(`PFB Field Options Diagnostic: Option ${index + 1}: label="${label}", value="${value}"`);
                    });
                } else {
                    console.log('PFB Field Options Diagnostic: No options found in field');
                }
            }
        },
        
        /**
         * Run full diagnostic
         */
        runDiagnostic: function() {
            console.log('=== PFB FIELD OPTIONS DIAGNOSTIC ===');
            
            const $fields = $('.pfb-field');
            console.log(`Found ${$fields.length} fields in form`);
            
            $fields.each(function(index) {
                const $field = $(this);
                const fieldId = $field.attr('id');
                const fieldType = $field.data('type');
                const fieldLabel = $field.find('.pfb-field-label-input').val() || $field.find('.pfb-field-title').text();
                
                console.log(`\n--- Field ${index + 1} ---`);
                console.log(`ID: ${fieldId}`);
                console.log(`Type: ${fieldType}`);
                console.log(`Label: ${fieldLabel}`);
                
                // Check if it's a choice field
                if (fieldType === 'dropdown' || fieldType === 'select' || fieldType === 'radio' || fieldType === 'checkbox') {
                    console.log('This is a choice field, checking options...');
                    
                    const $optionsContainer = $field.find('.pfb-field-options');
                    const $options = $field.find('.pfb-field-option');
                    
                    console.log(`Options container exists: ${$optionsContainer.length > 0}`);
                    console.log(`Number of option elements: ${$options.length}`);
                    
                    if ($options.length > 0) {
                        console.log('Options found:');
                        $options.each(function(optionIndex) {
                            const $option = $(this);
                            const label = $option.find('.pfb-option-label').val();
                            const value = $option.find('.pfb-option-value').val();
                            const variable = $option.find('.pfb-option-variable').val();
                            
                            console.log(`  Option ${optionIndex + 1}:`);
                            console.log(`    Label: "${label}"`);
                            console.log(`    Value: "${value}"`);
                            console.log(`    Variable: "${variable}"`);
                        });
                    } else {
                        console.log('NO OPTIONS FOUND - This is the problem!');
                        
                        // Check if the options container exists but is empty
                        if ($optionsContainer.length > 0) {
                            console.log('Options container exists but is empty');
                            console.log('Container HTML:', $optionsContainer.html());
                        } else {
                            console.log('Options container does not exist');
                        }
                        
                        // Check the entire field HTML
                        console.log('Full field HTML:');
                        console.log($field.html());
                    }
                }
            });
            
            console.log('\n=== DIAGNOSTIC COMPLETE ===');
        },
        
        /**
         * Diagnose save process
         */
        diagnoseSave: function() {
            console.log('=== SAVE DIAGNOSTIC ===');
            
            // Check which save scripts are loaded
            const scripts = [
                'pfb-form-save-v2',
                'pfb-direct-save',
                'pfb-complete-form-handler',
                'pfb-new-save',
                'pfb-complete-save-fix'
            ];
            
            scripts.forEach(function(script) {
                const scriptElement = $(`script[src*="${script}"]`);
                console.log(`Script ${script}: ${scriptElement.length > 0 ? 'LOADED' : 'NOT LOADED'}`);
            });
            
            // Check which save functions are available
            const saveFunctions = [
                'window.PFBAdmin.saveForm',
                'window.PFBAdmin.originalSaveForm',
                'window.PFB_FormSaveV2',
                'window.PFB_DirectSave',
                'window.PFB_CompleteFormHandler'
            ];
            
            saveFunctions.forEach(function(funcPath) {
                const func = eval(`typeof ${funcPath}`);
                console.log(`Function ${funcPath}: ${func}`);
            });
            
            // Try to collect form data using different methods
            console.log('\n--- Testing Form Data Collection ---');
            
            // Method 1: Check if pfb-form-save-v2 collectFormData exists
            if (typeof window.collectFormData === 'function') {
                console.log('Method 1: window.collectFormData exists');
                try {
                    const formData1 = window.collectFormData();
                    console.log('Form data from method 1:', formData1);
                } catch (e) {
                    console.log('Error with method 1:', e);
                }
            }
            
            // Method 2: Manual collection
            console.log('Method 2: Manual collection');
            const manualFormData = this.manualCollectFormData();
            console.log('Manually collected form data:', manualFormData);
        },
        
        /**
         * Manually collect form data for diagnostic
         */
        manualCollectFormData: function() {
            const formData = {
                title: $('#form_title').val(),
                fields: []
            };
            
            $('.pfb-field').each(function(index) {
                const $field = $(this);
                const fieldType = $field.data('type');
                
                const field = {
                    id: $field.attr('id'),
                    type: fieldType,
                    label: $field.find('.pfb-field-label-input').val(),
                    options: {}
                };
                
                // Check for choice field options
                if (fieldType === 'dropdown' || fieldType === 'select' || fieldType === 'radio' || fieldType === 'checkbox') {
                    field.options.items = [];
                    
                    $field.find('.pfb-field-option').each(function() {
                        const $option = $(this);
                        const label = $option.find('.pfb-option-label').val();
                        const value = $option.find('.pfb-option-value').val();
                        
                        if (label || value) {
                            field.options.items.push({
                                label: label || '',
                                value: value || '',
                                variable: $option.find('.pfb-option-variable').val() || ''
                            });
                        }
                    });
                    
                    console.log(`Manual collection: Field ${index + 1} (${fieldType}) has ${field.options.items.length} options`);
                }
                
                formData.fields.push(field);
            });
            
            return formData;
        }
    };
    
    // Initialize on document ready
    $(document).ready(function() {
        PFB_FieldOptionsDiagnostic.init();
    });
    
})(jQuery);
