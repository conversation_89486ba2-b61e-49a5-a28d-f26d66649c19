/**
 * Simple Field Options Fix
 * 
 * This is a simple, direct fix for the field options saving issue.
 * It ensures that when you add a dropdown field, the options are properly saved.
 */
(function($) {
    'use strict';
    
    console.log('PFB Simple Field Options Fix: Loading');
    
    // Wait for document ready
    $(document).ready(function() {
        console.log('PFB Simple Field Options Fix: Document ready');
        
        // Wait a bit more to ensure other scripts are loaded
        setTimeout(function() {
            initializeFieldOptionsFix();
        }, 2000);
    });
    
    function initializeFieldOptionsFix() {
        console.log('PFB Simple Field Options Fix: Initializing');
        
        // Override the save form function completely
        overrideSaveFunction();
        
        // Monitor field creation to ensure options UI exists
        monitorFieldCreation();
        
        // Add a test button for debugging
        addTestButton();
    }
    
    function overrideSaveFunction() {
        console.log('PFB Simple Field Options Fix: Overriding save function');
        
        // Remove all existing save handlers
        $(document).off('click', '#pfb-save-form, #pfb-save-form-bottom');
        
        // Add our own save handler
        $(document).on('click', '#pfb-save-form, #pfb-save-form-bottom', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            console.log('PFB Simple Field Options Fix: Save button clicked');
            saveFormWithFieldOptions();
            
            return false;
        });
    }
    
    function monitorFieldCreation() {
        console.log('PFB Simple Field Options Fix: Monitoring field creation');
        
        // Monitor when dropdown fields are added
        $(document).on('click', '.pfb-add-field-btn[data-type="dropdown"]', function() {
            console.log('PFB Simple Field Options Fix: Dropdown field being added');
            
            // Wait for the field to be created
            setTimeout(function() {
                ensureDropdownOptionsUI();
            }, 1000);
        });
    }
    
    function ensureDropdownOptionsUI() {
        console.log('PFB Simple Field Options Fix: Ensuring dropdown options UI');
        
        // Find the last field (should be the one just added)
        const $lastField = $('.pfb-field').last();
        const fieldType = $lastField.data('type');
        
        if (fieldType === 'dropdown') {
            console.log('PFB Simple Field Options Fix: Found dropdown field, checking options UI');
            
            // Check if options UI exists
            let $optionsContainer = $lastField.find('.pfb-field-options');
            
            if ($optionsContainer.length === 0) {
                console.log('PFB Simple Field Options Fix: Options UI missing, creating it');
                createOptionsUI($lastField);
            } else {
                console.log('PFB Simple Field Options Fix: Options UI exists');
                
                // Ensure at least one option exists
                if ($optionsContainer.find('.pfb-field-option').length === 0) {
                    console.log('PFB Simple Field Options Fix: No options found, adding default option');
                    addDefaultOption($optionsContainer);
                }
            }
        }
    }
    
    function createOptionsUI($field) {
        console.log('PFB Simple Field Options Fix: Creating options UI');
        
        const optionsHTML = `
            <div class="pfb-form-group">
                <label>Options</label>
                <div class="pfb-field-options-header" style="display: flex; margin-bottom: 5px;">
                    <div style="flex: 1; font-weight: bold;">Label</div>
                    <div style="flex: 1; font-weight: bold;">Value</div>
                    <div style="width: 40px;"></div>
                </div>
                <div class="pfb-field-options">
                    <div class="pfb-field-option" style="display: flex; margin-bottom: 5px;">
                        <input type="text" class="pfb-form-control pfb-option-label" placeholder="Label" value="Option 1" style="flex: 1; margin-right: 5px;">
                        <input type="text" class="pfb-form-control pfb-option-value" placeholder="Value" value="option_1" style="flex: 1; margin-right: 5px;">
                        <button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option" style="width: 40px;">-</button>
                    </div>
                </div>
                <button type="button" class="pfb-add-option pfb-btn pfb-btn-secondary" style="margin-top: 5px;">Add Option</button>
            </div>
        `;
        
        // Find the field settings and add options UI before the save/cancel buttons
        const $fieldSettings = $field.find('.pfb-field-settings');
        const $lastGroup = $fieldSettings.find('.pfb-form-group').last();
        
        if ($lastGroup.length > 0) {
            $lastGroup.before(optionsHTML);
            console.log('PFB Simple Field Options Fix: Options UI created successfully');
        } else {
            $fieldSettings.append(optionsHTML);
            console.log('PFB Simple Field Options Fix: Options UI appended to field settings');
        }
    }
    
    function addDefaultOption($optionsContainer) {
        const defaultOptionHTML = `
            <div class="pfb-field-option" style="display: flex; margin-bottom: 5px;">
                <input type="text" class="pfb-form-control pfb-option-label" placeholder="Label" value="Option 1" style="flex: 1; margin-right: 5px;">
                <input type="text" class="pfb-form-control pfb-option-value" placeholder="Value" value="option_1" style="flex: 1; margin-right: 5px;">
                <button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option" style="width: 40px;">-</button>
            </div>
        `;
        
        $optionsContainer.append(defaultOptionHTML);
        console.log('PFB Simple Field Options Fix: Default option added');
    }
    
    function saveFormWithFieldOptions() {
        console.log('PFB Simple Field Options Fix: Starting form save');
        
        // Show saving indicator
        const $savingIndicator = $('<div id="pfb-saving-indicator" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 5px; z-index: 10000;">Saving form...</div>')
            .appendTo('body');
        
        // Disable save buttons
        $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', true).text('Saving...');
        
        try {
            // Collect form data
            const formData = collectFormData();
            
            console.log('PFB Simple Field Options Fix: Form data collected:', formData);
            
            // Save via AJAX
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'pfb_save_form_complete',
                    nonce: pfb_data.nonce,
                    form_data: formData
                },
                success: function(response) {
                    console.log('PFB Simple Field Options Fix: Save response:', response);
                    
                    if (response.success) {
                        $savingIndicator.text('Form saved successfully!').css('background', 'rgba(0,128,0,0.8)');
                        
                        // Get form ID from URL or response
                        const urlParams = new URLSearchParams(window.location.search);
                        const formId = urlParams.get('form_id');
                        
                        // Redirect to edit page if new form
                        if (!formId && response.data && response.data.form_id) {
                            $savingIndicator.text('Form saved successfully! Redirecting...');
                            setTimeout(function() {
                                window.location.href = 'admin.php?page=pfb-form-editor&form_id=' + response.data.form_id;
                            }, 1000);
                        } else {
                            // Remove the indicator after a delay
                            setTimeout(function() {
                                $savingIndicator.fadeOut(500, function() {
                                    $(this).remove();
                                });
                            }, 2000);
                        }
                    } else {
                        const errorMessage = response.data && response.data.message
                            ? response.data.message
                            : 'Unknown error';
                        
                        console.error('PFB Simple Field Options Fix: Error response:', errorMessage);
                        $savingIndicator.text('Error saving form: ' + errorMessage).css('background', 'rgba(200,0,0,0.8)');
                        
                        // Remove the indicator after a delay
                        setTimeout(function() {
                            $savingIndicator.fadeOut(500, function() {
                                $(this).remove();
                            });
                        }, 5000);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('PFB Simple Field Options Fix: AJAX error:', status, error);
                    console.error('PFB Simple Field Options Fix: Response text:', xhr.responseText);
                    
                    $savingIndicator.text('Error saving form: ' + error).css('background', 'rgba(200,0,0,0.8)');
                    
                    // Remove the indicator after a delay
                    setTimeout(function() {
                        $savingIndicator.fadeOut(500, function() {
                            $(this).remove();
                        });
                    }, 5000);
                },
                complete: function() {
                    // Re-enable save buttons
                    $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');
                }
            });
        } catch (error) {
            console.error('PFB Simple Field Options Fix: Error collecting form data:', error);
            $savingIndicator.text('Error collecting form data: ' + error.message).css('background', 'rgba(200,0,0,0.8)');
            
            // Re-enable save buttons
            $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');
            
            // Remove the indicator after a delay
            setTimeout(function() {
                $savingIndicator.fadeOut(500, function() {
                    $(this).remove();
                });
            }, 5000);
        }
    }
    
    function collectFormData() {
        console.log('PFB Simple Field Options Fix: Collecting form data');
        
        // Get form ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const formId = urlParams.get('form_id');
        
        // Basic form data
        const formData = {
            id: formId || null,
            title: $('#form_title').val() || 'Untitled Form',
            description: $('#form_description').val() || '',
            status: $('#form_status').val() || 'draft',
            settings: {
                template: $('#form_template').val() || 'default',
                show_currency_selector: $('#form_show_currency_selector').val() || '1',
                submit_button_text: $('#form_submit_button_text').val() || 'Calculate Price'
            },
            fields: []
        };
        
        // Collect fields
        $('.pfb-field').each(function(index) {
            const $field = $(this);
            const fieldId = $field.attr('id');
            const fieldType = $field.data('type');
            
            console.log(`PFB Simple Field Options Fix: Processing field #${index + 1}: ${fieldType} (${fieldId})`);
            
            if (!fieldType) {
                console.log(`PFB Simple Field Options Fix: Skipping field #${index + 1}, no type specified`);
                return;
            }
            
            // Basic field data
            const field = {
                id: fieldId,
                type: fieldType,
                label: $field.find('.pfb-field-label-input').val() || `Field ${index + 1}`,
                name: $field.find('.pfb-field-name-input').val() || `field_${index + 1}`,
                required: $field.find('.pfb-field-required-input').is(':checked'),
                width: $field.find('.pfb-field-width-input').val() || '100',
                options: {}
            };
            
            // Special handling for dropdown fields
            if (fieldType === 'dropdown') {
                console.log(`PFB Simple Field Options Fix: Processing dropdown field options`);
                
                field.options.items = [];
                
                $field.find('.pfb-field-option').each(function() {
                    const $option = $(this);
                    const label = $option.find('.pfb-option-label').val();
                    const value = $option.find('.pfb-option-value').val();
                    
                    console.log(`PFB Simple Field Options Fix: Found option - Label: "${label}", Value: "${value}"`);
                    
                    if (label && value) {
                        field.options.items.push({
                            label: label,
                            value: value,
                            variable: $option.find('.pfb-option-variable').val() || ''
                        });
                    }
                });
                
                // Ensure at least one option exists
                if (field.options.items.length === 0) {
                    console.log('PFB Simple Field Options Fix: No options found, adding default option');
                    field.options.items.push({
                        label: 'Option 1',
                        value: 'option_1',
                        variable: ''
                    });
                }
                
                console.log(`PFB Simple Field Options Fix: Dropdown field has ${field.options.items.length} options:`, field.options.items);
            }
            
            // Add field to form data
            formData.fields.push(field);
        });
        
        console.log('PFB Simple Field Options Fix: Complete form data collected:', formData);
        return formData;
    }
    
    function addTestButton() {
        const testButton = `
            <button type="button" id="pfb-test-field-options" class="pfb-btn pfb-btn-secondary" style="margin-left: 10px;">
                <span class="dashicons dashicons-search"></span> Test Field Options
            </button>
        `;
        
        $('#pfb-save-form').after(testButton);
        
        $(document).on('click', '#pfb-test-field-options', function(e) {
            e.preventDefault();
            testFieldOptions();
        });
    }
    
    function testFieldOptions() {
        console.log('=== FIELD OPTIONS TEST ===');
        
        $('.pfb-field').each(function(index) {
            const $field = $(this);
            const fieldType = $field.data('type');
            const fieldId = $field.attr('id');
            
            console.log(`Field ${index + 1}: ${fieldType} (${fieldId})`);
            
            if (fieldType === 'dropdown') {
                const $options = $field.find('.pfb-field-option');
                console.log(`  Found ${$options.length} option elements`);
                
                $options.each(function(optionIndex) {
                    const $option = $(this);
                    const label = $option.find('.pfb-option-label').val();
                    const value = $option.find('.pfb-option-value').val();
                    console.log(`    Option ${optionIndex + 1}: "${label}" = "${value}"`);
                });
            }
        });
        
        console.log('=== TEST COMPLETE ===');
    }
    
})(jQuery);
