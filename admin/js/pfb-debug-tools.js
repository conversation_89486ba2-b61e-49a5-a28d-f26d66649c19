/**
 * PFB Debug Tools
 * A collection of debugging tools for the Price Form Builder
 */
(function($) {
    'use strict';

    // Wait for document ready
    $(document).ready(function() {
        console.log('PFB Debug Tools: Initializing...');
        
        // Wait a moment to ensure all other scripts have loaded
        setTimeout(initDebugTools, 1000);
    });
    
    /**
     * Initialize the debug tools
     */
    function initDebugTools() {
        console.log('PFB Debug Tools: Setting up debug tools');
        
        // Add debug panel
        const $debugPanel = $('<div id="pfb-debug-panel" style="position: fixed; top: 32px; right: 20px; background: #333; color: white; padding: 10px; border-radius: 5px; z-index: 9999; width: 300px; max-height: 500px; overflow-y: auto;">' +
            '<h3 style="margin: 0 0 10px 0; padding: 0; font-size: 16px;">PFB Debug Tools</h3>' +
            '<div id="pfb-debug-content"></div>' +
            '<div style="margin-top: 10px;">' +
                '<button id="pfb-debug-check-db" class="button button-primary" style="margin-right: 5px;">Check DB</button>' +
                '<button id="pfb-debug-load-form" class="button button-primary" style="margin-right: 5px;">Load Form</button>' +
                '<button id="pfb-debug-save-form" class="button button-primary">Save Form</button>' +
            '</div>' +
            '<div style="margin-top: 10px;">' +
                '<button id="pfb-debug-toggle-panel" class="button" style="width: 100%;">Hide Panel</button>' +
            '</div>' +
        '</div>').appendTo('body');
        
        // Add toggle functionality
        $('#pfb-debug-toggle-panel').on('click', function() {
            const $content = $('#pfb-debug-content');
            const $button = $(this);
            
            if ($content.is(':visible')) {
                $content.hide();
                $button.text('Show Panel');
            } else {
                $content.show();
                $button.text('Hide Panel');
            }
        });
        
        // Add check DB functionality
        $('#pfb-debug-check-db').on('click', function() {
            checkDatabase();
        });
        
        // Add load form functionality
        $('#pfb-debug-load-form').on('click', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const formId = urlParams.get('form_id');
            
            if (formId) {
                loadFormDebug(formId);
            } else {
                addDebugMessage('No form ID found in URL');
            }
        });
        
        // Add save form functionality
        $('#pfb-debug-save-form').on('click', function() {
            saveFormDebug();
        });
        
        // Add initial debug message
        addDebugMessage('Debug tools initialized');
        
        // Check if we're on the form editor page with a form ID
        const urlParams = new URLSearchParams(window.location.search);
        const formId = urlParams.get('form_id');
        
        if (formId) {
            addDebugMessage('Form ID detected: ' + formId);
        }
    }
    
    /**
     * Add a debug message to the debug panel
     */
    function addDebugMessage(message) {
        const timestamp = new Date().toLocaleTimeString();
        $('#pfb-debug-content').prepend('<div style="border-bottom: 1px solid #555; padding-bottom: 5px; margin-bottom: 5px;"><span style="color: #aaa;">[' + timestamp + ']</span> ' + message + '</div>');
    }
    
    /**
     * Check the database structure
     */
    function checkDatabase() {
        addDebugMessage('Checking database structure...');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'pfb_check_database',
                nonce: pfb_data.nonce
            },
            success: function(response) {
                console.log('PFB Debug Tools: Database check response:', response);
                
                if (response.success) {
                    addDebugMessage('Database check successful: ' + JSON.stringify(response.data));
                } else {
                    addDebugMessage('Database check failed: ' + (response.data && response.data.message ? response.data.message : 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('PFB Debug Tools: AJAX error checking database:', status, error);
                addDebugMessage('AJAX error checking database: ' + error);
            }
        });
    }
    
    /**
     * Load form with debug information
     */
    function loadFormDebug(formId) {
        addDebugMessage('Loading form with ID: ' + formId);
        
        $.ajax({
            url: ajaxurl,
            type: 'GET',
            data: {
                action: 'pfb_get_form',
                nonce: pfb_data.nonce,
                form_id: formId
            },
            success: function(response) {
                console.log('PFB Debug Tools: Form load response:', response);
                
                if (response.success) {
                    const form = response.data.form;
                    addDebugMessage('Form loaded successfully: ' + form.title);
                    addDebugMessage('Form has ' + (form.fields ? form.fields.length : 0) + ' fields');
                    
                    // Log the first field for debugging
                    if (form.fields && form.fields.length > 0) {
                        const field = form.fields[0];
                        addDebugMessage('First field: ' + field.field_label + ' (' + field.field_type + ')');
                        
                        // Check if field has conditional logic
                        if (field.conditional_logic) {
                            const logic = field.conditional_logic;
                            addDebugMessage('Field has conditional logic: ' + (typeof logic === 'object' ? JSON.stringify(logic) : logic));
                        }
                    }
                } else {
                    addDebugMessage('Form load failed: ' + (response.data && response.data.message ? response.data.message : 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('PFB Debug Tools: AJAX error loading form:', status, error);
                addDebugMessage('AJAX error loading form: ' + error);
                
                // Try to parse the response
                try {
                    const response = JSON.parse(xhr.responseText);
                    addDebugMessage('Response: ' + JSON.stringify(response));
                } catch (e) {
                    addDebugMessage('Response text: ' + xhr.responseText);
                }
            }
        });
    }
    
    /**
     * Save form with debug information
     */
    function saveFormDebug() {
        addDebugMessage('Collecting form data for saving...');
        
        // Collect form data
        const formData = {
            title: $('#form_title').val() || 'Untitled Form',
            description: $('#form_description').val() || '',
            status: $('#form_status').val() || 'publish',
            fields: []
        };
        
        // Get form ID if editing
        const urlParams = new URLSearchParams(window.location.search);
        const formId = urlParams.get('form_id');
        
        if (formId) {
            formData.id = formId;
            addDebugMessage('Updating existing form with ID: ' + formId);
        } else {
            addDebugMessage('Creating new form');
        }
        
        // Get all fields
        $('.pfb-form-fields .pfb-field').each(function(index) {
            const $field = $(this);
            const fieldId = $field.attr('id');
            const fieldType = $field.data('type');
            
            addDebugMessage('Processing field #' + (index + 1) + ': ' + fieldType);
            
            // Skip fields without type
            if (!fieldType) {
                addDebugMessage('Skipping field #' + (index + 1) + ': no type specified');
                return;
            }
            
            // Basic field data
            const field = {
                id: fieldId,
                type: fieldType,
                label: $field.find('.pfb-field-label-input').val() || 'Field ' + (index + 1),
                name: $field.find('.pfb-field-name-input').val() || 'field_' + (index + 1),
                required: $field.find('.pfb-field-required-input').is(':checked'),
                width: $field.find('.pfb-field-width-input').val() || '100',
                options: {}
            };
            
            // Add field to form data
            formData.fields.push(field);
        });
        
        addDebugMessage('Collected ' + formData.fields.length + ' fields');
        
        // Save form
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'pfb_save_form',
                nonce: pfb_data.nonce,
                form_data: formData
            },
            success: function(response) {
                console.log('PFB Debug Tools: Form save response:', response);
                
                if (response.success) {
                    addDebugMessage('Form saved successfully with ID: ' + response.data.form_id);
                } else {
                    addDebugMessage('Form save failed: ' + (response.data && response.data.message ? response.data.message : 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('PFB Debug Tools: AJAX error saving form:', status, error);
                addDebugMessage('AJAX error saving form: ' + error);
                
                // Try to parse the response
                try {
                    const response = JSON.parse(xhr.responseText);
                    addDebugMessage('Response: ' + JSON.stringify(response));
                } catch (e) {
                    addDebugMessage('Response text: ' + xhr.responseText);
                }
            }
        });
    }
    
    // Expose functions to the global object
    window.PFBDebugTools = {
        addDebugMessage: addDebugMessage,
        checkDatabase: checkDatabase,
        loadFormDebug: loadFormDebug,
        saveFormDebug: saveFormDebug
    };
    
})(jQuery);
