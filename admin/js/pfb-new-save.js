/**
 * New form saving functionality
 * This completely replaces the existing save functionality
 */
(function($) {
    'use strict';

    // Wait for document ready
    $(document).ready(function() {
        console.log('PFB New Save: Initializing...');

        // Wait a moment to ensure all other scripts have loaded
        setTimeout(initNewSave, 1000);
    });

    /**
     * Initialize the new save functionality
     */
    function initNewSave() {
        console.log('PFB New Save: Setting up save handlers');

        // Remove ALL existing click handlers from both save buttons
        $('#pfb-save-form, #pfb-save-form-bottom').off('click');

        // Add new save handler to top button
        $('#pfb-save-form').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('PFB New Save: Top save button clicked');

            // Collect form data
            const formData = collectFormData();

            // Save the form
            saveForm(formData);

            return false;
        });

        // Make bottom button trigger top button
        $('#pfb-save-form-bottom').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('PFB New Save: Bottom save button clicked');

            // Trigger top save button
            $('#pfb-save-form').trigger('click');

            return false;
        });

        // Add visual indicator
        $('<div id="pfb-new-save-indicator" style="position: fixed; top: 32px; right: 0; background: #4CAF50; color: white; padding: 5px 10px; z-index: 9999;">New Save System Active</div>')
            .appendTo('body');

        console.log('PFB New Save: Save handlers set up successfully');
    }

    /**
     * Collect form data
     */
    function collectFormData() {
        console.log('PFB New Save: Collecting form data');

        // Basic form data
        const formData = {
            title: $('#form_title').val(),
            description: $('#form_description').val(),
            status: $('#form_status').val(),
            settings: {
                template: $('#form_template').val(),
                show_currency_selector: $('#form_show_currency_selector').is(':checked') ? '1' : '0',
                submit_button_text: $('#form_submit_button_text').val()
            },
            fields: []
        };

        // Get form ID if editing
        const urlParams = new URLSearchParams(window.location.search);
        const formId = urlParams.get('form_id');

        if (formId) {
            formData.id = formId;
        }

        // Get all fields
        $('.pfb-form-fields .pfb-field').each(function(index) {
            const $field = $(this);
            const fieldId = $field.attr('id');
            const fieldType = $field.data('type');

            console.log(`PFB New Save: Processing field #${index + 1}, ID: ${fieldId}, Type: ${fieldType}`);

            // Collect field data
            const field = collectFieldData($field, fieldType, fieldId);

            // Add field to form data
            formData.fields.push(field);
        });

        console.log('PFB New Save: Form data collected:', formData);
        return formData;
    }

    /**
     * Collect data for a specific field
     */
    function collectFieldData($field, fieldType, fieldId) {
        // Basic field data
        const field = {
            id: fieldId,
            type: fieldType,
            label: $field.find('.pfb-field-label-input').val(),
            name: $field.find('.pfb-field-name-input').val(),
            required: $field.find('.pfb-field-required-input').is(':checked'),
            width: $field.find('.pfb-field-width-input').val() || '100',
            options: {}
        };

        // Process hidden field properties for text fields
        if (fieldType === 'text') {
            const isHidden = $field.find('.pfb-field-hidden-input').is(':checked');
            field.hidden = isHidden;

            if (isHidden) {
                const variableValue = $field.find('.pfb-hidden-field-variable').val();
                const defaultValue = $field.find('.pfb-hidden-field-value').val();

                if (variableValue) {
                    field.variable = variableValue;
                }

                if (defaultValue) {
                    field.default_value = defaultValue;
                }

                console.log('PFB New Save: Hidden field data:', {
                    field_id: fieldId,
                    hidden: field.hidden,
                    variable: field.variable,
                    default_value: field.default_value
                });
            }
        }

        // Collect field-specific options
        collectFieldOptions(field, $field, fieldType);

        // Collect conditional logic
        collectConditionalLogic(field, $field);

        return field;
    }

    /**
     * Collect options for a specific field type
     */
    function collectFieldOptions(field, $field, fieldType) {
        console.log(`PFB New Save: Collecting options for field type: ${fieldType}`);

        switch (fieldType) {
            case 'select':
            case 'radio':
            case 'checkbox':
                collectChoiceFieldOptions(field, $field);
                break;

            case 'number':
                collectNumberFieldOptions(field, $field);
                break;

            case 'slider':
                collectSliderFieldOptions(field, $field);
                break;

            case 'price':
            case 'total':
                collectPriceFieldOptions(field, $field, fieldType);
                break;

            case 'subtotal':
                collectSubtotalFieldOptions(field, $field);
                break;
        }

        // Collect common options
        collectCommonFieldOptions(field, $field);
    }

    /**
     * Collect options for choice fields (select, radio, checkbox)
     */
    function collectChoiceFieldOptions(field, $field) {
        const items = [];

        $field.find('.pfb-field-option').each(function() {
            const $option = $(this);
            const label = $option.find('.pfb-option-label').val() || 'Option';
            const value = $option.find('.pfb-option-value').val() || label || '0';
            const variable = $option.find('.pfb-option-variable').val() || '';

            items.push({
                label: label,
                value: value,
                variable: variable
            });
        });

        // Make sure we have at least one option
        if (items.length === 0) {
            items.push({
                label: 'Option 1',
                value: 'option_1',
                variable: ''
            });
        }

        // Set items in field options
        field.options.items = items;

        // Get variable input if it exists
        if ($field.find('.pfb-field-variable-input').length) {
            field.options.variable = $field.find('.pfb-field-variable-input').val() || '';
        }

        console.log(`PFB New Save: Collected ${items.length} options for choice field`);
    }

    /**
     * Collect options for number fields
     */
    function collectNumberFieldOptions(field, $field) {
        field.options.min = $field.find('.pfb-field-min').val() || '0';
        field.options.max = $field.find('.pfb-field-max').val() || '100';
        field.options.step = $field.find('.pfb-field-step').val() || '1';

        console.log('PFB New Save: Collected number field options:', field.options);
    }

    /**
     * Collect options for slider fields
     */
    function collectSliderFieldOptions(field, $field) {
        const min = $field.find('.pfb-slider-min-input').val() || '0';
        const max = $field.find('.pfb-slider-max-input').val() || '100';
        const step = $field.find('.pfb-slider-step-input').val() || '1';
        const defaultValue = $field.find('.pfb-slider-default-input').val() || '50';

        field.options.min = parseInt(min);
        field.options.max = parseInt(max);
        field.options.step = parseInt(step);
        field.options.default = parseInt(defaultValue);

        console.log('PFB New Save: Collected slider field options:', field.options);
    }

    /**
     * Collect options for price/total fields
     */
    function collectPriceFieldOptions(field, $field, fieldType) {
        const formula = $field.find('.pfb-field-formula-input').val();
        field.options.formula = formula || '';

        // For price fields, also get the price value
        if (fieldType === 'price') {
            field.options.price = $field.find('.pfb-field-price').val() || '0';
        }

        console.log(`PFB New Save: Collected ${fieldType} field options:`, field.options);
    }

    /**
     * Collect options for subtotal fields
     */
    function collectSubtotalFieldOptions(field, $field) {
        const lines = [];

        $field.find('.pfb-subtotal-line').each(function() {
            const $line = $(this);
            const label = $line.find('.pfb-subtotal-line-label').val() || $line.find('.pfb-line-label').val() || 'Line';
            const formula = $line.find('.pfb-subtotal-line-formula').val() || $line.find('.pfb-line-formula').val() || '';

            // Only add lines that have either a label or a formula
            if (label || formula) {
                lines.push({
                    label: label,
                    formula: formula
                });
            }
        });

        // Make sure we have at least one line
        if (lines.length === 0) {
            lines.push({
                label: 'Line 1',
                formula: ''
            });
        }

        // Get empty value display
        const emptyValue = $field.find('.pfb-subtotal-empty-value-input').val() || '---';

        field.options.lines = lines;
        field.options.empty_value = emptyValue;

        console.log(`PFB New Save: Collected ${lines.length} lines for subtotal field`);
    }

    /**
     * Collect common options for all field types
     */
    function collectCommonFieldOptions(field, $field) {
        // Placeholder
        if ($field.find('.pfb-field-placeholder').length) {
            field.options.placeholder = $field.find('.pfb-field-placeholder').val();
        }

        // Default value
        if ($field.find('.pfb-field-default').length) {
            field.options.default = $field.find('.pfb-field-default').val();
        }

        // Description
        if ($field.find('.pfb-field-description').length) {
            field.options.description = $field.find('.pfb-field-description').val();
        }
    }

    /**
     * Collect conditional logic data for a field
     */
    function collectConditionalLogic(field, $field) {
        console.log(`PFB New Save: Collecting conditional logic for field: ${field.id}`);

        // Check if conditional logic is enabled
        const $enableCheckbox = $field.find('.pfb-enable-conditional-logic');

        if (!$enableCheckbox.length) {
            console.log('PFB New Save: No conditional logic checkbox found');
            return;
        }

        const isEnabled = $enableCheckbox.is(':checked');
        console.log(`PFB New Save: Conditional logic enabled: ${isEnabled}`);

        // Create conditional logic object
        const conditionalLogic = {
            enabled: isEnabled,
            logic_type: $field.find('.pfb-logic-type').val() || 'all',
            rules: []
        };

        // If enabled, collect rules
        if (isEnabled) {
            $field.find('.pfb-conditional-rule').each(function() {
                const $rule = $(this);
                const ruleField = $rule.find('.pfb-rule-field').val();
                const ruleOperator = $rule.find('.pfb-rule-operator').val();
                const ruleValue = $rule.find('.pfb-rule-value').val();

                // Get the field name from the selected option
                const $selectedOption = $rule.find('.pfb-rule-field option:selected');
                const fieldName = $selectedOption.data('field-name') || '';

                // Only add rule if it has a field and operator
                if (ruleField && ruleOperator) {
                    conditionalLogic.rules.push({
                        field: ruleField,
                        field_name: fieldName,
                        operator: ruleOperator,
                        value: ruleValue || ''
                    });
                }
            });
        }

        // Add conditional logic to field
        field.conditional_logic = conditionalLogic;

        console.log(`PFB New Save: Collected ${conditionalLogic.rules.length} conditional rules`);
    }

    /**
     * Save the form data
     */
    function saveForm(formData) {
        console.log('PFB New Save: Saving form data');

        // Show saving indicator
        const $savingIndicator = $('<div id="pfb-saving-indicator" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 5px; z-index: 10000;">Saving form...</div>')
            .appendTo('body');

        // Disable save buttons
        $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', true).text('Saving...');

        // Validate form data
        if (!formData.title) {
            $savingIndicator.text('Error: Form title is required').css('background', 'rgba(200,0,0,0.8)');

            // Re-enable save buttons after a delay
            setTimeout(function() {
                $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');

                // Remove the indicator after a delay
                setTimeout(function() {
                    $savingIndicator.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 3000);
            }, 1000);

            return;
        }

        // Log the form data for debugging
        console.log('PFB New Save: Form data to save:', JSON.parse(JSON.stringify(formData)));

        // Save via AJAX
        $.ajax({
            url: pfb_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pfb_save_form_new',
                nonce: pfb_data.nonce,
                form_data: formData
            },
            dataType: 'json',
            success: function(response) {
                console.log('PFB New Save: Save response:', response);

                if (response.success) {
                    // Show success message
                    $savingIndicator.text('Form saved successfully!').css('background', 'rgba(0,128,0,0.8)');

                    // Get form ID from URL or response
                    const urlParams = new URLSearchParams(window.location.search);
                    const formId = urlParams.get('form_id');

                    // Redirect to edit page if new form
                    if (!formId && response.data && response.data.form_id) {
                        $savingIndicator.text('Form saved successfully! Redirecting...');
                        setTimeout(function() {
                            window.location.href = 'admin.php?page=pfb-form-editor&form_id=' + response.data.form_id;
                        }, 1000);
                    } else {
                        // Remove the indicator after a delay
                        setTimeout(function() {
                            $savingIndicator.fadeOut(500, function() {
                                $(this).remove();
                            });
                        }, 2000);
                    }
                } else {
                    const errorMessage = response.data && response.data.message
                        ? response.data.message
                        : 'Unknown error';

                    console.error('PFB New Save: Error response:', errorMessage);
                    $savingIndicator.text('Error saving form: ' + errorMessage).css('background', 'rgba(200,0,0,0.8)');

                    // Remove the indicator after a delay
                    setTimeout(function() {
                        $savingIndicator.fadeOut(500, function() {
                            $(this).remove();
                        });
                    }, 5000);
                }
            },
            error: function(xhr, status, error) {
                console.error('PFB New Save: AJAX error:', status, error);
                console.error('PFB New Save: Response text:', xhr.responseText);

                let errorMessage = error;

                // Try to parse the response JSON
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.data && response.data.message) {
                        errorMessage = response.data.message;
                    }
                } catch (e) {
                    // If parsing fails, use the original error message
                }

                $savingIndicator.text('Error saving form: ' + errorMessage).css('background', 'rgba(200,0,0,0.8)');

                // Remove the indicator after a delay
                setTimeout(function() {
                    $savingIndicator.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 5000);
            },
            complete: function() {
                // Re-enable save buttons
                $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');
            }
        });
    }
})(jQuery);
