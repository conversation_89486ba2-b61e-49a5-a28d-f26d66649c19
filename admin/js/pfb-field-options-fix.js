/**
 * Field Options Fix
 * 
 * Fixes field options display and saving issues.
 */
(function($) {
    'use strict';
    
    /**
     * Field Options Handler
     */
    var PFB_FieldOptions = {
        
        /**
         * Initialize field options handling
         */
        init: function() {
            console.log('PFB Field Options: Initializing field options fix');
            
            // Override the form loading to properly handle field options
            if (typeof window.PFBAdmin !== 'undefined') {
                window.PFBAdmin.originalLoadForm = window.PFBAdmin.loadForm;
                window.PFBAdmin.loadForm = PFB_FieldOptions.loadFormWithOptions;
            }
            
            // Override field creation to properly handle options
            if (typeof window.createFieldFromData !== 'undefined') {
                window.originalCreateFieldFromData = window.createFieldFromData;
                window.createFieldFromData = PFB_FieldOptions.createFieldFromDataWithOptions;
            }
        },
        
        /**
         * Load form with proper field options handling
         */
        loadFormWithOptions: function(formId) {
            console.log('PFB Field Options: Loading form with options fix for ID:', formId);
            
            // Show loading indicator
            const $loadingMessage = $('<div id="pfb-loading-indicator" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 5px; z-index: 10000;">Loading form with field options...</div>');
            $('body').append($loadingMessage);
            
            // Clear existing fields
            $('.pfb-form-fields').empty();
            
            $.ajax({
                url: ajaxurl,
                type: 'GET',
                data: {
                    action: 'pfb_get_form',
                    nonce: pfb_data.nonce,
                    form_id: formId
                },
                success: function(response) {
                    console.log('PFB Field Options: Form data received:', response);
                    
                    if (response.success && response.data && response.data.form) {
                        const form = response.data.form;
                        
                        // Set form basic data
                        $('#form_title').val(form.title || '');
                        $('#form_description').val(form.description || '');
                        $('#form_status').val(form.status || 'draft');
                        
                        // Load form settings
                        if (form.settings) {
                            $('#form_template').val(form.settings.template || 'default');
                            $('#form_show_currency_selector').val(form.settings.show_currency_selector || '1');
                            $('#form_submit_button_text').val(form.settings.submit_button_text || 'Calculate Price');
                        }
                        
                        // Load fields with proper options handling
                        if (form.fields && Array.isArray(form.fields) && form.fields.length > 0) {
                            console.log('PFB Field Options: Loading', form.fields.length, 'fields');
                            
                            form.fields.forEach(function(fieldData, index) {
                                console.log('PFB Field Options: Creating field', index + 1, ':', fieldData);
                                
                                const $field = PFB_FieldOptions.createFieldFromDataWithOptions(fieldData);
                                $('.pfb-form-fields').append($field);
                            });
                            
                            $('.pfb-empty-form-message').hide();
                        } else {
                            $('.pfb-form-fields').html('<div class="pfb-empty-form-message">No fields found. Add fields using the buttons above.</div>');
                        }
                    } else {
                        console.error('PFB Field Options: Error loading form:', response);
                        $('.pfb-form-fields').html('<div class="pfb-empty-form-message">Error loading form. Please try again.</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('PFB Field Options: AJAX error:', error);
                    $('.pfb-form-fields').html('<div class="pfb-empty-form-message">Error loading form. Please try again.</div>');
                },
                complete: function() {
                    $loadingMessage.remove();
                }
            });
        },
        
        /**
         * Create field from data with proper options handling
         */
        createFieldFromDataWithOptions: function(fieldData) {
            console.log('PFB Field Options: Creating field from data:', fieldData);
            
            const fieldId = fieldData.id || 'field_' + Date.now();
            const fieldType = fieldData.field_type || fieldData.type;
            const fieldLabel = fieldData.field_label || fieldData.label || 'Field';
            const fieldName = fieldData.field_name || fieldData.name || 'field_' + Date.now();
            const fieldRequired = fieldData.field_required || fieldData.required || false;
            const fieldWidth = fieldData.field_width || fieldData.width || '100';
            
            // Create the basic field structure
            const $field = $(`
                <div id="${fieldId}" class="pfb-field" data-type="${fieldType}">
                    <div class="pfb-field-header">
                        <div class="pfb-field-title">${fieldLabel}</div>
                        <div class="pfb-field-actions">
                            <div class="pfb-field-action pfb-field-move-up" title="Move Up"><span class="dashicons dashicons-arrow-up-alt2"></span></div>
                            <div class="pfb-field-action pfb-field-move-down" title="Move Down"><span class="dashicons dashicons-arrow-down-alt2"></span></div>
                            <div class="pfb-field-action pfb-field-edit" title="Edit"><span class="dashicons dashicons-edit"></span></div>
                            <div class="pfb-field-action pfb-field-duplicate" title="Duplicate"><span class="dashicons dashicons-admin-page"></span></div>
                            <div class="pfb-field-action pfb-field-delete" title="Delete"><span class="dashicons dashicons-trash"></span></div>
                        </div>
                    </div>
                    <div class="pfb-field-preview">
                        ${PFB_FieldOptions.getFieldPreview(fieldType)}
                    </div>
                    <div class="pfb-field-settings">
                        ${PFB_FieldOptions.getFieldSettingsWithOptions(fieldType, fieldData)}
                    </div>
                </div>
            `);
            
            // Set field values
            $field.find('.pfb-field-label-input').val(fieldLabel);
            $field.find('.pfb-field-name-input').val(fieldName);
            $field.find('.pfb-field-required-input').prop('checked', fieldRequired);
            $field.find('.pfb-field-width-input').val(fieldWidth);
            
            // Handle field options
            PFB_FieldOptions.populateFieldOptions($field, fieldData);
            
            // Handle conditional logic
            if (fieldData.conditional_logic) {
                PFB_FieldOptions.populateConditionalLogic($field, fieldData.conditional_logic);
            }
            
            return $field;
        },
        
        /**
         * Get field preview HTML
         */
        getFieldPreview: function(type) {
            switch (type) {
                case 'text':
                    return '<input type="text" class="pfb-form-control" placeholder="Text Input" disabled>';
                case 'number':
                    return '<input type="number" class="pfb-form-control" placeholder="0" disabled>';
                case 'slider':
                    return '<input type="range" class="pfb-slider-control" disabled>';
                case 'dropdown':
                case 'select':
                    return '<select class="pfb-form-control" disabled><option>Option 1</option></select>';
                case 'radio':
                    return '<div class="pfb-radio"><label><input type="radio" disabled> Option 1</label></div>';
                case 'checkbox':
                    return '<div class="pfb-checkbox"><label><input type="checkbox" disabled> Option 1</label></div>';
                case 'total':
                    return '<div class="pfb-total-preview">$0.00</div>';
                default:
                    return '<div>Field Preview</div>';
            }
        },
        
        /**
         * Get field settings HTML with proper options handling
         */
        getFieldSettingsWithOptions: function(type, fieldData) {
            let html = `
                <div class="pfb-form-group">
                    <label>Field Label</label>
                    <input type="text" class="pfb-form-control pfb-field-label-input" value="${fieldData.field_label || fieldData.label || ''}">
                </div>
                <div class="pfb-form-group">
                    <label>Field Name</label>
                    <input type="text" class="pfb-form-control pfb-field-name-input" value="${fieldData.field_name || fieldData.name || ''}">
                </div>
                <div class="pfb-form-group">
                    <label>
                        <input type="checkbox" class="pfb-field-required-input" ${fieldData.field_required || fieldData.required ? 'checked' : ''}> Required Field
                    </label>
                </div>
                <div class="pfb-form-group">
                    <label>Field Width</label>
                    <select class="pfb-form-control pfb-field-width-input">
                        <option value="100">100% (Full Width)</option>
                        <option value="50">50% (Half Width)</option>
                        <option value="33">33% (One Third)</option>
                        <option value="25">25% (Quarter Width)</option>
                    </select>
                </div>
            `;
            
            // Add field-specific settings
            if (type === 'dropdown' || type === 'select' || type === 'radio' || type === 'checkbox') {
                html += `
                    <div class="pfb-form-group">
                        <label>Options</label>
                        <div class="pfb-field-options-header" style="display: flex; margin-bottom: 5px;">
                            <div style="flex: 1; font-weight: bold;">Label</div>
                            <div style="flex: 1; font-weight: bold;">Value</div>
                            <div style="width: 40px;"></div>
                        </div>
                        <div class="pfb-field-options">
                            <!-- Options will be populated here -->
                        </div>
                        <button type="button" class="pfb-add-option pfb-btn pfb-btn-secondary" style="margin-top: 5px;">Add Option</button>
                    </div>
                `;
            }
            
            // Add conditional logic UI
            html += `
                <div class="pfb-form-group pfb-conditional-logic-group">
                    <label>Conditional Logic</label>
                    <div class="pfb-conditional-logic-toggle">
                        <label>
                            <input type="checkbox" class="pfb-enable-conditional-logic"> Enable conditional logic
                        </label>
                        <div class="pfb-conditional-logic-container" style="display: none; margin-top: 10px;">
                            <p>Show this field when:</p>
                            <div class="pfb-conditional-rules">
                                <!-- Rules will be added here -->
                            </div>
                            <div class="pfb-conditional-logic-actions" style="margin-top: 10px;">
                                <button type="button" class="pfb-add-rule pfb-btn pfb-btn-secondary">
                                    <span class="dashicons dashicons-plus"></span> Add Rule
                                </button>
                                <select class="pfb-logic-type">
                                    <option value="all">All conditions must match (AND)</option>
                                    <option value="any">Any condition can match (OR)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Add save/cancel buttons
            html += `
                <div class="pfb-form-group">
                    <button type="button" class="pfb-btn pfb-btn-primary pfb-save-field-settings">Save</button>
                    <button type="button" class="pfb-btn pfb-btn-secondary pfb-cancel-field-settings">Cancel</button>
                </div>
            `;
            
            return html;
        },
        
        /**
         * Populate field options from data
         */
        populateFieldOptions: function($field, fieldData) {
            const fieldType = fieldData.field_type || fieldData.type;
            
            // Handle choice fields (dropdown, radio, checkbox)
            if (fieldType === 'dropdown' || fieldType === 'select' || fieldType === 'radio' || fieldType === 'checkbox') {
                const $optionsContainer = $field.find('.pfb-field-options');
                
                // Get options from field data
                let options = [];
                
                if (fieldData.field_options && typeof fieldData.field_options === 'object') {
                    if (fieldData.field_options.items && Array.isArray(fieldData.field_options.items)) {
                        options = fieldData.field_options.items;
                    }
                } else if (fieldData.options && fieldData.options.items && Array.isArray(fieldData.options.items)) {
                    options = fieldData.options.items;
                }
                
                // If no options found, create default option
                if (options.length === 0) {
                    options = [
                        {
                            label: 'Option 1',
                            value: 'option_1',
                            variable: ''
                        }
                    ];
                }
                
                console.log('PFB Field Options: Populating', options.length, 'options for field:', fieldData);
                
                // Clear existing options
                $optionsContainer.empty();
                
                // Add each option
                options.forEach(function(option, index) {
                    const optionHtml = `
                        <div class="pfb-field-option" style="display: flex; margin-bottom: 5px;">
                            <input type="text" class="pfb-form-control pfb-option-label" placeholder="Label" value="${option.label || ''}" style="flex: 1; margin-right: 5px;">
                            <input type="text" class="pfb-form-control pfb-option-value" placeholder="Value" value="${option.value || ''}" style="flex: 1; margin-right: 5px;">
                            <button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option" style="width: 40px;">-</button>
                        </div>
                    `;
                    $optionsContainer.append(optionHtml);
                });
            }
        },
        
        /**
         * Populate conditional logic from data
         */
        populateConditionalLogic: function($field, conditionalLogic) {
            if (!conditionalLogic || !conditionalLogic.enabled) {
                return;
            }
            
            // Enable conditional logic
            $field.find('.pfb-enable-conditional-logic').prop('checked', true);
            $field.find('.pfb-conditional-logic-container').show();
            
            // Set logic type
            $field.find('.pfb-logic-type').val(conditionalLogic.logic_type || 'all');
            
            // Add rules
            if (conditionalLogic.rules && Array.isArray(conditionalLogic.rules)) {
                const $rulesContainer = $field.find('.pfb-conditional-rules');
                $rulesContainer.empty();
                
                conditionalLogic.rules.forEach(function(rule) {
                    // Add rule using existing function if available
                    if (typeof addRuleRow === 'function') {
                        addRuleRow($field.find('.pfb-conditional-logic-container'), rule);
                    }
                });
            }
        }
    };
    
    // Initialize on document ready
    $(document).ready(function() {
        PFB_FieldOptions.init();
    });
    
})(jQuery);
