/**
 * Core admin functionality for Price Form Builder.
 * This is a simplified version that focuses on the essential functionality.
 */
(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('PFB Admin Core: Initializing...');
        initFormBuilder();
        initTabs();
    });

    /**
     * Initialize tabs functionality
     */
    function initTabs() {
        console.log('PFB Admin Core: Initializing tabs...');

        // Tab click handler
        $('.pfb-tab').on('click', function() {
            const tabId = $(this).data('tab');
            console.log('PFB Admin Core: Tab clicked:', tabId);

            // Remove active class from all tabs and tab contents
            $('.pfb-tab').removeClass('active');
            $('.pfb-tab-content').removeClass('active');

            // Add active class to clicked tab and corresponding tab content
            $(this).addClass('active');
            $('#' + tabId).addClass('active');
        });
    }

    /**
     * Initialize form builder functionality.
     */
    function initFormBuilder() {
        console.log('PFB Admin Core: Initializing form builder...');

        // Create a MutationObserver to watch for changes to the form fields
        const formFieldsObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    // If fields were added or removed, refresh conditional logic dropdowns
                    if (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0) {
                        console.log('PFB Admin Core: Form fields changed, refreshing conditional logic dropdowns');
                        refreshConditionalLogicDropdowns();
                    }
                }
            });
        });

        // Start observing the form fields container
        if ($('.pfb-form-fields').length) {
            formFieldsObserver.observe($('.pfb-form-fields')[0], { childList: true });
        }

        // Add field button click
        $('.pfb-field-type, .pfb-add-field-btn').on('click', function() {
            const fieldType = $(this).data('type');
            console.log('PFB Admin Core: Adding field of type:', fieldType);
            addField(fieldType);
        });

        // Field actions
        $(document).on('click', '.pfb-field-edit', function() {
            const $field = $(this).closest('.pfb-field');
            $field.find('.pfb-field-settings').toggleClass('active');
        });

        $(document).on('click', '.pfb-field-delete', function() {
            if (confirm('Are you sure you want to delete this field?')) {
                $(this).closest('.pfb-field').remove();
            }
        });

        $(document).on('click', '.pfb-field-duplicate', function() {
            const $field = $(this).closest('.pfb-field');
            const fieldType = $field.data('type');
            const $newField = createField(fieldType);
            $field.after($newField);
        });

        $(document).on('click', '.pfb-field-move-up', function() {
            const $field = $(this).closest('.pfb-field');
            const $prev = $field.prev('.pfb-field');
            if ($prev.length) {
                $field.insertBefore($prev);
            }
        });

        $(document).on('click', '.pfb-field-move-down', function() {
            const $field = $(this).closest('.pfb-field');
            const $next = $field.next('.pfb-field');
            if ($next.length) {
                $field.insertAfter($next);
            }
        });

        // Add option button click
        $(document).on('click', '.pfb-add-option', function() {
            const $optionContainer = $(this).closest('.pfb-field-options');
            const optionHtml = `
                <div class="pfb-field-option" style="display: flex; margin-bottom: 5px;">
                    <input type="text" class="pfb-form-control pfb-option-label" placeholder="Label" value="New Option" style="flex: 1; margin-right: 5px;">
                    <input type="text" class="pfb-form-control pfb-option-value" placeholder="Value" value="new_option_${Date.now()}" style="flex: 1; margin-right: 5px;">
                    <button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option" style="width: 40px;">-</button>
                </div>
            `;
            $optionContainer.append(optionHtml);
        });

        // Remove option button click
        $(document).on('click', '.pfb-remove-option', function() {
            $(this).closest('.pfb-field-option').remove();
        });

        // Conditional logic toggle
        $(document).on('change', '.pfb-enable-conditional-logic', function() {
            const $container = $(this).closest('.pfb-conditional-logic-toggle').find('.pfb-conditional-logic-container');

            if ($(this).is(':checked')) {
                $container.slideDown(200);

                // Add default rule if none exists
                if ($container.find('.pfb-conditional-rule').length === 0) {
                    addRuleRow($container);
                }
            } else {
                $container.slideUp(200);
            }
        });

        // Add rule button click
        $(document).on('click', '.pfb-add-rule', function() {
            const $container = $(this).closest('.pfb-conditional-logic-container');
            addRuleRow($container);
        });

        // Remove rule button click
        $(document).on('click', '.pfb-remove-rule', function() {
            $(this).closest('.pfb-conditional-rule').remove();
        });

        // Save field settings
        $(document).on('click', '.pfb-save-field-settings', function() {
            const $field = $(this).closest('.pfb-field');
            const fieldType = $field.data('type');
            const fieldLabel = $field.find('.pfb-field-label-input').val();

            // Update field title
            $field.find('.pfb-field-title').text(fieldLabel);

            // Update field preview based on type
            if (fieldType === 'dropdown' || fieldType === 'radio' || fieldType === 'checkbox') {
                // Get the first option label
                const firstOptionLabel = $field.find('.pfb-option-label').first().val() || 'Option';

                // Update preview
                if (fieldType === 'dropdown') {
                    $field.find('.pfb-field-preview select').html(`<option>${firstOptionLabel}</option>`);
                } else if (fieldType === 'radio') {
                    $field.find('.pfb-field-preview .pfb-radio label').html(`<input type="radio" disabled> ${firstOptionLabel}`);
                } else if (fieldType === 'checkbox') {
                    $field.find('.pfb-field-preview .pfb-checkbox label').html(`<input type="checkbox" disabled> ${firstOptionLabel}`);
                }
            } else if (fieldType === 'text' || fieldType === 'number') {
                // Update placeholder
                $field.find('.pfb-field-preview input').attr('placeholder', fieldLabel);
            }

            // Close settings panel
            $field.find('.pfb-field-settings').removeClass('active');
        });

        // Cancel field settings
        $(document).on('click', '.pfb-cancel-field-settings', function() {
            $(this).closest('.pfb-field-settings').removeClass('active');
        });

        // Save form - commented out to prevent duplicate save events
        // $('#pfb-save-form, #pfb-save-form-bottom').on('click', function() {
        //     saveForm();
        // });

        // DISABLED - Form loading is now handled by pfb-clean-form-handler.js
        // const urlParams = new URLSearchParams(window.location.search);
        // const formId = urlParams.get('form_id');
        // if (formId) {
        //     loadForm(formId);
        // }
    }

    /**
     * Add a new field to the form.
     */
    function addField(type) {
        // Generate a unique ID for the field
        const id = 'field_' + Date.now();
        console.log('PFB Admin Core: Creating field with ID:', id);

        // Create the field element
        const $field = createField(type, id);

        // Add the field to the form
        $('.pfb-form-fields').append($field);

        // Hide empty message if it's visible
        $('.pfb-empty-form-message').hide();

        // Open field settings
        $('#' + id + ' .pfb-field-edit').trigger('click');
    }

    /**
     * Create a new field element.
     */
    function createField(type, id) {
        if (!id) {
            id = 'field_' + Date.now();
        }

        const fieldLabel = type.charAt(0).toUpperCase() + type.slice(1);

        let html = `
            <div id="${id}" class="pfb-field" data-type="${type}">
                <div class="pfb-field-header">
                    <div class="pfb-field-title">${fieldLabel}</div>
                    <div class="pfb-field-actions">
                        <div class="pfb-field-action pfb-field-move-up" title="Move Up"><span class="dashicons dashicons-arrow-up-alt2"></span></div>
                        <div class="pfb-field-action pfb-field-move-down" title="Move Down"><span class="dashicons dashicons-arrow-down-alt2"></span></div>
                        <div class="pfb-field-action pfb-field-edit" title="Edit"><span class="dashicons dashicons-edit"></span></div>
                        <div class="pfb-field-action pfb-field-duplicate" title="Duplicate"><span class="dashicons dashicons-admin-page"></span></div>
                        <div class="pfb-field-action pfb-field-delete" title="Delete"><span class="dashicons dashicons-trash"></span></div>
                    </div>
                </div>
                <div class="pfb-field-preview">
                    ${getFieldPreview(type)}
                </div>
                <div class="pfb-field-settings">
                    ${getFieldSettings(type, id)}
                </div>
            </div>
        `;

        return $(html);
    }

    /**
     * Get field preview HTML.
     */
    function getFieldPreview(type) {
        switch (type) {
            case 'text':
                return '<input type="text" class="pfb-form-control" placeholder="Text Input" disabled>';
            case 'number':
                return '<input type="number" class="pfb-form-control" placeholder="0" disabled>';
            case 'slider':
                return '<input type="range" class="pfb-slider-control" disabled>';
            case 'dropdown':
                return '<select class="pfb-form-control" disabled><option>Option 1</option></select>';
            case 'radio':
                return '<div class="pfb-radio"><label><input type="radio" disabled> Option 1</label></div>';
            case 'checkbox':
                return '<div class="pfb-checkbox"><label><input type="checkbox" disabled> Option 1</label></div>';
            case 'total':
                return '<div class="pfb-total-preview">$0.00</div>';
            default:
                return '<div>Field Preview</div>';
        }
    }

    /**
     * Get field settings HTML.
     */
    function getFieldSettings(type, id) {
        let html = `
            <div class="pfb-form-group">
                <label>Field Label</label>
                <input type="text" class="pfb-form-control pfb-field-label-input" value="${type.charAt(0).toUpperCase() + type.slice(1)}">
            </div>
            <div class="pfb-form-group">
                <label>Field Name</label>
                <input type="text" class="pfb-form-control pfb-field-name-input" value="${type}_${Date.now()}">
            </div>
            <div class="pfb-form-group">
                <label>
                    <input type="checkbox" class="pfb-field-required-input"> Required Field
                </label>
            </div>
            <div class="pfb-form-group">
                <label>Field Width</label>
                <select class="pfb-form-control pfb-field-width-input">
                    <option value="100">100% (Full Width)</option>
                    <option value="50">50% (Half Width)</option>
                    <option value="33">33% (One Third)</option>
                    <option value="25">25% (Quarter Width)</option>
                </select>
            </div>
        `;

        // Add field-specific settings
        if (type === 'dropdown' || type === 'radio' || type === 'checkbox') {
            html += `
                <div class="pfb-form-group">
                    <label>Options</label>
                    <div class="pfb-field-options-header" style="display: flex; margin-bottom: 5px;">
                        <div style="flex: 1; font-weight: bold;">Label</div>
                        <div style="flex: 1; font-weight: bold;">Value</div>
                        <div style="width: 40px;"></div>
                    </div>
                    <div class="pfb-field-options">
                        <div class="pfb-field-option" style="display: flex; margin-bottom: 5px;">
                            <input type="text" class="pfb-form-control pfb-option-label" placeholder="Label" value="Option 1" style="flex: 1; margin-right: 5px;">
                            <input type="text" class="pfb-form-control pfb-option-value" placeholder="Value" value="option_1" style="flex: 1; margin-right: 5px;">
                            <button type="button" class="pfb-btn pfb-btn-secondary pfb-add-option" style="width: 40px;">+</button>
                        </div>
                    </div>
                </div>
            `;
        } else if (type === 'slider') {
            html += `
                <div class="pfb-form-group">
                    <label>Min Value</label>
                    <input type="number" class="pfb-form-control pfb-slider-min-input" value="0">
                </div>
                <div class="pfb-form-group">
                    <label>Max Value</label>
                    <input type="number" class="pfb-form-control pfb-slider-max-input" value="100">
                </div>
                <div class="pfb-form-group">
                    <label>Step</label>
                    <input type="number" class="pfb-form-control pfb-slider-step-input" value="1">
                </div>
                <div class="pfb-form-group">
                    <label>Default Value</label>
                    <input type="number" class="pfb-form-control pfb-slider-default-input" value="50">
                </div>
            `;
        } else if (type === 'total') {
            html += `
                <div class="pfb-form-group">
                    <label>Price Formula</label>
                    <textarea class="pfb-form-control pfb-total-formula-input" rows="3" placeholder="Enter formula, e.g. {field1} * {field2} + 10"></textarea>
                    <div class="pfb-form-help">Use field names in curly braces, e.g. {field_name}</div>
                </div>
                <div class="pfb-form-group">
                    <label>Decimal Places</label>
                    <input type="number" class="pfb-form-control pfb-total-decimals-input" value="2" min="0" max="10">
                </div>
            `;
        }

        // Add conditional logic UI
        html += `
            <div class="pfb-form-group pfb-conditional-logic-group">
                <label>Conditional Logic</label>
                <div class="pfb-conditional-logic-toggle">
                    <label>
                        <input type="checkbox" class="pfb-enable-conditional-logic"> Enable conditional logic
                    </label>
                    <div class="pfb-conditional-logic-container" style="display: none; margin-top: 10px;">
                        <p>Show this field when:</p>
                        <div class="pfb-conditional-rules">
                            <!-- Rules will be added here -->
                        </div>
                        <div class="pfb-conditional-logic-actions" style="margin-top: 10px;">
                            <button type="button" class="pfb-add-rule pfb-btn pfb-btn-secondary">
                                <span class="dashicons dashicons-plus"></span> Add Rule
                            </button>
                            <select class="pfb-logic-type">
                                <option value="all">All conditions must match (AND)</option>
                                <option value="any">Any condition can match (OR)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add save/cancel buttons
        html += `
            <div class="pfb-form-group">
                <button type="button" class="pfb-btn pfb-btn-primary pfb-save-field-settings">Save</button>
                <button type="button" class="pfb-btn pfb-btn-secondary pfb-cancel-field-settings">Cancel</button>
            </div>
        `;

        return html;
    }

    /**
     * Add a rule row to the conditional logic container
     */
    function addRuleRow(container, rule = null) {
        console.log('PFB Admin Core: Adding rule row', rule);

        // Create rule HTML
        const ruleHtml = `
            <div class="pfb-conditional-rule" style="display: flex; margin-bottom: 10px; align-items: center;">
                <select class="pfb-rule-field" style="flex: 1; margin-right: 5px;">
                    <option value="">Select a field</option>
                </select>
                <select class="pfb-rule-operator" style="flex: 1; margin-right: 5px;">
                    <option value="is">is</option>
                    <option value="is_not">is not</option>
                    <option value="greater_than">greater than</option>
                    <option value="less_than">less than</option>
                    <option value="contains">contains</option>
                </select>
                <input type="text" class="pfb-rule-value pfb-form-control" placeholder="Value" style="flex: 1; margin-right: 5px;">
                <button type="button" class="pfb-remove-rule pfb-btn pfb-btn-secondary" style="width: 30px; height: 30px; padding: 0;">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>
            </div>
        `;

        // Add rule to container
        const $rule = $(ruleHtml);
        container.find('.pfb-conditional-rules').append($rule);

        // Store rule data as data attributes if provided
        if (rule) {
            if (rule.field) {
                $rule.attr('data-field-id', rule.field);
            }
            if (rule.field_name) {
                $rule.attr('data-field-name', rule.field_name);
            }
            if (rule.field_label) {
                $rule.attr('data-field-label', rule.field_label);
            }
            if (rule.operator) {
                $rule.attr('data-operator', rule.operator);
            }
            if (rule.value) {
                $rule.attr('data-value', rule.value);
            }
        }

        // Populate field dropdown
        populateFieldDropdown($rule.find('.pfb-rule-field'));

        // Set values if rule is provided
        if (rule) {
            // These will be set by the calling function after a delay
            // to ensure the dropdown is populated
        }

        return $rule;
    }

    /**
     * Refresh all conditional logic dropdowns
     */
    function refreshConditionalLogicDropdowns() {
        console.log('PFB Admin Core: Refreshing all conditional logic dropdowns');

        // Find all rule field dropdowns
        $('.pfb-rule-field').each(function() {
            const $dropdown = $(this);
            const selectedValue = $dropdown.val(); // Save current selection

            // Populate the dropdown
            populateFieldDropdown($dropdown);

            // Restore selection if possible
            if (selectedValue) {
                $dropdown.val(selectedValue);

                // If the selected value is no longer available, select the first option
                if (!$dropdown.val()) {
                    $dropdown.find('option:first').prop('selected', true);
                }
            }
        });
    }

    /**
     * Populate field dropdown with available fields
     */
    function populateFieldDropdown($dropdown) {
        // Clear existing options except the first one
        $dropdown.find('option:not(:first)').remove();

        // Get the current field
        const $currentField = $dropdown.closest('.pfb-field');
        const currentFieldId = $currentField.attr('id');

        console.log('PFB Admin Core: Populating field dropdown for field', currentFieldId);

        // Get all fields
        $('.pfb-field').each(function() {
            const $field = $(this);
            const fieldId = $field.attr('id');
            const fieldType = $field.data('type');
            const fieldName = $field.find('.pfb-field-name-input').val();
            const fieldLabel = $field.find('.pfb-field-label-input').val() || $field.find('.pfb-field-title').text();

            // Skip current field, total fields, and fields without names
            if (fieldId !== currentFieldId && fieldType !== 'total' && fieldName) {
                console.log('PFB Admin Core: Adding field to dropdown:', fieldId, fieldLabel, fieldName);
                $dropdown.append(`<option value="${fieldId}" data-field-name="${fieldName}">${fieldLabel} (${fieldName})</option>`);
            }
        });
    }

    /**
     * Get conditional logic data from UI
     */
    function getConditionalLogicData($field) {
        const $toggle = $field.find('.pfb-conditional-logic-toggle');
        const isEnabled = $toggle.find('.pfb-enable-conditional-logic').is(':checked');

        console.log('PFB Admin Core: Getting conditional logic data for field', $field.attr('id'), 'Enabled:', isEnabled);

        // Return empty object if not enabled
        if (!isEnabled) {
            return {
                enabled: false,
                logic_type: 'all',
                rules: []
            };
        }

        const $container = $toggle.find('.pfb-conditional-logic-container');
        const logicType = $container.find('.pfb-logic-type').val() || 'all';
        const rules = [];

        // Get rules
        $container.find('.pfb-conditional-rule').each(function() {
            const $rule = $(this);
            const field = $rule.find('.pfb-rule-field').val();
            const operator = $rule.find('.pfb-rule-operator').val();
            const value = $rule.find('.pfb-rule-value').val();

            // Get the field name and field label from the selected option
            const $selectedOption = $rule.find('.pfb-rule-field option:selected');
            const fieldName = $selectedOption.data('field-name') || '';
            const fieldLabel = $selectedOption.text() || '';

            if (field && operator) {
                // Store both the field ID and the field name for redundancy
                const ruleData = {
                    field: field,
                    field_name: fieldName,
                    field_label: fieldLabel,
                    operator: operator,
                    value: value || ''
                };

                console.log('PFB Admin Core: Adding rule:', ruleData);
                rules.push(ruleData);
            }
        });

        const result = {
            enabled: true,
            logic_type: logicType,
            rules: rules
        };

        console.log('PFB Admin Core: Final conditional logic data:', result);

        return result;
    }

    /**
     * Set conditional logic data to UI
     */
    function setConditionalLogicData($field, conditionalLogic) {
        if (!conditionalLogic) return;

        console.log('PFB Admin Core: Setting conditional logic data for field', $field.attr('id'), conditionalLogic);

        const $toggle = $field.find('.pfb-conditional-logic-toggle');
        const $enableCheckbox = $toggle.find('.pfb-enable-conditional-logic');
        const $container = $toggle.find('.pfb-conditional-logic-container');

        // Enable conditional logic if needed
        if (conditionalLogic.enabled) {
            $enableCheckbox.prop('checked', true);
            $container.show();

            // Set logic type
            $container.find('.pfb-logic-type').val(conditionalLogic.logic_type || 'all');

            // Clear existing rules
            $container.find('.pfb-conditional-rules').empty();

            // Add rules
            if (conditionalLogic.rules && conditionalLogic.rules.length) {
                console.log('PFB Admin Core: Adding', conditionalLogic.rules.length, 'rules');

                // Create a field map for easier reference
                const fieldMap = {};
                $('.pfb-field').each(function() {
                    const $f = $(this);
                    const fieldId = $f.attr('id');
                    const fieldName = $f.find('.pfb-field-name-input').val();
                    const fieldLabel = $f.find('.pfb-field-label-input').val();

                    if (fieldId && fieldName) {
                        fieldMap[fieldId] = {
                            name: fieldName,
                            label: fieldLabel
                        };

                        // Also map by field name for redundancy
                        fieldMap[fieldName] = {
                            id: fieldId,
                            label: fieldLabel
                        };
                    }
                });

                conditionalLogic.rules.forEach(function(rule) {
                    console.log('PFB Admin Core: Adding rule:', rule);
                    const $rule = addRuleRow($container, rule);

                    // Populate the dropdown immediately
                    populateFieldDropdown($rule.find('.pfb-rule-field'));

                    // Wait a bit for the dropdown to be populated
                    setTimeout(function() {
                        // Try to find the field in the dropdown
                        let fieldFound = false;

                        console.log('PFB Admin Core: Setting field for rule:', rule);

                        // First try by field ID
                        if (rule.field) {
                            $rule.find('.pfb-rule-field').val(rule.field);
                            if ($rule.find('.pfb-rule-field').val() === rule.field) {
                                fieldFound = true;
                                console.log('PFB Admin Core: Found field by ID:', rule.field);
                            }
                        }

                        // If not found by ID, try by field name
                        if (!fieldFound && rule.field_name) {
                            // Look through all fields to find a match by name
                            $('.pfb-field').each(function() {
                                const $field = $(this);
                                const fieldName = $field.find('.pfb-field-name-input').val();
                                const fieldId = $field.attr('id');

                                if (fieldName === rule.field_name) {
                                    $rule.find('.pfb-rule-field').val(fieldId);
                                    if ($rule.find('.pfb-rule-field').val() === fieldId) {
                                        fieldFound = true;
                                        console.log('PFB Admin Core: Found field by name match:', rule.field_name);
                                        return false; // Break the loop
                                    }
                                }
                            });
                        }

                        // If still not found, try to find a field with matching name in the dropdown
                        if (!fieldFound && rule.field_name) {
                            $rule.find('.pfb-rule-field option').each(function() {
                                const $option = $(this);
                                if ($option.data('field-name') === rule.field_name) {
                                    $rule.find('.pfb-rule-field').val($option.val());
                                    fieldFound = true;
                                    console.log('PFB Admin Core: Found field by name in dropdown:', rule.field_name);
                                    return false; // Break the loop
                                }
                            });
                        }

                        // If still not found and we have a field label, try to find by label
                        if (!fieldFound && rule.field_label) {
                            $rule.find('.pfb-rule-field option').each(function() {
                                const $option = $(this);
                                const optionText = $option.text();

                                // Check if the option text contains the field label
                                if (optionText.indexOf(rule.field_label) !== -1) {
                                    $rule.find('.pfb-rule-field').val($option.val());
                                    fieldFound = true;
                                    console.log('PFB Admin Core: Found field by label in dropdown:', rule.field_label);
                                    return false; // Break the loop
                                }
                            });
                        }

                        if (!fieldFound) {
                            console.error('PFB Admin Core: Could not find field for rule:', rule);
                        }

                        // Set the operator value
                        $rule.find('.pfb-rule-operator').val(rule.operator);

                        // Set the value
                        $rule.find('.pfb-rule-value').val(rule.value);
                    }, 100);
                });
            } else {
                // Add default rule
                console.log('PFB Admin Core: Adding default rule');
                addRuleRow($container);
            }
        }
    }

    /**
     * Get form data for saving.
     */
    function getFormData() {
        const formData = {
            id: new URLSearchParams(window.location.search).get('form_id') || '',
            title: $('#form_title').val(),
            description: $('#form_description').val(),
            status: $('#form_status').val(),
            fields: []
        };

        // Get form settings
        formData.settings = {
            template: $('#form_template').val(),
            currency: $('#form_currency').val(),
            show_currency_selector: $('#form_show_currency_selector').val(),
            submit_button_text: $('#form_submit_button_text').val(),
            success_message: $('#form_success_message').val()
        };

        // Get fields
        $('.pfb-field').each(function(index) {
            const $field = $(this);
            const fieldType = $field.data('type');

            const field = {
                type: fieldType,
                label: $field.find('.pfb-field-label-input').val(),
                name: $field.find('.pfb-field-name-input').val(),
                required: $field.find('.pfb-field-required-input').is(':checked') ? 1 : 0,
                width: $field.find('.pfb-field-width-input').val() || '100',
                options: {},
                conditional_logic: getConditionalLogicData($field)
            };

            // Get field-specific options
            if (fieldType === 'dropdown' || fieldType === 'radio' || fieldType === 'checkbox') {
                field.options.items = [];
                $field.find('.pfb-field-option').each(function() {
                    field.options.items.push({
                        label: $(this).find('.pfb-option-label').val() || 'Option',
                        value: $(this).find('.pfb-option-value').val() || 'option'
                    });
                });

                // Make sure we have at least one option
                if (field.options.items.length === 0) {
                    field.options.items.push({
                        label: 'Option 1',
                        value: 'option_1'
                    });
                }
            } else if (fieldType === 'slider') {
                field.options.min = $field.find('.pfb-slider-min-input').val() || 0;
                field.options.max = $field.find('.pfb-slider-max-input').val() || 100;
                field.options.step = $field.find('.pfb-slider-step-input').val() || 1;
                field.options.default = $field.find('.pfb-slider-default-input').val() || 50;
            } else if (fieldType === 'total') {
                field.options.formula = $field.find('.pfb-total-formula-input').val() || '';
                field.options.decimals = $field.find('.pfb-total-decimals-input').val() || 2;
            }

            formData.fields.push(field);
        });

        return formData;
    }

    /**
     * Save form data.
     *
     * NOTE: This function is not used anymore to prevent duplicate save events.
     * The save functionality is now handled by the pfb-admin.js file.
     */
    function saveForm() {
        console.log('PFB Admin Core: saveForm function is deprecated. Use the saveForm function in pfb-admin.js instead.');

        // Get the saveForm function from the main admin script
        if (typeof window.PFBAdmin !== 'undefined' && typeof window.PFBAdmin.saveForm === 'function') {
            console.log('PFB Admin Core: Delegating to PFBAdmin.saveForm');
            window.PFBAdmin.saveForm();
            return;
        }

        // Fallback to the original implementation
        console.warn('PFB Admin Core: PFBAdmin.saveForm not found, using fallback implementation');

        const formData = getFormData();
        console.log('PFB Admin Core: Saving form data:', formData);

        // Disable save button
        $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', true).text('Saving...');

        $.ajax({
            url: pfb_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pfb_save_form',
                nonce: pfb_data.nonce,
                form_data: formData
            },
            success: function(response) {
                console.log('PFB Admin Core: Save response:', response);

                if (response.success) {
                    // Use notification instead of alert
                    if (typeof showNotification === 'function') {
                        showNotification('success', 'Form saved successfully!');
                    } else {
                        alert('Form saved successfully!');
                    }

                    // Redirect to edit page if new form
                    if (!formData.id && response.data.form_id) {
                        window.location.href = 'admin.php?page=pfb-form-editor&form_id=' + response.data.form_id;
                    }
                } else {
                    // Use notification instead of alert
                    if (typeof showNotification === 'function') {
                        showNotification('error', response.data ? response.data.message : 'Unknown error');
                    } else {
                        alert('Error saving form: ' + (response.data ? response.data.message : 'Unknown error'));
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('PFB Admin Core: Save error:', error);

                // Use notification instead of alert
                if (typeof showNotification === 'function') {
                    showNotification('error', 'Error saving form: ' + error);
                } else {
                    alert('Error saving form: ' + error);
                }
            },
            complete: function() {
                $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');
            }
        });
    }

    /**
     * Load form data for editing.
     */
    function loadForm(formId) {
        console.log('PFB Admin Core: Loading form with ID:', formId);

        // Show loading indicator
        $('.pfb-form-fields').html('<div class="pfb-loading-message">Loading form data...</div>');

        $.ajax({
            url: pfb_data.ajax_url,
            type: 'GET',
            data: {
                action: 'pfb_get_form',
                nonce: pfb_data.nonce,
                form_id: formId
            },
            success: function(response) {
                console.log('PFB Admin Core: Load response:', response);

                if (response.success && response.data && response.data.form) {
                    const form = response.data.form;

                    // Set form data
                    $('#form_title').val(form.title || '');
                    $('#form_description').val(form.description || '');
                    $('#form_status').val(form.status || 'draft');

                    // Set form settings
                    if (form.settings) {
                        $('#form_template').val(form.settings.template || 'default');
                        $('#form_currency').val(form.settings.currency || '');
                        $('#form_show_currency_selector').val(form.settings.show_currency_selector || '1');
                        $('#form_submit_button_text').val(form.settings.submit_button_text || 'Calculate Price');
                        $('#form_success_message').val(form.settings.success_message || 'Thank you for your submission!');
                    }

                    // Clear existing fields
                    $('.pfb-form-fields').empty();

                    // Add fields - first pass to create all fields
                    if (form.fields && form.fields.length > 0) {
                        console.log('PFB Admin Core: First pass - creating all fields');
                        form.fields.forEach(function(field) {
                            // Create field with the correct field type
                            const fieldType = field.field_type || 'text';
                            console.log('PFB Admin Core: Creating field of type:', fieldType, field);

                            // Use the field's ID if available, otherwise create a new one
                            const $field = createField(fieldType, field.id);

                            // Set basic field data needed for references
                            $field.find('.pfb-field-title').text(field.field_label || '');
                            $field.find('.pfb-field-label-input').val(field.field_label || '');
                            $field.find('.pfb-field-name-input').val(field.field_name || '');

                            // Add to form
                            $('.pfb-form-fields').append($field);
                        });

                        // Second pass to set all field data including conditional logic
                        console.log('PFB Admin Core: Second pass - setting field data');
                        form.fields.forEach(function(field) {
                            const $field = $('#' + field.id);

                            if (!$field.length) {
                                console.error('PFB Admin Core: Could not find field with ID:', field.id);
                                return;
                            }

                            // Set field data
                            $field.find('.pfb-field-title').text(field.field_label || '');
                            $field.find('.pfb-field-label-input').val(field.field_label || '');
                            $field.find('.pfb-field-name-input').val(field.field_name || '');
                            $field.find('.pfb-field-required-input').prop('checked', field.field_required == 1);
                            $field.find('.pfb-field-width-input').val(field.field_width || '100');

                            // Get the field type
                            const fieldType = field.field_type || 'text';

                            // Handle field options for dropdown, radio, checkbox
                            if ((fieldType === 'dropdown' || fieldType === 'radio' || fieldType === 'checkbox') && field.field_options) {
                                try {
                                    let options = field.field_options;

                                    // If options is a string, try to parse it as JSON
                                    if (typeof options === 'string') {
                                        try {
                                            options = JSON.parse(options);
                                        } catch (e) {
                                            console.error('PFB Admin Core: Failed to parse field options:', e);
                                        }
                                    }

                                    // Clear existing options
                                    $field.find('.pfb-field-options').empty();

                                    // Add options
                                    if (options && options.items && Array.isArray(options.items)) {
                                        options.items.forEach(function(option, index) {
                                            const optionHtml = `
                                                <div class="pfb-field-option" style="display: flex; margin-bottom: 5px;">
                                                    <input type="text" class="pfb-form-control pfb-option-label" placeholder="Label" value="${option.label || ''}" style="flex: 1; margin-right: 5px;">
                                                    <input type="text" class="pfb-form-control pfb-option-value" placeholder="Value" value="${option.value || ''}" style="flex: 1; margin-right: 5px;">
                                                    ${index === 0 ?
                                                        '<button type="button" class="pfb-btn pfb-btn-secondary pfb-add-option" style="width: 40px;">+</button>' :
                                                        '<button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option" style="width: 40px;">-</button>'}
                                                </div>
                                            `;
                                            $field.find('.pfb-field-options').append(optionHtml);
                                        });
                                    } else {
                                        // Add default option if no options found
                                        const defaultOptionHtml = `
                                            <div class="pfb-field-option" style="display: flex; margin-bottom: 5px;">
                                                <input type="text" class="pfb-form-control pfb-option-label" placeholder="Label" value="Option 1" style="flex: 1; margin-right: 5px;">
                                                <input type="text" class="pfb-form-control pfb-option-value" placeholder="Value" value="option_1" style="flex: 1; margin-right: 5px;">
                                                <button type="button" class="pfb-btn pfb-btn-secondary pfb-add-option" style="width: 40px;">+</button>
                                            </div>
                                        `;
                                        $field.find('.pfb-field-options').append(defaultOptionHtml);
                                    }
                                } catch (e) {
                                    console.error('PFB Admin Core: Error setting field options:', e);
                                }
                            }

                            // Handle slider options
                            if (fieldType === 'slider' && field.field_options) {
                                try {
                                    let options = field.field_options;

                                    // If options is a string, try to parse it as JSON
                                    if (typeof options === 'string') {
                                        try {
                                            options = JSON.parse(options);
                                        } catch (e) {
                                            console.error('PFB Admin Core: Failed to parse slider options:', e);
                                        }
                                    }

                                    // Set slider options
                                    $field.find('.pfb-slider-min-input').val(options.min || 0);
                                    $field.find('.pfb-slider-max-input').val(options.max || 100);
                                    $field.find('.pfb-slider-step-input').val(options.step || 1);
                                    $field.find('.pfb-slider-default-input').val(options.default || 50);
                                } catch (e) {
                                    console.error('PFB Admin Core: Error setting slider options:', e);
                                }
                            }

                            // Handle total field options
                            if (fieldType === 'total' && field.field_options) {
                                try {
                                    let options = field.field_options;

                                    // If options is a string, try to parse it as JSON
                                    if (typeof options === 'string') {
                                        try {
                                            options = JSON.parse(options);
                                        } catch (e) {
                                            console.error('PFB Admin Core: Failed to parse total options:', e);
                                        }
                                    }

                                    // Set total options
                                    $field.find('.pfb-total-formula-input').val(options.formula || '');
                                    $field.find('.pfb-total-decimals-input').val(options.decimals || 2);
                                } catch (e) {
                                    console.error('PFB Admin Core: Error setting total options:', e);
                                }
                            }

                                            // Handle conditional logic
                            if (field.conditional_logic) {
                                try {
                                    let conditionalLogic = field.conditional_logic;

                                    // If conditional logic is a string, try to parse it as JSON
                                    if (typeof conditionalLogic === 'string') {
                                        try {
                                            conditionalLogic = JSON.parse(conditionalLogic);
                                            console.log('PFB Admin Core: Successfully parsed conditional logic:', conditionalLogic);
                                        } catch (e) {
                                            console.error('PFB Admin Core: Failed to parse conditional logic:', e);
                                        }
                                    }

                                    // Make sure conditionalLogic has the expected structure
                                    if (!conditionalLogic.hasOwnProperty('enabled')) {
                                        conditionalLogic = {
                                            enabled: conditionalLogic.rules && conditionalLogic.rules.length > 0,
                                            logic_type: conditionalLogic.logic_type || 'all',
                                            rules: conditionalLogic.rules || []
                                        };
                                    }

                                    // Create a field map for easier reference
                                    const fieldMap = {};
                                    $('.pfb-field').each(function() {
                                        const $f = $(this);
                                        const fieldId = $f.attr('id');
                                        const fieldName = $f.find('.pfb-field-name-input').val();
                                        const fieldLabel = $f.find('.pfb-field-label-input').val();

                                        if (fieldId && fieldName) {
                                            fieldMap[fieldId] = {
                                                name: fieldName,
                                                label: fieldLabel
                                            };
                                            console.log('PFB Admin Core: Mapped field ID', fieldId, 'to name', fieldName);
                                        }
                                    });

                                    // Process rules to ensure they have all necessary data
                                    if (conditionalLogic.rules && Array.isArray(conditionalLogic.rules)) {
                                        conditionalLogic.rules.forEach(function(rule, index) {
                                            // If we have the field in our map, add its name and label
                                            if (rule.field && fieldMap[rule.field]) {
                                                conditionalLogic.rules[index].field_name = fieldMap[rule.field].name;
                                                conditionalLogic.rules[index].field_label = fieldMap[rule.field].label;
                                                console.log('PFB Admin Core: Updated rule with field data:', conditionalLogic.rules[index]);
                                            }
                                        });
                                    }

                                    // Set conditional logic
                                    setConditionalLogicData($field, conditionalLogic);
                                } catch (e) {
                                    console.error('PFB Admin Core: Error setting conditional logic:', e);
                                }
                            }

                            // Add to form
                            $('.pfb-form-fields').append($field);
                        });

                        // After all fields are loaded, refresh conditional logic dropdowns
                        setTimeout(function() {
                            console.log('PFB Admin Core: Refreshing all conditional logic dropdowns after form load');
                            refreshConditionalLogicDropdowns();
                        }, 500);
                    } else {
                        $('.pfb-form-fields').html('<div class="pfb-empty-form-message">No fields found. Add fields using the buttons above.</div>');
                    }
                } else {
                    alert('Error loading form: ' + (response.data ? response.data.message : 'Unknown error'));
                    $('.pfb-form-fields').html('<div class="pfb-empty-form-message">Error loading form. Please try again.</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('PFB Admin Core: Load error:', error);
                alert('Error loading form: ' + error);
                $('.pfb-form-fields').html('<div class="pfb-empty-form-message">Error loading form. Please try again.</div>');
            }
        });
    }

    // Expose functions globally for other scripts
    window.createField = createField;
    window.addRuleRow = addRuleRow;

})(jQuery);
