/**
 * Clean Form Handler for Price Form Builder
 * 
 * Simple, clean form save and load without conflicts or diagnostics
 */
(function($) {
    'use strict';

    let isLoading = false;
    let hasLoaded = false;

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('PFB Clean Form Handler: Initializing');
        
        // Prevent multiple loading
        if (hasLoaded) {
            console.log('PFB Clean Form Handler: Already loaded, skipping');
            return;
        }
        
        hasLoaded = true;
        
        // Setup save handlers
        setupSaveHandlers();
        
        // Setup form loading
        setupFormLoading();
    });

    /**
     * Setup save handlers
     */
    function setupSaveHandlers() {
        // Remove any existing handlers
        $('#pfb-save-form, #pfb-save-form-bottom').off('click.pfb-clean');
        
        // Add clean save handler
        $('#pfb-save-form, #pfb-save-form-bottom').on('click.pfb-clean', function(e) {
            e.preventDefault();
            e.stopPropagation();
            saveForm();
            return false;
        });
    }

    /**
     * Setup form loading
     */
    function setupFormLoading() {
        // Check if we need to load a form from URL
        const urlParams = new URLSearchParams(window.location.search);
        const formId = urlParams.get('form_id');
        
        if (formId && !isLoading) {
            console.log('PFB Clean Form Handler: Loading form:', formId);
            loadForm(formId);
        }
    }

    /**
     * Load form
     */
    function loadForm(formId) {
        if (isLoading) {
            console.log('PFB Clean Form Handler: Already loading, skipping');
            return;
        }
        
        isLoading = true;
        
        $.ajax({
            url: ajaxurl,
            type: 'GET',
            data: {
                action: 'pfb_get_form',
                nonce: pfb_data.nonce,
                form_id: formId
            },
            success: function(response) {
                if (response.success && response.data && response.data.form) {
                    const form = response.data.form;
                    
                    // Set form basic data
                    $('#form_title').val(form.title || '');
                    $('#form_description').val(form.description || '');
                    $('#form_status').val(form.status || 'draft');
                    
                    // Load form settings
                    if (form.settings) {
                        $('#form_template').val(form.settings.template || 'default');
                        $('#form_show_currency_selector').val(form.settings.show_currency_selector || '1');
                        $('#form_submit_button_text').val(form.settings.submit_button_text || 'Calculate Price');
                    }
                    
                    // Load fields
                    if (form.fields && Array.isArray(form.fields) && form.fields.length > 0) {
                        loadFields(form.fields);
                    }
                }
                isLoading = false;
            },
            error: function(xhr, status, error) {
                console.error('PFB Clean Form Handler: Load error:', error);
                isLoading = false;
            }
        });
    }

    /**
     * Load fields
     */
    function loadFields(fields) {
        $('.pfb-form-fields').empty();
        
        fields.forEach(function(fieldData, index) {
            const $field = createFieldFromData(fieldData);
            $('.pfb-form-fields').append($field);
        });
        
        $('.pfb-empty-form-message').hide();
    }

    /**
     * Create field from data
     */
    function createFieldFromData(fieldData) {
        const fieldId = fieldData.id || 'field_' + Date.now();
        const fieldType = fieldData.field_type || fieldData.type;
        const fieldLabel = fieldData.field_label || fieldData.label || 'Field';
        
        // Use existing createField function if available
        let $field;
        if (typeof window.createField === 'function') {
            $field = window.createField(fieldType, fieldId);
        } else if (typeof window.PFBAdmin !== 'undefined' && typeof window.PFBAdmin.createField === 'function') {
            $field = window.PFBAdmin.createField(fieldType, fieldId);
        } else {
            // Fallback - create basic field
            $field = $('<div id="' + fieldId + '" class="pfb-field" data-type="' + fieldType + '"><div class="pfb-field-header"><div class="pfb-field-title">' + fieldLabel + '</div></div></div>');
        }
        
        if (!$field || !$field.length) {
            return $('<div>Error creating field</div>');
        }
        
        // Set field values
        $field.find('.pfb-field-label-input').val(fieldLabel);
        $field.find('.pfb-field-name-input').val(fieldData.field_name || fieldData.name || 'field_' + Date.now());
        $field.find('.pfb-field-required-input').prop('checked', fieldData.field_required || fieldData.required || false);
        $field.find('.pfb-field-width-input').val(fieldData.field_width || fieldData.width || '100');
        
        // Handle field options for dropdown fields
        if (fieldType === 'dropdown' && fieldData.field_options) {
            populateFieldOptions($field, fieldData.field_options);
        }
        
        return $field;
    }

    /**
     * Populate field options
     */
    function populateFieldOptions($field, fieldOptions) {
        const $optionsContainer = $field.find('.pfb-field-options');
        
        if ($optionsContainer.length === 0) {
            return;
        }
        
        // Clear existing options
        $optionsContainer.empty();
        
        // Get options from field data
        let options = [];
        
        if (fieldOptions && typeof fieldOptions === 'object') {
            if (fieldOptions.items && Array.isArray(fieldOptions.items)) {
                options = fieldOptions.items;
            }
        }
        
        // If no options found, create default option
        if (options.length === 0) {
            options = [{ label: 'Option 1', value: 'option_1' }];
        }
        
        // Add each option
        options.forEach(function(option) {
            addOptionToField($field, option.label || '', option.value || '');
        });
    }

    /**
     * Add option to field
     */
    function addOptionToField($field, label, value) {
        const $optionsContainer = $field.find('.pfb-field-options');
        
        const optionHTML = `
            <div class="pfb-field-option" style="display: flex; margin-bottom: 5px;">
                <input type="text" class="pfb-form-control pfb-option-label" placeholder="Label" value="${label}" style="flex: 1; margin-right: 5px;">
                <input type="text" class="pfb-form-control pfb-option-value" placeholder="Value" value="${value}" style="flex: 1; margin-right: 5px;">
                <button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option" style="width: 40px;">-</button>
            </div>
        `;
        
        $optionsContainer.append(optionHTML);
    }

    /**
     * Save form
     */
    function saveForm() {
        console.log('PFB Clean Form Handler: Saving form');
        
        // Show saving indicator
        const $savingIndicator = $('<div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 5px; z-index: 10000;">Saving form...</div>');
        $('body').append($savingIndicator);
        
        // Disable save buttons
        $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', true).text('Saving...');
        
        try {
            // Collect form data
            const formData = collectFormData();
            
            // Save via AJAX
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'pfb_save_form_complete',
                    nonce: pfb_data.nonce,
                    form_data: formData
                },
                success: function(response) {
                    if (response.success) {
                        $savingIndicator.text('Form saved successfully!').css('background', 'rgba(0,128,0,0.8)');
                        
                        // Get form ID from URL or response
                        const urlParams = new URLSearchParams(window.location.search);
                        const formId = urlParams.get('form_id');
                        
                        // Redirect to edit page if new form
                        if (!formId && response.data && response.data.form_id) {
                            setTimeout(function() {
                                window.location.href = 'admin.php?page=pfb-form-editor&form_id=' + response.data.form_id;
                            }, 1000);
                        } else {
                            setTimeout(function() {
                                $savingIndicator.fadeOut(500, function() {
                                    $(this).remove();
                                });
                            }, 2000);
                        }
                    } else {
                        const errorMessage = response.data && response.data.message ? response.data.message : 'Unknown error';
                        $savingIndicator.text('Error: ' + errorMessage).css('background', 'rgba(200,0,0,0.8)');
                        setTimeout(function() {
                            $savingIndicator.fadeOut(500, function() {
                                $(this).remove();
                            });
                        }, 5000);
                    }
                },
                error: function(xhr, status, error) {
                    $savingIndicator.text('Error: ' + error).css('background', 'rgba(200,0,0,0.8)');
                    setTimeout(function() {
                        $savingIndicator.fadeOut(500, function() {
                            $(this).remove();
                        });
                    }, 5000);
                },
                complete: function() {
                    // Re-enable save buttons
                    $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');
                }
            });
        } catch (error) {
            console.error('PFB Clean Form Handler: Error:', error);
            $savingIndicator.text('Error: ' + error.message).css('background', 'rgba(200,0,0,0.8)');
            $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');
            setTimeout(function() {
                $savingIndicator.fadeOut(500, function() {
                    $(this).remove();
                });
            }, 5000);
        }
    }

    /**
     * Collect form data
     */
    function collectFormData() {
        // Get form ID from URL
        const urlParams = new URLSearchParams(window.location.search);
        const formId = urlParams.get('form_id');
        
        // Basic form data
        const formData = {
            id: formId || null,
            title: $('#form_title').val() || 'Untitled Form',
            description: $('#form_description').val() || '',
            status: $('#form_status').val() || 'draft',
            settings: {
                template: $('#form_template').val() || 'default',
                show_currency_selector: $('#form_show_currency_selector').val() || '1',
                submit_button_text: $('#form_submit_button_text').val() || 'Calculate Price'
            },
            fields: []
        };
        
        // Collect fields
        $('.pfb-field').each(function(index) {
            const $field = $(this);
            const fieldId = $field.attr('id');
            const fieldType = $field.data('type');
            
            if (!fieldType) {
                return;
            }
            
            // Basic field data
            const field = {
                id: fieldId,
                type: fieldType,
                label: $field.find('.pfb-field-label-input').val() || 'Field ' + (index + 1),
                name: $field.find('.pfb-field-name-input').val() || 'field_' + (index + 1),
                required: $field.find('.pfb-field-required-input').is(':checked'),
                width: $field.find('.pfb-field-width-input').val() || '100',
                options: {}
            };
            
            // Collect field options for dropdown fields
            if (fieldType === 'dropdown') {
                field.options.items = [];
                
                $field.find('.pfb-field-option').each(function() {
                    const $option = $(this);
                    const label = $option.find('.pfb-option-label').val();
                    const value = $option.find('.pfb-option-value').val();
                    
                    if (label && value) {
                        field.options.items.push({
                            label: label,
                            value: value,
                            variable: ''
                        });
                    }
                });
                
                // Ensure at least one option exists
                if (field.options.items.length === 0) {
                    field.options.items.push({
                        label: 'Option 1',
                        value: 'option_1',
                        variable: ''
                    });
                }
            }
            
            formData.fields.push(field);
        });
        
        return formData;
    }

})(jQuery);
