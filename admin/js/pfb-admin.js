/**
 * Admin JavaScript for the Price Form Builder plugin.
 *
 * @since      1.0.0
 */
(function($) {
    'use strict';

    // Create a global PFBAdmin object to expose functions
    window.PFBAdmin = {};

    // Expose key functions globally for emergency fix
    window.getFormData = getFormData;

    /**
     * Initialize the admin functionality.
     */
    function init() {
        initTabs();

        // Add debug logging for initialization
        console.log('PFB Admin: Initializing admin functionality');

        // Initialize form builder with a slight delay to ensure DOM is fully loaded
        setTimeout(function() {
            console.log('PFB Admin: Initializing form builder');
            initFormBuilder();

            // Initialize conditional logic if available
            if (typeof window.PFBConditionalLogic !== 'undefined' && typeof window.PFBConditionalLogic.init === 'function') {
                console.log('PFB Admin: Initializing conditional logic on document ready');
                window.PFBConditionalLogic.init();
            }

            // Check if we're on the form editor page with a form ID
            const urlParams = new URLSearchParams(window.location.search);
            const formId = urlParams.get('form_id');

            if (formId) {
                console.log('PFB Admin: Form ID detected in URL, loading form:', formId);
                loadForm(formId);
            }
        }, 100);

        initPriceVariables();
        initCurrencies();
        initSettings();
    }

    /**
     * Initialize tabs functionality.
     */
    function initTabs() {
        $('.pfb-tab').on('click', function() {
            const tabId = $(this).data('tab');

            // Update active tab
            $('.pfb-tab').removeClass('active');
            $(this).addClass('active');

            // Show active tab content
            $('.pfb-tab-content').removeClass('active');
            $('#' + tabId).addClass('active');
        });
    }

    /**
     * Initialize form builder functionality.
     */
    function initFormBuilder() {
        if (!$('#pfb-form-builder').length) {
            console.log('PFB Admin: Form builder not found, skipping initialization');
            return;
        }

        console.log('PFB Admin: Initializing form builder functionality');

        // Bind conditional logic events
        bindConditionalLogicEvents();

        // Bind conditional formula events
        bindConditionalFormulaEvents();

        // Add field button click
        $(document).off('click', '.pfb-add-field-btn').on('click', '.pfb-add-field-btn', function() {
            const fieldType = $(this).data('type');
            const fieldId = 'field_' + Date.now();

            console.log('PFB Admin: Adding new field of type:', fieldType, 'with ID:', fieldId);
            console.log('PFB Admin: If adding fields does not work, use the "Debug: Add Text Field" button at the top of the page');

            try {
                // Create new field
                const $field = createField(fieldType, fieldId);

                if (!$field || !$field.length) {
                    console.error('PFB Admin: Failed to create field element');
                    return;
                }

                console.log('PFB Admin: Field element created successfully');

                // Add to form fields
                $('.pfb-form-fields').append($field);
                console.log('PFB Admin: Field added to form');

                // Hide empty message if it's visible
                $('.pfb-empty-form-message').hide();

                // Initialize conditional logic for the new field
                if (typeof window.PFBConditionalLogic !== 'undefined' && typeof window.PFBConditionalLogic.initField === 'function') {
                    console.log('PFB Admin: Initializing conditional logic for new field');
                    window.PFBConditionalLogic.initField($field);
                }

                // Open field settings
                $('#' + fieldId + ' .pfb-field-edit').trigger('click');
                console.log('PFB Admin: Field settings opened');

                // Scroll to the new field
                $('html, body').animate({
                    scrollTop: $field.offset().top - 100
                }, 300);
            } catch (error) {
                console.error('PFB Admin: Error adding field:', error);
                alert('Error adding field. Please check the console for details or use the debug buttons.');
            }
        });

        // Field actions
        $(document).on('click', '.pfb-field-edit', function() {
            const $field = $(this).closest('.pfb-field');
            const fieldId = $field.attr('id');

            // Debug log for field edit
            console.log('Editing field with id:', fieldId);

            toggleFieldSettings(fieldId);

            // Debug log for width dropdown after toggling settings
            setTimeout(function() {
                console.log('Width dropdown exists after toggle:', $field.find('.pfb-field-width-input').length > 0);
                console.log('Width dropdown value:', $field.find('.pfb-field-width-input').val());
            }, 100);
        });

        $(document).on('click', '.pfb-field-duplicate', function() {
            const $field = $(this).closest('.pfb-field');
            const fieldType = $field.data('type');
            const newId = 'field_' + Date.now();

            // Create a new field of the same type instead of cloning
            const $newField = createField(fieldType, newId);

            // Copy field settings
            $newField.find('.pfb-field-label-input').val($field.find('.pfb-field-label-input').val());
            $newField.find('.pfb-field-name-input').val($field.find('.pfb-field-name-input').val() + '_copy');
            $newField.find('.pfb-field-required-input').prop('checked', $field.find('.pfb-field-required-input').is(':checked'));

            // Copy field width
            const fieldWidth = $field.attr('data-width') || '100';
            $newField.attr('data-width', fieldWidth);
            $newField.find('.pfb-field-width-input').val(fieldWidth);

            // Copy conditional logic if it exists
            if ($field.data('conditional-logic')) {
                $newField.data('conditional-logic', $field.data('conditional-logic'));

                // Initialize conditional logic UI
                if (typeof window.PFBConditionalLogic !== 'undefined' && typeof window.PFBConditionalLogic.setConditionalLogicData === 'function') {
                    window.PFBConditionalLogic.setConditionalLogicData($newField, $field.data('conditional-logic'));
                }
            }

            // Insert after the current field
            $field.after($newField);
        });

        // Toggle hidden field options when the hidden checkbox is changed
        $(document).on('change', '.pfb-field-hidden-input', function() {
            const $field = $(this).closest('.pfb-field');
            const $hiddenOptions = $field.find('.pfb-hidden-field-options');
            const isChecked = $(this).is(':checked');

            console.log('Hidden checkbox changed:', isChecked);

            if (isChecked) {
                $hiddenOptions.slideDown(200);
            } else {
                $hiddenOptions.slideUp(200);
            }
        });

        // Open variable selector for hidden fields
        $(document).on('click', '.pfb-select-hidden-variable', function() {
            const $button = $(this);
            const $option = $button.closest('.pfb-option-value-container');
            const $valueInput = $option.find('.pfb-hidden-field-value');
            const $variableInput = $option.find('.pfb-hidden-field-variable');

            console.log('Variable selector clicked, inputs:', {
                valueInput: $valueInput.length ? $valueInput.attr('id') : 'not found',
                variableInput: $variableInput.length ? $variableInput.attr('id') : 'not found'
            });

            // Store references to the inputs
            $('#pfb-dynamic-value-modal').data('valueInput', $valueInput);
            $('#pfb-dynamic-value-modal').data('variableInput', $variableInput);

            // Load variables
            loadDynamicValues();

            // Show modal
            $('#pfb-dynamic-value-modal').show();
        });

        // Save field settings
        $(document).on('click', '.pfb-save-field-settings', function() {
            const $field = $(this).closest('.pfb-field');
            const fieldType = $field.data('type');

            // Update field title with the label
            const fieldLabel = $field.find('.pfb-field-label-input').val();
            $field.find('.pfb-field-title').text(fieldLabel);

            // Get field width
            const fieldWidth = $field.find('.pfb-field-width-input').val() || '100';
            console.log('SAVE: Field width set to:', fieldWidth, 'for field:', $field.find('.pfb-field-name-input').val());
            console.log('SAVE: Width dropdown value:', $field.find('.pfb-field-width-input').val());
            console.log('SAVE: Width dropdown exists:', $field.find('.pfb-field-width-input').length > 0);

            // Store the width as a data attribute on the field
            $field.attr('data-width', fieldWidth);

            // Remove any existing width indicator
            $field.find('.pfb-field-width-indicator').remove();

            // Add a visual indicator of the field width
            if (fieldWidth !== '100') {
                $field.find('.pfb-field-title').after(`<span class="pfb-field-width-indicator">Width: ${fieldWidth}%</span>`);
            }

            // For dropdown, radio, and checkbox fields, ensure options are properly set
            if (['dropdown', 'radio', 'checkbox'].includes(fieldType)) {
                // Log options for debugging
                console.log('Field options before saving:', getFieldOptions($field));

                // Update field preview
                updateFieldPreview($field);
            }

            // Close settings panel
            $field.find('.pfb-field-settings').removeClass('active');
        });

        // Cancel field settings
        $(document).on('click', '.pfb-cancel-field-settings', function() {
            const $field = $(this).closest('.pfb-field');
            $field.find('.pfb-field-settings').removeClass('active');
        });

        // Add option
        $(document).on('click', '.pfb-add-option', function() {
            const $option = $(this).closest('.pfb-field-option');
            const $newOption = $option.clone();

            // Clear values
            $newOption.find('input').val('');

            // Change button from add to remove
            $newOption.find('.pfb-add-option')
                .removeClass('pfb-add-option')
                .addClass('pfb-remove-option')
                .text('-');

            $option.after($newOption);
        });

        // Add subtotal line
        $(document).on('click', '.pfb-add-subtotal-line', function() {
            const $line = $(this).closest('.pfb-subtotal-line');
            const $newLine = $line.clone();

            // Clear formula value but keep a default label
            $newLine.find('.pfb-subtotal-line-formula').val('');

            // Update the line label to be the next number
            const currentLabel = $line.find('.pfb-subtotal-line-label').val();
            const labelParts = currentLabel.split(' ');
            const lineNumber = parseInt(labelParts[labelParts.length - 1]) || 1;
            $newLine.find('.pfb-subtotal-line-label').val(labelParts.slice(0, -1).join(' ') + ' ' + (lineNumber + 1));

            // Change button from add to remove
            $newLine.find('.pfb-add-subtotal-line')
                .removeClass('pfb-add-subtotal-line')
                .addClass('pfb-remove-subtotal-line')
                .text('-');

            $line.after($newLine);
        });

        // Remove subtotal line
        $(document).on('click', '.pfb-remove-subtotal-line', function() {
            $(this).closest('.pfb-subtotal-line').remove();
        });

        // Open formula builder for subtotal line
        $(document).on('click', '.pfb-select-subtotal-formula', function() {
            const $button = $(this);
            const $line = $button.closest('.pfb-subtotal-line');
            const $formulaInput = $line.find('.pfb-subtotal-line-formula');
            const $field = $button.closest('.pfb-field');
            const $formulaTools = $field.find('.pfb-formula-tools');

            // Add active class to the current line
            $('.pfb-subtotal-line').removeClass('active-line');
            $line.addClass('active-line');

            // Store reference to the formula input
            $field.data('currentFormulaInput', $formulaInput);

            // Load fields for the formula builder
            loadFormulaFields($field);

            // Toggle formula tools with animation
            if ($formulaTools.is(':visible')) {
                $formulaTools.slideUp(200);
            } else {
                // Position the formula tools below the current line
                const linePosition = $line.position().top;
                const lineHeight = $line.outerHeight();
                const linesContainer = $field.find('.pfb-subtotal-lines');
                const linesScrollTop = linesContainer.scrollTop();

                // Adjust the scroll position to show the formula tools
                linesContainer.animate({
                    scrollTop: linePosition + linesScrollTop - 50
                }, 200);

                $formulaTools.slideDown(200);
            }
        });

        // Open dynamic value modal
        $(document).on('click', '.pfb-select-dynamic-value', function() {
            const $button = $(this);
            const $option = $button.closest('.pfb-field-option');
            const $valueInput = $option.find('.pfb-option-value');
            const $variableInput = $option.find('.pfb-option-variable');

            // Store references to the inputs
            $('#pfb-dynamic-value-modal').data('valueInput', $valueInput);
            $('#pfb-dynamic-value-modal').data('variableInput', $variableInput);

            // Load variables
            loadDynamicValues();

            // Show modal
            $('#pfb-dynamic-value-modal').show();
        });

        // Remove option
        $(document).on('click', '.pfb-remove-option', function() {
            $(this).closest('.pfb-field-option').remove();
        });

        $(document).on('click', '.pfb-field-delete', function() {
            if (confirm(pfb_data.i18n.confirm_delete)) {
                $(this).closest('.pfb-field').remove();

                // Show empty message if no fields left
                if ($('.pfb-form-fields').children().length === 0) {
                    $('.pfb-empty-form-message').show();
                }
            }
        });

        // Move field up
        $(document).on('click', '.pfb-field-move-up', function() {
            const $field = $(this).closest('.pfb-field');
            const $prev = $field.prev('.pfb-field');

            if ($prev.length) {
                $field.insertBefore($prev);
            }
        });

        // Move field down
        $(document).on('click', '.pfb-field-move-down', function() {
            const $field = $(this).closest('.pfb-field');
            const $next = $field.next('.pfb-field');

            if ($next.length) {
                $field.insertAfter($next);
            }
        });

        // Collapse/expand all fields
        $('#pfb-collapse-all-fields').on('click', function() {
            $('.pfb-field-settings.active').removeClass('active');
        });

        $('#pfb-expand-all-fields').on('click', function() {
            $('.pfb-field').each(function() {
                const fieldId = $(this).attr('id');
                $('#' + fieldId + ' .pfb-field-settings').addClass('active');
            });
        });

        // Save form - first remove any existing handlers to prevent duplicates
        $('#pfb-save-form').off('click').on('click', function(e) {
            e.preventDefault(); // Prevent any default action
            console.log('Save button clicked');

            // Trigger event before saving form
            const formData = getFormData();
            $(document).trigger('pfb:before_save_form', [formData]);

            // Call the save function
            saveForm(formData);

            return false; // Prevent event bubbling
        });

        // Load form if editing
        const urlParams = new URLSearchParams(window.location.search);
        const formId = urlParams.get('form_id');

        if (formId) {
            loadForm(formId);
        }
    }

    /**
     * Create a new field element.
     */
    function createField(type, id) {
        try {
            if (!type || !id) {
                console.error('PFB Admin: Invalid parameters for createField. Type:', type, 'ID:', id);
                return null;
            }

            const fieldTypes = pfb_data.field_types || {};
            const fieldLabel = fieldTypes[type] || type.charAt(0).toUpperCase() + type.slice(1);

            // Debug log for field creation
            console.log('PFB Admin: Creating field of type:', type, 'with id:', id, 'and label:', fieldLabel);

            let html = `
                <div id="${id}" class="pfb-field" data-type="${type}" data-field-id="${id}">
                    <div class="pfb-field-header">
                        <div class="pfb-field-title">${fieldLabel}</div>
                        <div class="pfb-field-actions">
                            <div class="pfb-field-action pfb-field-move-up" title="Move Up"><span class="dashicons dashicons-arrow-up-alt2"></span></div>
                            <div class="pfb-field-action pfb-field-move-down" title="Move Down"><span class="dashicons dashicons-arrow-down-alt2"></span></div>
                            <div class="pfb-field-action pfb-field-edit" title="Edit"><span class="dashicons dashicons-edit"></span></div>
                            <div class="pfb-field-action pfb-field-duplicate" title="Duplicate"><span class="dashicons dashicons-admin-page"></span></div>
                            <div class="pfb-field-action pfb-field-delete" title="Delete"><span class="dashicons dashicons-trash"></span></div>
                        </div>
                    </div>
                    <div class="pfb-field-preview">
                        ${getFieldPreview(type)}
                    </div>
                    <div class="pfb-field-settings">
                        ${getFieldSettings(type, id)}
                    </div>
                </div>
            `;

            const $field = $(html);

            if (!$field.length) {
                console.error('PFB Admin: Failed to create jQuery object from HTML');
                return null;
            }

            // Debug log for field settings
            console.log('PFB Admin: Field settings HTML length:', $field.find('.pfb-field-settings').html().length);
            console.log('PFB Admin: Width dropdown exists:', $field.find('.pfb-field-width-input').length > 0);

            // Initialize conditional logic for the new field
            if (typeof window.PFBConditionalLogic !== 'undefined' && typeof window.PFBConditionalLogic.initField === 'function') {
                console.log('PFB Admin: Initializing conditional logic for new field');
                window.PFBConditionalLogic.initField($field);
            }

            return $field;
        } catch (error) {
            console.error('PFB Admin: Error creating field:', error);
            return null;
        }
    }

    /**
     * Get field preview HTML.
     */
    function getFieldPreview(type) {
        switch (type) {
            case 'text':
                return '<input type="text" class="pfb-form-control" placeholder="Text Input" disabled>';

            case 'number':
                return '<input type="number" class="pfb-form-control" placeholder="0" disabled>';

            case 'slider':
                return `
                    <div class="pfb-slider-preview">
                        <input type="range" min="0" max="100" value="50" class="pfb-slider-control" disabled>
                        <div class="pfb-slider-value">50</div>
                    </div>
                `;

            case 'dropdown':
                return `
                    <select class="pfb-form-control" disabled>
                        <option>Option 1</option>
                        <option>Option 2</option>
                        <option>Option 3</option>
                    </select>
                `;

            case 'radio':
                return `
                    <div>
                        <label><input type="radio" name="radio_preview" disabled> Option 1</label><br>
                        <label><input type="radio" name="radio_preview" disabled> Option 2</label><br>
                        <label><input type="radio" name="radio_preview" disabled> Option 3</label>
                    </div>
                `;

            case 'checkbox':
                return `
                    <div>
                        <label><input type="checkbox" disabled> Option 1</label><br>
                        <label><input type="checkbox" disabled> Option 2</label><br>
                        <label><input type="checkbox" disabled> Option 3</label>
                    </div>
                `;

            case 'subtotal':
                return `
                    <div class="pfb-subtotal-preview">
                        <div class="pfb-subtotal-line">
                            <div class="pfb-subtotal-line-label">Line 1</div>
                            <div class="pfb-subtotal-line-value">$0.00</div>
                        </div>
                        <div class="pfb-subtotal-line">
                            <div class="pfb-subtotal-line-label">Line 2</div>
                            <div class="pfb-subtotal-line-value">$0.00</div>
                        </div>
                        <div class="pfb-subtotal-total">
                            <div class="pfb-subtotal-total-label">${pfb_data.i18n.subtotal || 'Subtotal'}</div>
                            <div class="pfb-subtotal-total-value">$0.00</div>
                        </div>
                    </div>
                `;

            case 'total':
                return '<div class="pfb-total-preview">$0.00</div>';

            default:
                return '<div>Field Preview</div>';
        }
    }

    /**
     * Get field settings HTML.
     */
    function getFieldSettings(type, id) {
        let html = `
            <div class="pfb-form-group">
                <label for="${id}_label">${pfb_data.i18n.field_label}</label>
                <input type="text" id="${id}_label" class="pfb-form-control pfb-field-label-input" value="${type.charAt(0).toUpperCase() + type.slice(1)}">
            </div>
            <div class="pfb-form-group">
                <label for="${id}_name">${pfb_data.i18n.field_name}</label>
                <input type="text" id="${id}_name" class="pfb-form-control pfb-field-name-input" value="${type}_${id.replace('field_', '')}">
                <div class="pfb-form-help">${pfb_data.i18n.field_identifier}</div>
            </div>
            <div class="pfb-form-group">
                <label>
                    <input type="checkbox" class="pfb-field-required-input"> ${pfb_data.i18n.required_field}
                </label>
            </div>
            <div class="pfb-form-group pfb-width-setting-group">
                <label for="${id}_width">${pfb_data.i18n.field_width || 'Field Width'}</label>
                <select id="${id}_width" class="pfb-form-control pfb-field-width-input">
                    <option value="100">${pfb_data.i18n.width_100 || '100% (Full Width)'}</option>
                    <option value="50">${pfb_data.i18n.width_50 || '50% (Half Width)'}</option>
                    <option value="33">${pfb_data.i18n.width_33 || '33% (One Third)'}</option>
                    <option value="25">${pfb_data.i18n.width_25 || '25% (Quarter Width)'}</option>
                </select>
                <div class="pfb-form-help">${pfb_data.i18n.field_width_help || 'Select the width of this field to place multiple fields in the same row'}</div>
            </div>
            ${type === 'text' ? `
            <div class="pfb-form-group">
                <label>
                    <input type="checkbox" class="pfb-field-hidden-input"> Make this field hidden
                </label>
                <div class="pfb-form-help">Hidden fields will not be visible to users but their values will be used in calculations</div>
            </div>
            <div class="pfb-form-group pfb-hidden-field-options" style="display: none;">
                <label for="${id}_variable">Price Variable</label>
                <div class="pfb-option-value-container">
                    <input type="text" id="${id}_variable_value" class="pfb-form-control pfb-hidden-field-value" placeholder="Variable or value" value="">
                    <button type="button" class="pfb-dynamic-value-button pfb-select-hidden-variable">
                        <span class="dashicons dashicons-database"></span>
                    </button>
                    <input type="hidden" id="${id}_variable" class="pfb-hidden-field-variable" value="">
                </div>
                <div class="pfb-form-help">Select a price variable or enter a default value for this hidden field</div>
            </div>
            ` : ''}
        `;

        // Add type-specific settings
        switch (type) {
            case 'slider':
                html += `
                    <div class="pfb-form-group">
                        <label for="${id}_min">Minimum Value</label>
                        <input type="number" id="${id}_min" class="pfb-form-control pfb-slider-min-input" value="0">
                    </div>
                    <div class="pfb-form-group">
                        <label for="${id}_max">Maximum Value</label>
                        <input type="number" id="${id}_max" class="pfb-form-control pfb-slider-max-input" value="100">
                    </div>
                    <div class="pfb-form-group">
                        <label for="${id}_step">Step</label>
                        <input type="number" id="${id}_step" class="pfb-form-control pfb-slider-step-input" value="1">
                        <div class="pfb-form-help">The step value determines the size of each increment. For example, a step of 2 will create values like 0, 2, 4, 6...</div>
                    </div>
                    <div class="pfb-form-group">
                        <label for="${id}_default">Default Value</label>
                        <input type="number" id="${id}_default" class="pfb-form-control pfb-slider-default-input" value="50">
                    </div>
                `;
                break;

            case 'subtotal':
                html += `
                    <div class="pfb-form-group">
                        <label>${pfb_data.i18n.subtotal_lines || 'Subtotal Lines'}</label>
                        <div class="pfb-subtotal-lines-header">
                            <div class="pfb-subtotal-line-label-header">${pfb_data.i18n.line_label || 'Line Label'}</div>
                            <div class="pfb-subtotal-line-formula-header">${pfb_data.i18n.formula || 'Formula'}</div>
                            <div class="pfb-subtotal-line-action-header"></div>
                        </div>
                        <div class="pfb-subtotal-lines">
                            <div class="pfb-subtotal-line">
                                <input type="text" class="pfb-form-control pfb-subtotal-line-label" placeholder="${pfb_data.i18n.line_label || 'Line Label'}" value="${pfb_data.i18n.line || 'Line'} 1">
                                <div class="pfb-formula-input-container">
                                    <textarea class="pfb-form-control pfb-subtotal-line-formula" rows="2" placeholder="${pfb_data.i18n.formula || 'Formula'}"></textarea>
                                    <button type="button" class="pfb-select-subtotal-formula">
                                        <span class="dashicons dashicons-editor-code"></span>
                                    </button>
                                </div>
                                <button type="button" class="pfb-btn pfb-add-subtotal-line">+</button>
                            </div>
                        </div>
                        <div class="pfb-form-help">${pfb_data.i18n.subtotal_lines_help || 'Add multiple lines with labels and formulas. The subtotal will be the sum of all lines.'}</div>
                    </div>

                    <div class="pfb-formula-tools" style="display: none;">
                        <div class="pfb-formula-section">
                            <div class="pfb-formula-section-title">${pfb_data.i18n.fields || 'Fields'}</div>
                            <div class="pfb-formula-fields"></div>
                        </div>
                        <div class="pfb-formula-section">
                            <div class="pfb-formula-section-title">${pfb_data.i18n.variables || 'Variables'}</div>
                            <div class="pfb-formula-variables"></div>
                        </div>
                        <div class="pfb-formula-section">
                            <div class="pfb-formula-section-title">${pfb_data.i18n.operators || 'Operators'}</div>
                            <div class="pfb-formula-operators">
                                <button type="button" class="pfb-formula-operator" data-operator="+">${pfb_data.i18n.addition || '+'}</button>
                                <button type="button" class="pfb-formula-operator" data-operator="-">${pfb_data.i18n.subtraction || '-'}</button>
                                <button type="button" class="pfb-formula-operator" data-operator="*">${pfb_data.i18n.multiplication || '*'}</button>
                                <button type="button" class="pfb-formula-operator" data-operator="/">${pfb_data.i18n.division || '/'}</button>
                                <button type="button" class="pfb-formula-operator" data-operator="(">${pfb_data.i18n.parentheses || '('}</button>
                                <button type="button" class="pfb-formula-operator" data-operator=")">${pfb_data.i18n.parentheses || ')'}</button>
                            </div>
                        </div>
                        <div class="pfb-formula-section">
                            <div class="pfb-formula-section-title">${pfb_data.i18n.functions || 'Functions'}</div>
                            <div class="pfb-formula-functions">
                                <button type="button" class="pfb-formula-function" data-function="ceil()">${pfb_data.i18n.ceil || 'Ceil'}</button>
                                <button type="button" class="pfb-formula-function" data-function="floor()">${pfb_data.i18n.floor || 'Floor'}</button>
                                <button type="button" class="pfb-formula-function" data-function="round()">${pfb_data.i18n.round || 'Round'}</button>
                                <button type="button" class="pfb-formula-function" data-function="min()">${pfb_data.i18n.min || 'Min'}</button>
                                <button type="button" class="pfb-formula-function" data-function="max()">${pfb_data.i18n.max || 'Max'}</button>
                            </div>
                        </div>
                        <div class="pfb-formula-section">
                            <div class="pfb-formula-section-title">${pfb_data.i18n.numbers || 'Numbers'}</div>
                            <div class="pfb-formula-numbers">
                                <button type="button" class="pfb-formula-number" data-number="1">1</button>
                                <button type="button" class="pfb-formula-number" data-number="2">2</button>
                                <button type="button" class="pfb-formula-number" data-number="3">3</button>
                                <button type="button" class="pfb-formula-number" data-number="4">4</button>
                                <button type="button" class="pfb-formula-number" data-number="5">5</button>
                                <button type="button" class="pfb-formula-number" data-number="6">6</button>
                                <button type="button" class="pfb-formula-number" data-number="7">7</button>
                                <button type="button" class="pfb-formula-number" data-number="8">8</button>
                                <button type="button" class="pfb-formula-number" data-number="9">9</button>
                                <button type="button" class="pfb-formula-number" data-number="0">0</button>
                                <button type="button" class="pfb-formula-number" data-number=".">.</button>
                                <button type="button" class="pfb-clear-formula">${pfb_data.i18n.clear || 'Clear'}</button>
                            </div>
                        </div>
                    </div>

                    <div class="pfb-form-group">
                        <label for="${id}_empty_value">${pfb_data.i18n.empty_value_display || 'Empty Value Display'}</label>
                        <input type="text" id="${id}_empty_value" class="pfb-subtotal-empty-value-input" value="---">
                        <div class="pfb-form-help">${pfb_data.i18n.empty_value_help || 'Text to display when a line cannot be calculated (e.g., when required fields are not filled).'}</div>
                    </div>
                `;
                break;

            case 'dropdown':
            case 'radio':
            case 'checkbox':
                html += `
                    <div class="pfb-form-group">
                        <label>${pfb_data.i18n.options}</label>
                        <div class="pfb-field-options-header">
                            <div class="pfb-field-option-label-header">${pfb_data.i18n.label}</div>
                            <div class="pfb-field-option-value-header">${pfb_data.i18n.value}</div>
                            <div class="pfb-field-option-action-header"></div>
                        </div>
                        <div class="pfb-field-options">
                            <div class="pfb-field-option">
                                <input type="text" class="pfb-form-control pfb-option-label" placeholder="${pfb_data.i18n.label}" value="${pfb_data.i18n.option} 1">
                                <div class="pfb-option-value-container">
                                    <input type="text" class="pfb-form-control pfb-option-value" placeholder="${pfb_data.i18n.value}" value="10">
                                    <button type="button" class="pfb-dynamic-value-button pfb-select-dynamic-value">
                                        <span class="dashicons dashicons-database"></span>
                                    </button>
                                    <input type="hidden" class="pfb-option-variable" value="">
                                </div>
                                <button type="button" class="pfb-btn pfb-btn-secondary pfb-add-option">+</button>
                            </div>
                            <div class="pfb-field-option">
                                <input type="text" class="pfb-form-control pfb-option-label" placeholder="${pfb_data.i18n.label}" value="${pfb_data.i18n.option} 2">
                                <div class="pfb-option-value-container">
                                    <input type="text" class="pfb-form-control pfb-option-value" placeholder="${pfb_data.i18n.value}" value="20">
                                    <button type="button" class="pfb-dynamic-value-button pfb-select-dynamic-value">
                                        <span class="dashicons dashicons-database"></span>
                                    </button>
                                    <input type="hidden" class="pfb-option-variable" value="">
                                </div>
                                <button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option">-</button>
                            </div>
                            <div class="pfb-field-option">
                                <input type="text" class="pfb-form-control pfb-option-label" placeholder="${pfb_data.i18n.label}" value="${pfb_data.i18n.option} 3">
                                <div class="pfb-option-value-container">
                                    <input type="text" class="pfb-form-control pfb-option-value" placeholder="${pfb_data.i18n.value}" value="30">
                                    <button type="button" class="pfb-dynamic-value-button pfb-select-dynamic-value">
                                        <span class="dashicons dashicons-database"></span>
                                    </button>
                                    <input type="hidden" class="pfb-option-variable" value="">
                                </div>
                                <button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option">-</button>
                            </div>
                        </div>
                        <div class="pfb-form-help">${pfb_data.i18n.option_help}</div>
                    </div>
                `;
                break;

            case 'total':
                html += `
                    <div class="pfb-form-group">
                        <label for="${id}_formula">${pfb_data.i18n.price_formula}</label>
                        <div class="pfb-formula-builder">
                            <div class="pfb-formula-input-container">
                                <textarea id="${id}_formula" class="pfb-form-control pfb-field-formula-input" rows="4"></textarea>
                                <button type="button" class="pfb-btn pfb-btn-secondary pfb-clear-formula">${pfb_data.i18n.clear}</button>
                            </div>
                            <div class="pfb-formula-tools">
                                <div class="pfb-formula-section">
                                    <div class="pfb-formula-section-title">${pfb_data.i18n.form_fields}</div>
                                    <div class="pfb-formula-fields">
                                        <!-- Fields will be loaded dynamically -->
                                        <div class="pfb-formula-empty">${pfb_data.i18n.no_fields}</div>
                                    </div>
                                </div>
                                <div class="pfb-formula-section">
                                    <div class="pfb-formula-section-title">${pfb_data.i18n.price_variables}</div>
                                    <button type="button" class="pfb-btn pfb-btn-secondary pfb-select-formula-variables">
                                        <span class="dashicons dashicons-database"></span> ${pfb_data.i18n.select_variables}
                                    </button>
                                </div>
                                <div class="pfb-formula-section">
                                    <div class="pfb-formula-section-title">${pfb_data.i18n.operators}</div>
                                    <div class="pfb-formula-operators">
                                        <button type="button" class="pfb-formula-operator" data-operator="+">+</button>
                                        <button type="button" class="pfb-formula-operator" data-operator="-">-</button>
                                        <button type="button" class="pfb-formula-operator" data-operator="*">×</button>
                                        <button type="button" class="pfb-formula-operator" data-operator="/">÷</button>
                                        <button type="button" class="pfb-formula-operator" data-operator="(">(</button>
                                        <button type="button" class="pfb-formula-operator" data-operator=")">)</button>
                                    </div>
                                </div>
                                <div class="pfb-formula-section">
                                    <div class="pfb-formula-section-title">${pfb_data.i18n.numbers}</div>
                                    <div class="pfb-formula-numbers">
                                        <button type="button" class="pfb-formula-number" data-number="0">0</button>
                                        <button type="button" class="pfb-formula-number" data-number="1">1</button>
                                        <button type="button" class="pfb-formula-number" data-number="2">2</button>
                                        <button type="button" class="pfb-formula-number" data-number="3">3</button>
                                        <button type="button" class="pfb-formula-number" data-number="4">4</button>
                                        <button type="button" class="pfb-formula-number" data-number="5">5</button>
                                        <button type="button" class="pfb-formula-number" data-number="6">6</button>
                                        <button type="button" class="pfb-formula-number" data-number="7">7</button>
                                        <button type="button" class="pfb-formula-number" data-number="8">8</button>
                                        <button type="button" class="pfb-formula-number" data-number="9">9</button>
                                        <button type="button" class="pfb-formula-number" data-number=".">.</button>
                                    </div>
                                </div>
                                <div class="pfb-formula-section">
                                    <div class="pfb-formula-section-title">${pfb_data.i18n.functions}</div>
                                    <div class="pfb-formula-functions">
                                        <button type="button" class="pfb-formula-function" data-function="round(">${pfb_data.i18n.round}</button>
                                        <button type="button" class="pfb-formula-function" data-function="ceil(">${pfb_data.i18n.ceil}</button>
                                        <button type="button" class="pfb-formula-function" data-function="floor(">${pfb_data.i18n.floor}</button>
                                        <button type="button" class="pfb-formula-function" data-function="min(">${pfb_data.i18n.min}</button>
                                        <button type="button" class="pfb-formula-function" data-function="max(">${pfb_data.i18n.max}</button>
                                        <button type="button" class="pfb-formula-function" data-function="if(">${pfb_data.i18n.if}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="pfb-form-help">
                            ${pfb_data.i18n.formula_help}<br>
                            ${pfb_data.i18n.ceil_help}
                        </div>
                    </div>
                `;
                break;
        }

        // Create conditional logic HTML for non-total fields
        let conditionalLogicHtml = '';
        if (type !== 'total') {
            conditionalLogicHtml = `
                <div class="pfb-form-group pfb-conditional-logic-toggle" id="conditional_logic_${id}">
                    <label>
                        <input type="checkbox" class="pfb-enable-conditional-logic" data-field-id="${id}">
                        ${pfb_data.i18n.conditionalLogic || 'Conditional Logic'}
                    </label>
                    <div class="pfb-conditional-logic-container" style="display: none;">
                        <p>${pfb_data.i18n.showThisFieldWhen || 'Show this field when:'}</p>
                        <div class="pfb-conditional-rules">
                            <div class="pfb-conditional-rule">
                                <select class="pfb-rule-field">
                                    <option value="">${pfb_data.i18n.selectField || 'Select a field'}</option>
                                </select>
                                <select class="pfb-rule-operator">
                                    <option value="is">${pfb_data.i18n.is || 'is'}</option>
                                    <option value="is_not">${pfb_data.i18n.isNot || 'is not'}</option>
                                    <option value="greater_than">${pfb_data.i18n.greaterThan || 'greater than'}</option>
                                    <option value="less_than">${pfb_data.i18n.lessThan || 'less than'}</option>
                                    <option value="contains">${pfb_data.i18n.contains || 'contains'}</option>
                                </select>
                                <input type="text" class="pfb-rule-value" placeholder="${pfb_data.i18n.value || 'Value'}">
                                <button type="button" class="pfb-remove-rule pfb-btn pfb-btn-icon">
                                    <span class="dashicons dashicons-no-alt"></span>
                                </button>
                            </div>
                        </div>
                        <div class="pfb-conditional-logic-actions">
                            <button type="button" class="pfb-add-rule pfb-btn pfb-btn-secondary">
                                <span class="dashicons dashicons-plus"></span> ${pfb_data.i18n.addRule || 'Add Rule'}
                            </button>
                            <select class="pfb-logic-type">
                                <option value="all">${pfb_data.i18n.allConditions || 'Match ALL conditions'}</option>
                                <option value="any">${pfb_data.i18n.anyCondition || 'Match ANY condition'}</option>
                            </select>
                        </div>
                    </div>
                </div>
            `;
        }

        // Create conditional formula HTML for total and subtotal fields
        let conditionalFormulaHtml = '';
        if (type === 'total' || type === 'subtotal') {
            conditionalFormulaHtml = `
                <div class="pfb-form-group pfb-conditional-formula-toggle">
                    <label>
                        <input type="checkbox" class="pfb-enable-conditional-formula" data-field-id="${id}">
                        ${pfb_data.i18n.conditionalFormula}
                    </label>
                    <div class="pfb-conditional-formula-container" style="display: none;">
                        <div class="pfb-conditional-formula">
                            <div class="pfb-conditional-formula-row">
                                <label>${pfb_data.i18n.ifCondition}</label>
                                <div class="pfb-conditional-formula-condition">
                                    <select class="pfb-condition-field">
                                        <option value="">${pfb_data.i18n.selectField}</option>
                                    </select>
                                    <select class="pfb-condition-operator">
                                        <option value="is">${pfb_data.i18n.is}</option>
                                        <option value="is_not">${pfb_data.i18n.isNot}</option>
                                        <option value="greater_than">${pfb_data.i18n.greaterThan}</option>
                                        <option value="less_than">${pfb_data.i18n.lessThan}</option>
                                        <option value="contains">${pfb_data.i18n.contains}</option>
                                    </select>
                                    <input type="text" class="pfb-condition-value" placeholder="${pfb_data.i18n.value}">
                                </div>
                            </div>
                            <div class="pfb-conditional-formula-row">
                                <label>${pfb_data.i18n.thenFormula}</label>
                                <div class="pfb-formula-editor pfb-then-formula">
                                    <textarea class="pfb-formula-input" placeholder="${pfb_data.i18n.formula}"></textarea>
                                    <div class="pfb-formula-builder-toggle">
                                        <button type="button" class="pfb-btn pfb-btn-secondary pfb-open-formula-builder">
                                            <span class="dashicons dashicons-editor-code"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="pfb-conditional-formula-row">
                                <label>${pfb_data.i18n.elseFormula}</label>
                                <div class="pfb-formula-editor pfb-else-formula">
                                    <textarea class="pfb-formula-input" placeholder="${pfb_data.i18n.formula}"></textarea>
                                    <div class="pfb-formula-builder-toggle">
                                        <button type="button" class="pfb-btn pfb-btn-secondary pfb-open-formula-builder">
                                            <span class="dashicons dashicons-editor-code"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Add conditional logic/formula before save button
        console.log('Adding conditional logic HTML:', conditionalLogicHtml);
        console.log('Adding conditional formula HTML:', conditionalFormulaHtml);

        // Add conditional logic directly without wrapper
        html += conditionalLogicHtml;

        html += conditionalFormulaHtml;

        // Add save and cancel buttons
        html += `
            <div class="pfb-form-group">
                <button type="button" class="pfb-btn pfb-btn-primary pfb-save-field-settings">${pfb_data.i18n.save_settings}</button>
                <button type="button" class="pfb-btn pfb-btn-secondary pfb-cancel-field-settings">${pfb_data.i18n.cancel}</button>
            </div>
        `;

        return html;
    }

    /**
     * Toggle field settings panel.
     */
    function toggleFieldSettings(fieldId) {
        const $field = $('#' + fieldId);
        const $settings = $field.find('.pfb-field-settings');

        if ($settings.hasClass('active')) {
            $settings.removeClass('active');
        } else {
            // Close other open settings
            $('.pfb-field-settings.active').removeClass('active');

            // Open this field's settings
            $settings.addClass('active');

            // Debug log for field settings
            console.log('Field settings opened for field:', fieldId);
            console.log('Field settings HTML:', $settings.html());

            // Check if width dropdown exists
            const $widthDropdown = $field.find('.pfb-field-width-input');
            console.log('Width dropdown exists:', $widthDropdown.length > 0);

            // If width dropdown doesn't exist, add it
            if ($widthDropdown.length === 0) {
                console.log('Width dropdown not found, adding it');
                const $widthGroup = $(`
                    <div class="pfb-form-group pfb-width-setting-group">
                        <label for="${fieldId}_width">Field Width</label>
                        <select id="${fieldId}_width" class="pfb-form-control pfb-field-width-input">
                            <option value="100">100% (Full Width)</option>
                            <option value="50">50% (Half Width)</option>
                            <option value="33">33% (One Third)</option>
                            <option value="25">25% (Quarter Width)</option>
                        </select>
                        <div class="pfb-form-help">Select the width of this field to place multiple fields in the same row</div>
                    </div>
                `);

                // Insert after the required field checkbox
                $field.find('.pfb-field-required-input').closest('.pfb-form-group').after($widthGroup);
            }

            // Load price variables if needed
            const fieldType = $field.data('type');
            if (['dropdown', 'radio', 'checkbox'].includes(fieldType)) {
                // Load variables for each option
                $field.find('.pfb-option-variable').each(function() {
                    loadPriceVariables($(this));
                });
            }

            // Load fields for formula builder
            if (fieldType === 'total' || fieldType === 'subtotal') {
                // First, remove any existing event handlers to prevent duplicates
                $field.off('click', '.pfb-formula-field');
                $field.off('click', '.pfb-formula-variable');
                $field.off('click', '.pfb-formula-number');
                $field.off('click', '.pfb-formula-operator');
                $field.off('click', '.pfb-formula-function');
                $field.off('click', '.pfb-clear-formula');
                $field.off('click', '.pfb-select-formula-variables');

                // Load fields for the formula builder
                loadFormulaFields($field);

                // Add event handlers for formula builder elements
                initFormulaBuilder($field);

                // For subtotal fields, hide the formula tools initially
                if (fieldType === 'subtotal') {
                    $field.find('.pfb-formula-tools').hide();
                }

                // Initialize conditional formula field dropdown
                const $conditionField = $field.find('.pfb-condition-field');
                if ($conditionField.length && $conditionField.find('option').length <= 1) {
                    console.log('Populating conditional formula field dropdown for field:', fieldId);

                    // Get all fields
                    $('.pfb-field').each(function() {
                        const otherFieldElement = $(this);
                        const otherFieldId = otherFieldElement.attr('id') || otherFieldElement.data('field-id');
                        const otherFieldType = otherFieldElement.data('type');
                        const otherFieldLabel = otherFieldElement.find('.pfb-field-label-input').val() || otherFieldElement.find('.pfb-field-title').text().trim();

                        // Skip current field and total/subtotal fields
                        if (otherFieldId !== fieldId && otherFieldType !== 'total' && otherFieldType !== 'subtotal') {
                            $conditionField.append(`<option value="${otherFieldId}">${otherFieldLabel}</option>`);
                        }
                    });
                }
            }

            // Initialize conditional logic for all field types except total
            if (fieldType !== 'total') {
                // Check if conditional logic toggle exists, if not add it
                if (!$field.find('.pfb-conditional-logic-toggle').length) {
                    console.log('Adding conditional logic toggle for field:', fieldId);

                    // Use the PFBConditionalLogic.init function to add the toggle if available
                    if (typeof window.PFBConditionalLogic !== 'undefined' && typeof window.PFBConditionalLogic.init === 'function') {
                        console.log('Using PFBConditionalLogic.init to add conditional logic toggle');
                        window.PFBConditionalLogic.init();
                        return; // Exit early since the toggle has been added
                    }

                    // Find the save button to insert before it
                    const $saveButton = $field.find('.pfb-save-field-settings').closest('.pfb-form-group');

                    console.log('Save button found:', $saveButton.length > 0);

                    // Create a highly visible conditional logic toggle
                    const customToggleHtml = `
                        <div class="pfb-form-group pfb-conditional-logic-toggle" style="border: 5px solid red !important; padding: 15px !important; margin: 20px 0 !important; background-color: #ffe0e0 !important;">
                            <label style="font-weight: bold !important; font-size: 16px !important; color: #ff0000 !important; text-transform: uppercase !important;">
                                <input type="checkbox" class="pfb-enable-conditional-logic" data-field-id="${fieldId}" style="width: 20px !important; height: 20px !important; margin-right: 10px !important;">
                                CONDITIONAL LOGIC (ADDED IN TOGGLE)
                            </label>
                            <div class="pfb-conditional-logic-container" style="display: none; margin-top: 15px !important; padding: 15px !important; background-color: #fff !important; border: 1px solid #ccc !important;">
                                <p style="font-weight: bold !important;">Show this field when:</p>
                                <div class="pfb-conditional-rules">
                                    <div class="pfb-conditional-rule" style="margin-bottom: 10px !important; display: flex !important; align-items: center !important;">
                                        <select class="pfb-rule-field" style="flex: 2 !important; margin-right: 5px !important;">
                                            <option value="">Select a field</option>
                                        </select>
                                        <select class="pfb-rule-operator" style="flex: 1 !important; margin-right: 5px !important;">
                                            <option value="is">is</option>
                                            <option value="is_not">is not</option>
                                            <option value="greater_than">greater than</option>
                                            <option value="less_than">less than</option>
                                            <option value="contains">contains</option>
                                        </select>
                                        <input type="text" class="pfb-rule-value" placeholder="Value" style="flex: 1 !important; margin-right: 5px !important;">
                                        <button type="button" class="pfb-remove-rule pfb-btn pfb-btn-icon" style="background-color: #f44336 !important; color: white !important;">
                                            <span class="dashicons dashicons-no-alt"></span>
                                        </button>
                                    </div>
                                </div>
                                <div class="pfb-conditional-logic-actions" style="margin-top: 10px !important; display: flex !important; justify-content: space-between !important;">
                                    <button type="button" class="pfb-add-rule pfb-btn pfb-btn-secondary" style="background-color: #4CAF50 !important; color: white !important;">
                                        <span class="dashicons dashicons-plus"></span> Add Rule
                                    </button>
                                    <select class="pfb-logic-type">
                                        <option value="all">Match ALL conditions</option>
                                        <option value="any">Match ANY condition</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    `;

                    // Insert before save button
                    if ($saveButton.length) {
                        $saveButton.before(customToggleHtml);
                        console.log('Inserted custom conditional logic toggle before save button');
                    } else {
                        // Fallback to appending
                        $field.find('.pfb-field-settings').append(customToggleHtml);
                        console.log('Appended custom conditional logic toggle to field settings');
                    }

                    // For backward compatibility, also create the original toggle
                    const toggleHtml = `
                            <div class="pfb-form-group pfb-conditional-logic-toggle">
                                <label>
                                    <input type="checkbox" class="pfb-enable-conditional-logic" data-field-id="${fieldId}">
                                    ${pfb_data.i18n.conditionalLogic}
                                </label>
                                <div class="pfb-conditional-logic-container" style="display: none;">
                                    <p>${pfb_data.i18n.showThisFieldWhen}</p>
                                    <div class="pfb-conditional-rules">
                                        <div class="pfb-conditional-rule">
                                            <select class="pfb-rule-field">
                                                <option value="">${pfb_data.i18n.selectField}</option>
                                            </select>
                                            <select class="pfb-rule-operator">
                                                <option value="is">${pfb_data.i18n.is}</option>
                                                <option value="is_not">${pfb_data.i18n.isNot}</option>
                                                <option value="greater_than">${pfb_data.i18n.greaterThan}</option>
                                                <option value="less_than">${pfb_data.i18n.lessThan}</option>
                                                <option value="contains">${pfb_data.i18n.contains}</option>
                                            </select>
                                            <input type="text" class="pfb-rule-value" placeholder="${pfb_data.i18n.value}">
                                            <button type="button" class="pfb-remove-rule pfb-btn pfb-btn-icon">
                                                <span class="dashicons dashicons-no-alt"></span>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="pfb-conditional-logic-actions">
                                        <button type="button" class="pfb-add-rule pfb-btn pfb-btn-secondary">
                                            <span class="dashicons dashicons-plus"></span> ${pfb_data.i18n.addRule}
                                        </button>
                                        <select class="pfb-logic-type">
                                            <option value="all">${pfb_data.i18n.allConditions}</option>
                                            <option value="any">${pfb_data.i18n.anyCondition}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        `;

                        // We don't need to insert the original toggle since we already inserted the custom one
                        console.log('Original toggle HTML is available but not inserted');
                    }
                }

                // Populate field dropdown for conditional logic
                const $ruleField = $field.find('.pfb-rule-field');
                if ($ruleField.length && $ruleField.find('option').length <= 1) {
                    console.log('Populating conditional logic field dropdown for field:', fieldId);

                    // Get all fields
                    $('.pfb-field').each(function() {
                        const otherFieldElement = $(this);
                        const otherFieldId = otherFieldElement.attr('id') || otherFieldElement.data('field-id');
                        const otherFieldType = otherFieldElement.data('type');
                        const otherFieldLabel = otherFieldElement.find('.pfb-field-label-input').val() || otherFieldElement.find('.pfb-field-title').text().trim();

                        // Skip current field and total/subtotal fields
                        if (otherFieldId !== fieldId && otherFieldType !== 'total' && otherFieldType !== 'subtotal') {
                            $ruleField.append(`<option value="${otherFieldId}">${otherFieldLabel}</option>`);
                        }
                    });
                }

                // Initialize existing conditional logic if available
                if ($field.data('conditional-logic')) {
                    const conditionalLogic = $field.data('conditional-logic');
                    const $toggle = $field.find('.pfb-conditional-logic-toggle');
                    const $container = $toggle.find('.pfb-conditional-logic-container');

                    // Enable the toggle
                    $toggle.find('.pfb-enable-conditional-logic').prop('checked', true);
                    $container.show();

                    // Set logic type
                    $container.find('.pfb-logic-type').val(conditionalLogic.logic_type || 'all');

                    // Clear existing rules
                    $container.find('.pfb-conditional-rules').empty();

                    // Add rules
                    if (conditionalLogic.rules && conditionalLogic.rules.length) {
                        conditionalLogic.rules.forEach(function(rule) {
                            // Create rule HTML
                            const ruleHtml = `
                                <div class="pfb-conditional-rule">
                                    <select class="pfb-rule-field">
                                        <option value="">${pfb_data.i18n.selectField}</option>
                                    </select>
                                    <select class="pfb-rule-operator">
                                        <option value="is">${pfb_data.i18n.is}</option>
                                        <option value="is_not">${pfb_data.i18n.isNot}</option>
                                        <option value="greater_than">${pfb_data.i18n.greaterThan}</option>
                                        <option value="less_than">${pfb_data.i18n.lessThan}</option>
                                        <option value="contains">${pfb_data.i18n.contains}</option>
                                    </select>
                                    <input type="text" class="pfb-rule-value" placeholder="${pfb_data.i18n.value}">
                                    <button type="button" class="pfb-remove-rule pfb-btn pfb-btn-icon">
                                        <span class="dashicons dashicons-no-alt"></span>
                                    </button>
                                </div>
                            `;

                            // Add rule to container
                            const $rule = $(ruleHtml);
                            $container.find('.pfb-conditional-rules').append($rule);

                            // Populate field dropdown
                            $('.pfb-field').each(function() {
                                const otherFieldElement = $(this);
                                const otherFieldId = otherFieldElement.attr('id') || otherFieldElement.data('field-id');
                                const otherFieldType = otherFieldElement.data('type');
                                const otherFieldLabel = otherFieldElement.find('.pfb-field-label-input').val() || otherFieldElement.find('.pfb-field-title').text().trim();

                                // Skip current field and total/subtotal fields
                                if (otherFieldId !== fieldId && otherFieldType !== 'total' && otherFieldType !== 'subtotal') {
                                    $rule.find('.pfb-rule-field').append(`<option value="${otherFieldId}">${otherFieldLabel}</option>`);
                                }
                            });

                            // Set values
                            $rule.find('.pfb-rule-field').val(rule.field);
                            $rule.find('.pfb-rule-operator').val(rule.operator);
                            $rule.find('.pfb-rule-value').val(rule.value);
                        });
                    } else {
                        // Add default rule
                        const ruleHtml = `
                            <div class="pfb-conditional-rule">
                                <select class="pfb-rule-field">
                                    <option value="">${pfb_data.i18n.selectField}</option>
                                </select>
                                <select class="pfb-rule-operator">
                                    <option value="is">${pfb_data.i18n.is}</option>
                                    <option value="is_not">${pfb_data.i18n.isNot}</option>
                                    <option value="greater_than">${pfb_data.i18n.greaterThan}</option>
                                    <option value="less_than">${pfb_data.i18n.lessThan}</option>
                                    <option value="contains">${pfb_data.i18n.contains}</option>
                                </select>
                                <input type="text" class="pfb-rule-value" placeholder="${pfb_data.i18n.value}">
                                <button type="button" class="pfb-remove-rule pfb-btn pfb-btn-icon">
                                    <span class="dashicons dashicons-no-alt"></span>
                                </button>
                            </div>
                        `;

                        // Add rule to container
                        $container.find('.pfb-conditional-rules').append(ruleHtml);
                    }
                }
            }
        }
    }

    /**
     * Load price variables into a select element.
     */
    function loadPriceVariables($select) {
        $.ajax({
            url: pfb_data.ajax_url,
            type: 'GET',
            data: {
                action: 'pfb_get_price_variables',
                nonce: pfb_data.nonce
            },
            success: function(response) {
                if (response.success && response.data.variables) {
                    const variables = response.data.variables;
                    const categories = response.data.categories;

                    // Clear existing options except the first one
                    $select.find('option:not(:first)').remove();

                    // Group variables by category
                    const groupedVariables = {};

                    categories.forEach(function(category) {
                        groupedVariables[category.id] = {
                            name: category.name,
                            variables: []
                        };
                    });

                    variables.forEach(function(variable) {
                        if (groupedVariables[variable.category_id]) {
                            groupedVariables[variable.category_id].variables.push(variable);
                        }
                    });

                    // Add variables to select
                    for (const categoryId in groupedVariables) {
                        const category = groupedVariables[categoryId];

                        if (category.variables.length > 0) {
                            const $optgroup = $('<optgroup label="' + category.name + '">');

                            category.variables.forEach(function(variable) {
                                $optgroup.append('<option value="' + variable.variable_key + '">' + variable.name + '</option>');
                            });

                            $select.append($optgroup);
                        }
                    }
                }
            }
        });
    }

    /**
     * Load form fields for the formula builder.
     */
    function loadFormulaFields($field) {
        const $formulaFields = $field.find('.pfb-formula-fields');

        // Clear existing fields
        $formulaFields.empty();

        // Get all form fields except the current one
        const fields = [];
        $('.pfb-form-fields .pfb-field').each(function() {
            const fieldId = $(this).attr('id');

            // Skip the current field (total field)
            if (fieldId === $field.attr('id')) {
                return;
            }

            const fieldName = $(this).find('.pfb-field-name-input').val();
            const fieldLabel = $(this).find('.pfb-field-label-input').val();

            if (fieldName) {
                fields.push({
                    id: fieldId,
                    name: fieldName,
                    label: fieldLabel || fieldName
                });
            }
        });

        // Add fields to the formula builder
        if (fields.length > 0) {
            fields.forEach(function(field) {
                const $button = $('<button type="button" class="pfb-formula-field" data-field="' + field.name + '">' + field.label + '</button>');
                $formulaFields.append($button);
            });
        } else {
            $formulaFields.html('<div class="pfb-formula-empty">No fields available yet</div>');
        }

        // Also load price variables
        loadFormulaVariables($field);
    }

    /**
     * Bind events for conditional logic.
     */
    function bindConditionalLogicEvents() {
        // Toggle conditional logic container
        $(document).off('change', '.pfb-enable-conditional-logic').on('change', '.pfb-enable-conditional-logic', function() {
            const container = $(this).closest('.pfb-conditional-logic-toggle').find('.pfb-conditional-logic-container');

            if ($(this).is(':checked')) {
                container.slideDown();

                // Add default rule if none exists
                if (container.find('.pfb-conditional-rule').length === 0) {
                    addRuleRow(container);
                }
            } else {
                container.slideUp();
            }
        });

        // Add rule
        $(document).off('click', '.pfb-add-rule').on('click', '.pfb-add-rule', function() {
            const container = $(this).closest('.pfb-conditional-logic-container');
            addRuleRow(container);
        });

        // Remove rule
        $(document).off('click', '.pfb-remove-rule').on('click', '.pfb-remove-rule', function() {
            const rulesContainer = $(this).closest('.pfb-conditional-rules');
            $(this).closest('.pfb-conditional-rule').remove();

            // Add default rule if all rules are removed
            if (rulesContainer.find('.pfb-conditional-rule').length === 0) {
                addRuleRow(rulesContainer.closest('.pfb-conditional-logic-container'));
            }
        });
    }

    /**
     * Add a new rule row for conditional logic.
     */
    function addRuleRow(container) {
        const fieldElement = container.closest('.pfb-field');
        const fieldId = fieldElement.attr('id') || fieldElement.data('field-id');

        console.log('Adding rule row for field:', fieldId);

        // Create rule HTML
        const ruleHtml = `
            <div class="pfb-conditional-rule">
                <select class="pfb-rule-field">
                    <option value="">${pfb_data.i18n.selectField}</option>
                </select>
                <select class="pfb-rule-operator">
                    <option value="is">${pfb_data.i18n.is}</option>
                    <option value="is_not">${pfb_data.i18n.isNot}</option>
                    <option value="greater_than">${pfb_data.i18n.greaterThan}</option>
                    <option value="less_than">${pfb_data.i18n.lessThan}</option>
                    <option value="contains">${pfb_data.i18n.contains}</option>
                </select>
                <input type="text" class="pfb-rule-value" placeholder="${pfb_data.i18n.value}">
                <button type="button" class="pfb-remove-rule pfb-btn pfb-btn-icon">
                    <span class="dashicons dashicons-no-alt"></span>
                </button>
            </div>
        `;

        // Add rule to container
        const ruleElement = $(ruleHtml);
        container.find('.pfb-conditional-rules').append(ruleElement);

        // Populate field dropdown
        const $ruleField = ruleElement.find('.pfb-rule-field');

        // Get all fields
        $('.pfb-field').each(function() {
            const otherFieldElement = $(this);
            const otherFieldId = otherFieldElement.attr('id') || otherFieldElement.data('field-id');
            const otherFieldType = otherFieldElement.data('type');
            const otherFieldLabel = otherFieldElement.find('.pfb-field-label-input').val() || otherFieldElement.find('.pfb-field-title').text().trim();

            // Skip current field and total/subtotal fields
            if (otherFieldId !== fieldId && otherFieldType !== 'total' && otherFieldType !== 'subtotal') {
                $ruleField.append(`<option value="${otherFieldId}">${otherFieldLabel}</option>`);
            }
        });
    }

    /**
     * Bind events for conditional formula.
     */
    function bindConditionalFormulaEvents() {
        // Toggle conditional formula container
        $(document).off('change', '.pfb-enable-conditional-formula').on('change', '.pfb-enable-conditional-formula', function() {
            const container = $(this).closest('.pfb-conditional-formula-toggle').find('.pfb-conditional-formula-container');

            if ($(this).is(':checked')) {
                container.slideDown();
            } else {
                container.slideUp();
            }
        });

        // Open formula builder
        $(document).off('click', '.pfb-open-formula-builder').on('click', '.pfb-open-formula-builder', function() {
            const formulaInput = $(this).closest('.pfb-formula-editor').find('.pfb-formula-input');
            const fieldElement = $(this).closest('.pfb-field');

            // Store reference to the formula input
            fieldElement.data('currentFormulaInput', formulaInput);

            // Load fields for the formula builder
            loadFormulaFields(fieldElement);

            // Show formula tools
            fieldElement.find('.pfb-formula-tools').slideDown(200);
        });
    }

    /**
     * Load price variables for the formula builder.
     */
    function loadFormulaVariables($field) {
        const $formulaVariables = $field.find('.pfb-formula-variables');

        // Show loading message
        $formulaVariables.html('<div class="pfb-formula-empty">Loading variables...</div>');

        // Load variables via AJAX
        $.ajax({
            url: pfb_data.ajax_url,
            type: 'GET',
            data: {
                action: 'pfb_get_price_variables',
                nonce: pfb_data.nonce
            },
            success: function(response) {
                if (response.success && response.data.variables) {
                    $formulaVariables.empty();

                    let hasVariables = false;

                    // Group variables by category
                    const categories = {};

                    response.data.variables.forEach(function(variable) {
                        if (!categories[variable.category_id]) {
                            categories[variable.category_id] = {
                                name: variable.category_name,
                                variables: []
                            };
                        }

                        categories[variable.category_id].variables.push(variable);
                        hasVariables = true;
                    });

                    // Add variables to the formula builder
                    if (hasVariables) {
                        Object.values(categories).forEach(function(category) {
                            category.variables.forEach(function(variable) {
                                const $button = $('<button type="button" class="pfb-formula-variable" data-variable="' + variable.variable_key + '">' + variable.name + '</button>');
                                $formulaVariables.append($button);
                            });
                        });
                    } else {
                        $formulaVariables.html('<div class="pfb-formula-empty">No variables available</div>');
                    }
                } else {
                    $formulaVariables.html('<div class="pfb-formula-empty">Failed to load variables</div>');
                }
            },
            error: function() {
                $formulaVariables.html('<div class="pfb-formula-empty">Failed to load variables</div>');
            }
        });
    }

    /**
     * Load dynamic values for the modal.
     */
    function loadDynamicValues() {
        const $categories = $('.pfb-dynamic-value-categories');

        // Show loading message
        $categories.html('<div class="pfb-dynamic-value-loading">Loading variables...</div>');

        // Load variables via AJAX
        $.ajax({
            url: pfb_data.ajax_url,
            type: 'GET',
            data: {
                action: 'pfb_get_price_variables',
                nonce: pfb_data.nonce
            },
            success: function(response) {
                if (response.success && response.data.variables && response.data.categories) {
                    const variables = response.data.variables;
                    const categories = response.data.categories;

                    // Clear loading message
                    $categories.empty();

                    // Group variables by category
                    const groupedVariables = {};

                    categories.forEach(function(category) {
                        groupedVariables[category.id] = {
                            name: category.name,
                            variables: []
                        };
                    });

                    variables.forEach(function(variable) {
                        if (groupedVariables[variable.category_id]) {
                            groupedVariables[variable.category_id].variables.push(variable);
                        }
                    });

                    // Add categories and variables to the modal
                    let hasVariables = false;

                    for (const categoryId in groupedVariables) {
                        const category = groupedVariables[categoryId];

                        if (category.variables.length > 0) {
                            hasVariables = true;

                            const $category = $('<div class="pfb-dynamic-value-category">');
                            $category.append('<div class="pfb-dynamic-value-category-header">' + category.name + '</div>');

                            const $list = $('<div class="pfb-dynamic-value-list">');

                            category.variables.forEach(function(variable) {
                                const $item = $('<div class="pfb-dynamic-value-item" data-variable="' + variable.variable_key + '">');
                                $item.append('<div class="pfb-dynamic-value-name">' + variable.name + '</div>');
                                $item.append('<div class="pfb-dynamic-value-key">{' + variable.variable_key + '}</div>');
                                $list.append($item);
                            });

                            $category.append($list);
                            $categories.append($category);
                        }
                    }

                    if (!hasVariables) {
                        $categories.html('<div class="pfb-dynamic-value-empty">No variables available. Please add variables in the Price Variables section.</div>');
                    }

                    // Add click handler for variable items
                    $('.pfb-dynamic-value-item').on('click', function() {
                        const variable = $(this).data('variable');
                        const $valueInput = $('#pfb-dynamic-value-modal').data('valueInput');
                        const $variableInput = $('#pfb-dynamic-value-modal').data('variableInput');

                        // Set the value input to the variable placeholder
                        if ($valueInput) {
                            $valueInput.val('{' + variable + '}');
                            console.log('Set value input to:', '{' + variable + '}');
                        } else {
                            console.error('Value input not found');
                        }

                        // Store the variable key in the hidden input
                        if ($variableInput) {
                            $variableInput.val(variable);
                            console.log('Set variable input to:', variable);
                        } else {
                            console.error('Variable input not found');
                        }

                        // Log the current state of the inputs
                        console.log('Current state after selection:', {
                            valueInput: $valueInput ? $valueInput.val() : 'not found',
                            variableInput: $variableInput ? $variableInput.val() : 'not found'
                        });

                        // Close the modal
                        $('#pfb-dynamic-value-modal').hide();
                    });

                    // Add search functionality
                    $('#pfb-dynamic-value-search-input').on('input', function() {
                        const searchTerm = $(this).val().toLowerCase();

                        $('.pfb-dynamic-value-item').each(function() {
                            const name = $(this).find('.pfb-dynamic-value-name').text().toLowerCase();
                            const key = $(this).find('.pfb-dynamic-value-key').text().toLowerCase();

                            if (name.includes(searchTerm) || key.includes(searchTerm)) {
                                $(this).show();
                            } else {
                                $(this).hide();
                            }
                        });

                        // Show/hide categories based on visible items
                        $('.pfb-dynamic-value-category').each(function() {
                            const $category = $(this);
                            const hasVisibleItems = $category.find('.pfb-dynamic-value-item:visible').length > 0;

                            if (hasVisibleItems) {
                                $category.show();
                            } else {
                                $category.hide();
                            }
                        });
                    });
                } else {
                    $categories.html('<div class="pfb-dynamic-value-empty">Failed to load variables. Please try again.</div>');
                }
            },
            error: function() {
                $categories.html('<div class="pfb-dynamic-value-empty">Failed to load variables. Please try again.</div>');
            }
        });
    }

    // Close dynamic value modal
    $(document).on('click', '.pfb-modal-close, .pfb-modal-cancel', function() {
        $('.pfb-modal').hide();
    });

    // Prevent modal close when clicking inside
    $(document).on('click', '.pfb-modal-content', function(e) {
        e.stopPropagation();
    });

    // Close modal when clicking outside
    $(document).on('click', '.pfb-modal', function() {
        $(this).hide();
    });

    // Handle formula variable item click
    $(document).on('click', '.pfb-formula-variable-item', function() {
        const variableName = $(this).data('variable');
        const $formulaInput = $('#pfb-formula-variables-modal').data('formulaInput');

        if ($formulaInput) {
            insertAtCursor($formulaInput[0], '{' + variableName + '}');

            // Close the modal
            $('#pfb-formula-variables-modal').hide();
        }
    });

    /**
     * Initialize formula builder event handlers.
     */
    function initFormulaBuilder($field) {
        const $formulaInput = $field.find('.pfb-field-formula-input');
        const fieldType = $field.data('type');

        // Function to get the current formula input
        function getCurrentFormulaInput() {
            // For subtotal fields, use the stored reference to the current formula input
            if (fieldType === 'subtotal') {
                return $field.data('currentFormulaInput') || $formulaInput;
            }
            // For total fields, use the main formula input
            return $formulaInput;
        }

        // Field button click
        $field.on('click', '.pfb-formula-field', function() {
            const fieldName = $(this).data('field');
            const $currentInput = getCurrentFormulaInput();
            insertAtCursor($currentInput[0], '{' + fieldName + '}');
        });

        // Variable button click
        $field.on('click', '.pfb-formula-variable', function() {
            const variableName = $(this).data('variable');
            const $currentInput = getCurrentFormulaInput();
            insertAtCursor($currentInput[0], '{' + variableName + '}');
        });

        // Number button click
        $field.on('click', '.pfb-formula-number', function() {
            const number = $(this).data('number');
            const $currentInput = getCurrentFormulaInput();
            insertAtCursor($currentInput[0], number);
        });

        // Operator button click
        $field.on('click', '.pfb-formula-operator', function() {
            const operator = $(this).data('operator');
            const $currentInput = getCurrentFormulaInput();

            // Special handling for parentheses
            if (operator === '(') {
                // Always insert a pair of parentheses and position cursor between them
                insertAtCursor($currentInput[0], '()');

                // Move cursor between the parentheses
                const cursorPos = $currentInput[0].selectionStart;
                $currentInput[0].selectionStart = cursorPos - 1;
                $currentInput[0].selectionEnd = cursorPos - 1;
                $currentInput[0].focus();

                // Show a helpful message
                showFormulaInfo($field, 'Parentheses added. Type your expression between them.');
            } else if (operator === ')') {
                // We don't need this button anymore since we're always inserting paired parentheses
                showFormulaInfo($field, 'Use the ( button to add a pair of parentheses.');
            } else {
                // For all other operators, just insert them
                insertAtCursor($currentInput[0], operator);
            }
        });

        // Function button click
        $field.on('click', '.pfb-formula-function', function() {
            const func = $(this).data('function');
            const funcName = func.substring(0, func.length - 1);
            const $currentInput = getCurrentFormulaInput();

            // Always insert the function with empty parentheses
            insertAtCursor($currentInput[0], funcName + '()');

            // Position cursor inside the parentheses
            const cursorPos = $currentInput[0].selectionStart;
            $currentInput[0].selectionStart = cursorPos - 1;
            $currentInput[0].selectionEnd = cursorPos - 1;
            $currentInput[0].focus();

            // Show a helpful tooltip about the function
            let helpText = '';
            switch (funcName) {
                case 'ceil':
                    helpText = 'Rounds a number up to the next whole number. Example: ceil(4.3) = 5';
                    break;
                case 'floor':
                    helpText = 'Rounds a number down to the previous whole number. Example: floor(4.7) = 4';
                    break;
                case 'round':
                    helpText = 'Rounds a number to the nearest whole number. Example: round(4.5) = 5';
                    break;
                case 'min':
                    helpText = 'Returns the smallest of the given numbers. Example: min(4, 7, 2) = 2';
                    break;
                case 'max':
                    helpText = 'Returns the largest of the given numbers. Example: max(4, 7, 2) = 7';
                    break;
            }

            if (helpText) {
                showFormulaInfo($field, helpText);
            }
        });

        // Clear button click
        $field.on('click', '.pfb-clear-formula', function() {
            const $currentInput = getCurrentFormulaInput();
            $currentInput.val('');
            $currentInput.focus();
        });

        // Open variables modal
        $field.on('click', '.pfb-select-formula-variables', function() {
            const $currentInput = getCurrentFormulaInput();

            // Store reference to the formula input
            $('#pfb-formula-variables-modal').data('formulaInput', $currentInput);

            // Load variables
            loadFormulaVariablesForModal();

            // Show modal
            $('#pfb-formula-variables-modal').show();
        });
    }

    /**
     * Load price variables for the formula variables modal.
     */
    function loadFormulaVariablesForModal() {
        const $categories = $('#pfb-formula-variables-modal .pfb-formula-variables-list');

        // Show loading message
        $categories.html('<div class="pfb-formula-empty">Loading variables...</div>');

        // Load variables via AJAX
        $.ajax({
            url: pfb_data.ajax_url,
            type: 'GET',
            data: {
                action: 'pfb_get_price_variables',
                nonce: pfb_data.nonce
            },
            success: function(response) {
                if (response.success && response.data.variables) {
                    $categories.empty();

                    let hasVariables = false;

                    // Group variables by category
                    const categories = {};

                    response.data.variables.forEach(function(variable) {
                        if (!categories[variable.category_id]) {
                            categories[variable.category_id] = {
                                name: variable.category_name,
                                variables: []
                            };
                        }

                        categories[variable.category_id].variables.push(variable);
                        hasVariables = true;
                    });

                    // Add variables to the modal
                    if (hasVariables) {
                        Object.values(categories).forEach(function(category) {
                            const $category = $('<div class="pfb-formula-variables-category"></div>');
                            $category.append('<h4>' + category.name + '</h4>');

                            const $variables = $('<div class="pfb-formula-variables-items"></div>');

                            category.variables.forEach(function(variable) {
                                const $variable = $('<button type="button" class="pfb-formula-variable-item" data-variable="' + variable.variable_key + '">' + variable.name + '</button>');
                                $variables.append($variable);
                            });

                            $category.append($variables);
                            $categories.append($category);
                        });
                    } else {
                        $categories.html('<div class="pfb-formula-empty">No variables available</div>');
                    }
                } else {
                    $categories.html('<div class="pfb-formula-empty">Failed to load variables</div>');
                }
            },
            error: function() {
                $categories.html('<div class="pfb-formula-empty">Failed to load variables</div>');
            }
        });
    }

    /**
     * Debug function to log form data before saving
     */
    function logFormData(formData) {
        console.log('Form data to be saved:', JSON.parse(JSON.stringify(formData)));

        // Log field options specifically
        if (formData.fields && formData.fields.length > 0) {
            formData.fields.forEach(function(field, index) {
                if (field.options && field.options.items) {
                    console.log(`Field ${index} (${field.type}) options:`, JSON.parse(JSON.stringify(field.options.items)));
                }
                if (field.conditional_logic) {
                    console.log(`Field ${index} (${field.type}) conditional logic:`, JSON.parse(JSON.stringify(field.conditional_logic)));
                }
            });
        }
    }

    /**
     * Get conditional logic data from a field element.
     */
    function getConditionalLogicData(fieldElement) {
        const fieldId = fieldElement.attr('id') || fieldElement.data('field-id');
        const fieldType = fieldElement.data('type');
        console.log('Getting conditional logic data for field:', fieldId, 'type:', fieldType);

        const conditionalLogicToggle = fieldElement.find('.pfb-conditional-logic-toggle');

        if (!conditionalLogicToggle.length) {
            console.log('No conditional logic toggle found for field:', fieldId);
            return null;
        }

        const isEnabled = conditionalLogicToggle.find('.pfb-enable-conditional-logic').is(':checked');
        console.log('Conditional logic enabled for field', fieldId, ':', isEnabled);

        if (!isEnabled) {
            return null;
        }

        // Get logic type (all or any)
        const logicType = conditionalLogicToggle.find('.pfb-logic-type').val();

        // Get rules
        const rules = [];
        conditionalLogicToggle.find('.pfb-conditional-rule').each(function() {
            const $rule = $(this);
            const field = $rule.find('.pfb-rule-field').val();
            const operator = $rule.find('.pfb-rule-operator').val();
            const value = $rule.find('.pfb-rule-value').val();

            // Only add rules that have a field selected
            if (field) {
                rules.push({
                    field: field,
                    operator: operator,
                    value: value
                });
            }
        });

        // Only return conditional logic if there are rules
        if (rules.length > 0) {
            return {
                logic_type: logicType,
                rules: rules
            };
        }

        return null;
    }

    /**
     * Insert text at cursor position in a textarea.
     */
    function insertAtCursor(textarea, text) {
        if (textarea.selectionStart || textarea.selectionStart === 0) {
            const startPos = textarea.selectionStart;
            const endPos = textarea.selectionEnd;

            textarea.value = textarea.value.substring(0, startPos) + text + textarea.value.substring(endPos, textarea.value.length);

            // Set cursor position after inserted text
            textarea.selectionStart = startPos + text.length;
            textarea.selectionEnd = startPos + text.length;
        } else {
            textarea.value += text;
        }

        // Focus the textarea
        textarea.focus();
    }

    /**
     * Get form data for saving.
     */
    function getFormData() {
        const $form = $('#pfb-form-editor-form');
        const formData = {
            title: $('#form_title').val(),
            description: $('#form_description').val(),
            status: $('#form_status').val(),
            settings: {
                template: $('#form_template').val(),
                show_currency_selector: $('#form_show_currency_selector').val(),
                submit_button_text: $('#form_submit_button_text').val()
            },
            fields: []
        };

        // Get form ID if editing
        const urlParams = new URLSearchParams(window.location.search);
        const formId = urlParams.get('form_id');

        if (formId) {
            formData.id = formId;
        }

        // Debug log for form data collection
        console.log('Collecting form data for saving...');

        // Get fields
        $('.pfb-form-fields .pfb-field').each(function(index) {
            const $field = $(this);
            const fieldType = $field.data('type');
            const fieldId = $field.attr('id');

            // Get field width from dropdown or data attribute
            const $widthDropdown = $field.find('.pfb-field-width-input');
            const dropdownWidth = $widthDropdown.length > 0 ? $widthDropdown.val() : null;
            const dataWidth = $field.attr('data-width');

            // Get the numeric width value (without % symbol)
            let fieldWidth = dropdownWidth || dataWidth || '100';

            // Remove % symbol if present for storage
            fieldWidth = fieldWidth.toString().replace('%', '');

            const field = {
                type: fieldType,
                label: $field.find('.pfb-field-label-input').val(),
                name: $field.find('.pfb-field-name-input').val(),
                required: $field.find('.pfb-field-required-input').is(':checked'),
                hidden: fieldType === 'text' ? $field.find('.pfb-field-hidden-input').is(':checked') : false,
                width: fieldWidth,
                options: {},
                id: fieldId
            };

            // Get conditional logic data if available
            if (fieldType !== 'total') {
                // First try to get conditional logic from PFBConditionalLogic if available
                let conditionalLogic = null;

                if (typeof window.PFBConditionalLogic !== 'undefined' && typeof window.PFBConditionalLogic.getConditionalLogicData === 'function') {
                    conditionalLogic = window.PFBConditionalLogic.getConditionalLogicData($field);
                    console.log('Got conditional logic from PFBConditionalLogic for field', fieldId, ':', conditionalLogic);
                }

                // Fallback to local function if PFBConditionalLogic is not available or returned null
                if (!conditionalLogic) {
                    conditionalLogic = getConditionalLogicData($field);
                    console.log('Got conditional logic from local function for field', fieldId, ':', conditionalLogic);
                }

                if (conditionalLogic) {
                    field.conditional_logic = conditionalLogic;
                    console.log('Added conditional logic to field', fieldId, ':', conditionalLogic);
                }
            }

            // Add hidden field properties directly to the field object
            if (fieldType === 'text' && field.hidden) {
                // Get variable and value
                const variableValue = $field.find('.pfb-hidden-field-variable').val();
                const defaultValue = $field.find('.pfb-hidden-field-value').val();

                // Store variable and default value directly on the field object
                if (variableValue) {
                    field.variable = variableValue;
                }

                if (defaultValue) {
                    field.default_value = defaultValue;
                }
            }

            // Get field-specific options
            switch (fieldType) {
                case 'slider':
                    // Get slider options
                    const min = $field.find('.pfb-slider-min-input').val() || 0;
                    const max = $field.find('.pfb-slider-max-input').val() || 100;
                    const step = $field.find('.pfb-slider-step-input').val() || 1;
                    const defaultValue = $field.find('.pfb-slider-default-input').val() || 50;

                    field.options.min = parseInt(min);
                    field.options.max = parseInt(max);
                    field.options.step = parseInt(step);
                    field.options.default = parseInt(defaultValue);
                    break;

                case 'subtotal':
                    // Get subtotal lines
                    const lines = [];
                    $field.find('.pfb-subtotal-line').each(function() {
                        const $line = $(this);
                        const label = $line.find('.pfb-subtotal-line-label').val();
                        const formula = $line.find('.pfb-subtotal-line-formula').val();

                        // Only add lines that have either a label or a formula
                        if (label || formula) {
                            lines.push({
                                label: label || 'Line',
                                formula: formula || ''
                            });
                        }
                    });

                    // Make sure we have at least one line
                    if (lines.length === 0) {
                        lines.push({
                            label: 'Line 1',
                            formula: ''
                        });
                    }

                    // Get empty value display
                    const emptyValue = $field.find('.pfb-subtotal-empty-value-input').val() || '---';

                    // Make sure we're creating a new object for options
                    field.options = field.options || {};
                    field.options.lines = lines;
                    field.options.empty_value = emptyValue;
                    break;

                case 'dropdown':
                case 'radio':
                case 'checkbox':
                    // Use the getFieldOptions function to get all options
                    const fieldOptions = getFieldOptions($field);

                    // Filter out empty options
                    field.options.items = fieldOptions.filter(option =>
                        option.label !== '' || option.value !== ''
                    );

                    // Ensure each option has all required properties
                    field.options.items = field.options.items.map(option => ({
                        label: option.label || 'Option',
                        value: option.value || option.label || '0',
                        variable: option.variable || ''
                    }));

                    field.options.variable = $field.find('.pfb-field-variable-input').val();
                    break;

                case 'total':
                    const formula = $field.find('.pfb-field-formula-input').val();
                    field.options.formula = formula;
                    break;
            }

            formData.fields.push(field);
        });

        return formData;
    }

    /**
     * Save form data.
     */
    function saveForm(formData) {
        if (!formData) {
            formData = getFormData();
        }

        // Debug log for form settings
        console.log('Saving form with template:', formData.settings.template);

        // Get form ID if editing
        const urlParams = new URLSearchParams(window.location.search);
        const formId = urlParams.get('form_id');

        if (formId) {
            formData.id = formId;
        }

        // Don't clear the fields array - this was causing duplication
        // formData.fields = [];

        // Get fields
        $('.pfb-form-fields .pfb-field').each(function(index) {
            const $field = $(this);
            const fieldType = $field.data('type');
            const fieldId = $field.attr('id');

            // Get field width from dropdown or data attribute
            const $widthDropdown = $field.find('.pfb-field-width-input');
            const dropdownWidth = $widthDropdown.length > 0 ? $widthDropdown.val() : null;
            const dataWidth = $field.attr('data-width');

            // Get the numeric width value (without % symbol)
            let fieldWidth = dropdownWidth || dataWidth || '100';

            // Remove % symbol if present for storage
            fieldWidth = fieldWidth.toString().replace('%', '');



            const field = {
                type: fieldType,
                label: $field.find('.pfb-field-label-input').val(),
                name: $field.find('.pfb-field-name-input').val(),
                required: $field.find('.pfb-field-required-input').is(':checked'),
                hidden: fieldType === 'text' ? $field.find('.pfb-field-hidden-input').is(':checked') : false,
                width: fieldWidth,
                options: {},
                id: fieldId
            };

            // Get conditional logic data if available
            if (fieldType !== 'total') {
                // First try to get conditional logic from PFBConditionalLogic if available
                let conditionalLogic = null;

                if (typeof window.PFBConditionalLogic !== 'undefined' && typeof window.PFBConditionalLogic.getConditionalLogicData === 'function') {
                    conditionalLogic = window.PFBConditionalLogic.getConditionalLogicData($field);
                    console.log('Got conditional logic from PFBConditionalLogic for field', fieldId, ':', conditionalLogic);
                }

                // Fallback to local function if PFBConditionalLogic is not available or returned null
                if (!conditionalLogic) {
                    conditionalLogic = getConditionalLogicData($field);
                    console.log('Got conditional logic from local function for field', fieldId, ':', conditionalLogic);
                }

                if (conditionalLogic) {
                    field.conditional_logic = conditionalLogic;
                    console.log('Added conditional logic to field', fieldId, ':', JSON.stringify(conditionalLogic));
                }
            }



            // Add hidden field properties directly to the field object
            if (fieldType === 'text' && field.hidden) {
                // Get variable and value
                const variableValue = $field.find('.pfb-hidden-field-variable').val();
                const defaultValue = $field.find('.pfb-hidden-field-value').val();

                // Store variable and default value directly on the field object
                if (variableValue) {
                    field.variable = variableValue;
                }

                if (defaultValue) {
                    field.default_value = defaultValue;
                }

                // Log for debugging
                console.log('Hidden field data for ' + field.name + ':', {
                    hidden: field.hidden,
                    variable: field.variable,
                    default_value: field.default_value
                });

                // Also log the DOM elements for debugging
                console.log('Hidden field DOM elements:', {
                    hidden_checkbox: $field.find('.pfb-field-hidden-input').get(0),
                    variable_input: $field.find('.pfb-hidden-field-variable').get(0),
                    value_input: $field.find('.pfb-hidden-field-value').get(0)
                });
            }

            // Get field-specific options
            switch (fieldType) {
                case 'slider':
                    // Get slider options
                    const min = $field.find('.pfb-slider-min-input').val() || 0;
                    const max = $field.find('.pfb-slider-max-input').val() || 100;
                    const step = $field.find('.pfb-slider-step-input').val() || 1;
                    const defaultValue = $field.find('.pfb-slider-default-input').val() || 50;

                    field.options.min = parseInt(min);
                    field.options.max = parseInt(max);
                    field.options.step = parseInt(step);
                    field.options.default = parseInt(defaultValue);

                    // Log options for debugging
                    console.log(`Slider field ${fieldId} options:`, JSON.parse(JSON.stringify(field.options)));
                    break;

                case 'subtotal':
                    // Get subtotal lines
                    const lines = [];
                    $field.find('.pfb-subtotal-line').each(function() {
                        const $line = $(this);
                        const label = $line.find('.pfb-subtotal-line-label').val();
                        const formula = $line.find('.pfb-subtotal-line-formula').val();

                        console.log(`Subtotal line: label=${label}, formula=${formula}`);

                        // Only add lines that have either a label or a formula
                        // This prevents empty lines from being saved
                        if (label || formula) {
                            lines.push({
                                label: label || 'Line',
                                formula: formula || ''
                            });
                        }
                    });

                    // Make sure we have at least one line
                    if (lines.length === 0) {
                        lines.push({
                            label: 'Line 1',
                            formula: ''
                        });
                    }

                    // Get empty value display
                    const emptyValue = $field.find('.pfb-subtotal-empty-value-input').val() || '---';

                    // Make sure we're creating a new object for options
                    field.options = field.options || {};
                    field.options.lines = lines;
                    field.options.empty_value = emptyValue;

                    // Log options for debugging
                    console.log(`Subtotal field ${fieldId} options:`, JSON.parse(JSON.stringify(field.options)));
                    break;

                case 'dropdown':
                case 'radio':
                case 'checkbox':
                    // Use the getFieldOptions function to get all options
                    const fieldOptions = getFieldOptions($field);

                    // Filter out empty options
                    field.options.items = fieldOptions.filter(option =>
                        option.label !== '' || option.value !== ''
                    );

                    // Ensure each option has all required properties
                    field.options.items = field.options.items.map(option => ({
                        label: option.label || 'Option',
                        value: option.value || option.label || '0',
                        variable: option.variable || ''
                    }));

                    // Log options for debugging
                    console.log(`Field ${fieldId} options:`, JSON.parse(JSON.stringify(field.options.items)));

                    field.options.variable = $field.find('.pfb-field-variable-input').val();
                    break;

                case 'total':
                    const formula = $field.find('.pfb-field-formula-input').val();

                    // Validate formula before saving
                    if (formula && !validateFormula(formula)) {
                        showFormulaWarning($field, 'Formula may have syntax errors. Please check parentheses and operators.');

                        // Scroll to the field with the error
                        $('html, body').animate({
                            scrollTop: $field.offset().top - 100
                        }, 300);

                        // Continue saving anyway, but with a warning
                        console.warn('Saving formula with potential syntax errors:', formula);
                    }

                    field.options.formula = formula;
                    break;
            }

            formData.fields.push(field);
        });

        // Debug form data before saving
        logFormData(formData);



        // Debug log for form data before AJAX
        console.log('Form data being sent to server:', JSON.stringify(formData));
        console.log('Template setting:', formData.settings.template);

        // Debug log specifically for conditional logic
        formData.fields.forEach(function(field) {
            if (field.conditional_logic) {
                console.log('Field ' + field.id + ' has conditional logic:', JSON.stringify(field.conditional_logic));
            }
        });

        // Save form via AJAX
        console.log('PFB Admin: Sending AJAX request to save form');

        // Disable both save buttons
        $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', true).text(pfb_data.i18n.loading || 'Saving...');

        $.ajax({
            url: pfb_data.ajax_url,
            type: 'POST',
            data: {
                action: 'pfb_save_form',
                nonce: pfb_data.nonce,
                form_data: formData
            },
            beforeSend: function() {
                console.log('PFB Admin: AJAX request being sent');
            },
            success: function(response) {
                console.log('PFB Admin: AJAX response received:', response);

                if (response.success) {
                    // Show success message
                    showNotification('success', response.data.message || 'Form saved successfully!');

                    // Redirect to edit page if new form
                    if (!formId && response.data.form_id) {
                        console.log('PFB Admin: Redirecting to edit page for new form:', response.data.form_id);
                        window.location.href = 'admin.php?page=pfb-form-editor&form_id=' + response.data.form_id;
                    }
                } else {
                    console.error('PFB Admin: Error saving form:', response.data ? response.data.message : 'Unknown error');
                    showNotification('error', response.data ? response.data.message : 'Unknown error saving form');
                }
            },
            error: function(xhr, status, error) {
                console.error('PFB Admin: AJAX error:', status, error);
                showNotification('error', 'An error occurred while saving the form: ' + error);
            },
            complete: function() {
                console.log('PFB Admin: AJAX request complete');
                // Re-enable both save buttons
                $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text(pfb_data.i18n.save || 'Save Form');
            }
        });
    }

    /**
     * Load form data for editing.
     */
    function loadForm(formId) {
        console.log('PFB Admin: Loading form with ID:', formId);

        // Show loading indicator
        $('#pfb-form-editor-form').addClass('loading');
        $('.pfb-form-fields').html('<div class="pfb-loading-message">Loading form data...</div>');

        // Add a debug message
        console.log('PFB Admin: If the form does not load, use the "Debug: Load Form" button at the top of the page');

        $.ajax({
            url: pfb_data.ajax_url,
            type: 'GET',
            data: {
                action: 'pfb_get_form',
                nonce: pfb_data.nonce,
                form_id: formId
            },
            beforeSend: function() {
                console.log('PFB Admin: Sending AJAX request to load form');
            },
            success: function(response) {
                console.log('PFB Admin: Received form data response:', response);

                if (response.success && response.data && response.data.form) {
                    const form = response.data.form;
                    console.log('PFB Admin: Form data loaded:', form);

                    // Debug: Check if fields array exists and has items
                    if (form.fields && Array.isArray(form.fields)) {
                        console.log('PFB Admin: Form has ' + form.fields.length + ' fields');
                        form.fields.forEach((field, index) => {
                            console.log(`PFB Admin: Field ${index + 1}:`, field);
                        });
                    } else {
                        console.error('PFB Admin: Form fields missing or not an array:', form.fields);
                    }

                    try {
                        // Set form data
                        $('#form_title').val(form.title || '');
                        $('#form_description').val(form.description || '');
                        $('#form_status').val(form.status || 'draft');

                        // Load form settings if available
                        if (form.settings) {
                            console.log('PFB Admin: Form settings:', form.settings);

                            if (form.settings.template) {
                                $('#form_template').val(form.settings.template);
                            }
                            if (form.settings.show_currency_selector !== undefined) {
                                $('#form_show_currency_selector').val(form.settings.show_currency_selector);
                            }
                            if (form.settings.submit_button_text) {
                                $('#form_submit_button_text').val(form.settings.submit_button_text);
                            }
                        }

                        // Clear existing fields
                        console.log('PFB Admin: Clearing existing fields');
                        $('.pfb-form-fields').empty();

                        // Check if we have fields
                        if (!form.fields || !Array.isArray(form.fields) || form.fields.length === 0) {
                            console.log('PFB Admin: No fields found in form data');
                            $('.pfb-form-fields').html('<div class="pfb-empty-form-message">No fields found. Add fields using the buttons above.</div>');
                            return;
                        }

                        console.log('PFB Admin: Loading ' + form.fields.length + ' fields');
                    } catch (error) {
                        console.error('PFB Admin: Error setting form data:', error);
                    }

                    // Add fields
                    if (form.fields && form.fields.length > 0) {
                        console.log('PFB Admin: Processing ' + form.fields.length + ' fields');

                        // Clear existing fields first
                        console.log('PFB Admin: Clearing existing fields before loading new ones');
                        $('.pfb-form-fields').empty();

                        // Process fields one by one with a slight delay to avoid browser freezing
                        let fieldIndex = 0;

                        function processNextField() {
                            if (fieldIndex >= form.fields.length) {
                                console.log('PFB Admin: All fields processed successfully');

                                // Initialize conditional logic for all fields after they're loaded
                                if (typeof window.PFBConditionalLogic !== 'undefined' && typeof window.PFBConditionalLogic.init === 'function') {
                                    console.log('PFB Admin: Initializing conditional logic after loading all fields');
                                    window.PFBConditionalLogic.init();
                                }
                                return;
                            }

                            const field = form.fields[fieldIndex];
                            console.log('PFB Admin: Processing field ' + (fieldIndex + 1) + ' of ' + form.fields.length + ':', field);

                            try {
                                // Generate a unique ID for the field
                                const fieldId = 'field_' + Date.now() + '_' + Math.floor(Math.random() * 1000);

                                // Create the field element
                                const $field = createField(field.field_type, fieldId);

                                if (!$field || !$field.length) {
                                    console.error('PFB Admin: Failed to create field element for field:', field);
                                    fieldIndex++;
                                    setTimeout(processNextField, 10);
                                    return;
                                }

                                // Add the field to the form
                                $('.pfb-form-fields').append($field);

                            // Set field data
                            $field.find('.pfb-field-label-input').val(field.field_label);
                            $field.find('.pfb-field-name-input').val(field.field_name);
                            $field.find('.pfb-field-required-input').prop('checked', field.field_required == 1);

                            // Set field width if available
                            let fieldWidth = '100';



                            // First try to get width from field_width column (new approach)
                            if (field.field_width !== undefined && field.field_width !== null) {
                                // Get the numeric width value
                                fieldWidth = field.field_width.toString().replace('%', '');
                            }
                            // Fallback to field_options.width (old approach)
                            else if (field.field_options && field.field_options.width) {
                                // Get the numeric width value
                                fieldWidth = field.field_options.width.toString().replace('%', '');
                            }

                            // Set the width in the dropdown and as a data attribute
                            const $widthDropdown = $field.find('.pfb-field-width-input');


                            if ($widthDropdown.length > 0) {
                                // Force the dropdown to have the correct value
                                $widthDropdown.val(fieldWidth);

                                // If the value wasn't set correctly, try to find the option and select it
                                if ($widthDropdown.val() !== fieldWidth) {
                                    // Try to find the option with the correct value
                                    const $option = $widthDropdown.find('option[value="' + fieldWidth + '"]');

                                    if ($option.length > 0) {
                                        // Select the option
                                        $option.prop('selected', true);
                                    } else {
                                        // Add a new option if it doesn't exist
                                        $widthDropdown.append('<option value="' + fieldWidth + '">' + fieldWidth + '% (Custom)</option>');
                                        $widthDropdown.val(fieldWidth);
                                    }
                                }
                            }

                            // Store the width as a data attribute (without % symbol)
                            $field.attr('data-width', fieldWidth);

                            // Add width indicator if not 100% (with % symbol for display)
                            if (fieldWidth !== '100') {
                                $field.find('.pfb-field-title').after(`<span class="pfb-field-width-indicator">Width: ${fieldWidth}%</span>`);
                            }

                            // Update the field title with the label
                            $field.find('.pfb-field-title').text(field.field_label);

                            // Get field options
                            let fieldOptions = {};
                            if (field.field_options) {
                                // Handle field options
                                fieldOptions = field.field_options || {};

                                // For subtotal fields, ensure lines array exists
                                if (field.field_type === 'subtotal') {
                                    console.log('Processing subtotal field options:', fieldOptions);

                                    if (!fieldOptions.lines || !Array.isArray(fieldOptions.lines)) {
                                        console.log('Creating default lines array for subtotal field');
                                        fieldOptions.lines = [
                                            {
                                                label: 'Line 1',
                                                formula: ''
                                            }
                                        ];
                                    }

                                    if (!fieldOptions.empty_value) {
                                        fieldOptions.empty_value = '---';
                                    }
                                }

                                // Debug log
                                console.log('Field options loaded for field ' + field.field_name + ':', fieldOptions);
                            }

                            // Check if this is a hidden text field
                            let isHidden = false;

                            if (field.field_type === 'text') {
                                // Check all possible ways the field could be marked as hidden
                                isHidden = (field.field_hidden == 1) ||
                                          (fieldOptions && fieldOptions.is_hidden === true);

                                console.log('Field ' + field.field_name + ' hidden status check:', {
                                    isHidden: isHidden,
                                    field_hidden: field.field_hidden,
                                    options_is_hidden: fieldOptions && fieldOptions.is_hidden
                                });
                            }

                            if (isHidden) {
                                // Set the hidden checkbox
                                $field.find('.pfb-field-hidden-input').prop('checked', true);

                                // Show the hidden field options
                                $field.find('.pfb-hidden-field-options').show();

                                console.log('Setting up hidden field ' + field.field_name);

                                // Get variable from all possible sources
                                let variableValue = null;
                                if (field.field_variable) {
                                    variableValue = field.field_variable;
                                    console.log('Found variable in field_variable:', variableValue);
                                } else if (fieldOptions && fieldOptions.variable) {
                                    variableValue = fieldOptions.variable;
                                    console.log('Found variable in fieldOptions.variable:', variableValue);
                                }

                                // Get default value from all possible sources
                                let defaultValue = null;
                                if (field.field_default_value) {
                                    defaultValue = field.field_default_value;
                                    console.log('Found default_value in field_default_value:', defaultValue);
                                } else if (fieldOptions && fieldOptions.default_value) {
                                    defaultValue = fieldOptions.default_value;
                                    console.log('Found default_value in fieldOptions.default_value:', defaultValue);
                                }

                                // Set the values in the form
                                if (variableValue) {
                                    $field.find('.pfb-hidden-field-variable').val(variableValue);
                                    $field.find('.pfb-hidden-field-value').val('{' + variableValue + '}');
                                } else if (defaultValue) {
                                    $field.find('.pfb-hidden-field-value').val(defaultValue);
                                }

                                // Log the final values
                                console.log('Final hidden field values for ' + field.field_name + ':', {
                                    variable: $field.find('.pfb-hidden-field-variable').val(),
                                    value: $field.find('.pfb-hidden-field-value').val()
                                });
                            }

                            // Handle conditional logic if available
                            if (field.conditional_logic) {
                                console.log('Loading conditional logic for field', field.field_name, ':', field.conditional_logic);

                                // Store conditional logic data on the field element
                                $field.data('conditional-logic', field.conditional_logic);

                                // Enable conditional logic toggle
                                const $toggle = $field.find('.pfb-conditional-logic-toggle');
                                if ($toggle.length) {
                                    const $checkbox = $toggle.find('.pfb-enable-conditional-logic');
                                    $checkbox.prop('checked', true);

                                    // Show the conditional logic container
                                    $toggle.find('.pfb-conditional-logic-container').show();

                                    // Set logic type
                                    if (field.conditional_logic.logic_type) {
                                        $toggle.find('.pfb-logic-type').val(field.conditional_logic.logic_type);
                                    }

                                    // Clear existing rules
                                    $toggle.find('.pfb-conditional-rules').empty();

                                    // Add rules
                                    if (field.conditional_logic.rules && field.conditional_logic.rules.length > 0) {
                                        field.conditional_logic.rules.forEach(function(rule) {
                                            // Create rule HTML
                                            const $rule = $(`
                                                <div class="pfb-conditional-rule">
                                                    <select class="pfb-rule-field">
                                                        <option value="">${pfb_data.i18n.selectField}</option>
                                                    </select>
                                                    <select class="pfb-rule-operator">
                                                        <option value="is">${pfb_data.i18n.is}</option>
                                                        <option value="is_not">${pfb_data.i18n.isNot}</option>
                                                        <option value="greater_than">${pfb_data.i18n.greaterThan}</option>
                                                        <option value="less_than">${pfb_data.i18n.lessThan}</option>
                                                        <option value="contains">${pfb_data.i18n.contains}</option>
                                                    </select>
                                                    <input type="text" class="pfb-rule-value" placeholder="${pfb_data.i18n.value}">
                                                    <button type="button" class="pfb-remove-rule pfb-btn pfb-btn-icon">
                                                        <span class="dashicons dashicons-no-alt"></span>
                                                    </button>
                                                </div>
                                            `);

                                            // Add rule to container
                                            $toggle.find('.pfb-conditional-rules').append($rule);

                                            // Populate field dropdown
                                            $('.pfb-field').each(function() {
                                                const otherFieldElement = $(this);
                                                const otherFieldId = otherFieldElement.attr('id') || otherFieldElement.data('field-id');
                                                const otherFieldType = otherFieldElement.data('type');
                                                const otherFieldLabel = otherFieldElement.find('.pfb-field-label-input').val() || otherFieldElement.find('.pfb-field-title').text().trim();

                                                // Skip current field and total/subtotal fields
                                                if (otherFieldId !== $field.attr('id') && otherFieldType !== 'total' && otherFieldType !== 'subtotal') {
                                                    $rule.find('.pfb-rule-field').append(`<option value="${otherFieldId}">${otherFieldLabel}</option>`);
                                                }
                                            });

                                            // Set rule values
                                            if (rule.field) {
                                                $rule.find('.pfb-rule-field').val(rule.field);
                                            }
                                            if (rule.operator) {
                                                $rule.find('.pfb-rule-operator').val(rule.operator);
                                            }
                                            if (rule.value !== undefined) {
                                                $rule.find('.pfb-rule-value').val(rule.value);
                                            }
                                        });
                                    } else {
                                        // Add default rule if none exists
                                        addRuleRow($toggle.find('.pfb-conditional-logic-container'));
                                    }
                                }
                            }

                            // Set field-specific options
                            const options = field.field_options;

                            if (options) {
                                switch (field.field_type) {
                                    case 'slider':
                                        // Log the options for debugging
                                        console.log('Loading slider field options:', options);

                                        // Set slider options
                                        const min = options.min !== undefined ? options.min : 0;
                                        const max = options.max !== undefined ? options.max : 100;
                                        const step = options.step !== undefined ? options.step : 1;
                                        const defaultValue = options.default !== undefined ? options.default : 50;

                                        $field.find('.pfb-slider-min-input').val(min);
                                        $field.find('.pfb-slider-max-input').val(max);
                                        $field.find('.pfb-slider-step-input').val(step);
                                        $field.find('.pfb-slider-default-input').val(defaultValue);

                                        console.log('Set slider options:', { min, max, step, default: defaultValue });
                                        break;

                                    case 'dropdown':
                                    case 'radio':
                                    case 'checkbox':
                                        // Log the options for debugging
                                        console.log('Loading field options:', options);

                                        // Ensure options.items is an array
                                        if (!options.items || !Array.isArray(options.items)) {
                                            options.items = [];
                                            console.log('Field options.items was not an array, initializing empty array');
                                        }

                                        // Add a default option if none exist
                                        if (options.items.length === 0) {
                                            options.items.push({
                                                label: 'Option 1',
                                                value: 'option_1',
                                                variable: ''
                                            });
                                            console.log('Added default option to empty options list');
                                        }

                                        if (options.items && options.items.length > 0) {
                                            const $optionsContainer = $field.find('.pfb-field-options');
                                            $optionsContainer.empty();

                                            options.items.forEach(function(item, index) {
                                                const $option = $('<div class="pfb-field-option">');

                                                // Ensure we have valid values
                                                const label = item.label || 'Option';
                                                const value = item.value || label || '0';
                                                const variable = item.variable || '';

                                                console.log(`Option ${index}: label=${label}, value=${value}, variable=${variable}`);

                                                $option.append('<input type="text" class="pfb-form-control pfb-option-label" placeholder="Label" value="' + label + '">');

                                                // Create value container with dynamic value button
                                                const $valueContainer = $('<div class="pfb-option-value-container">');
                                                $valueContainer.append('<input type="text" class="pfb-form-control pfb-option-value" placeholder="Value" value="' + value + '">');
                                                $valueContainer.append('<button type="button" class="pfb-dynamic-value-button pfb-select-dynamic-value"><span class="dashicons dashicons-database"></span></button>');
                                                $valueContainer.append('<input type="hidden" class="pfb-option-variable" value="' + variable + '">');

                                                $option.append($valueContainer);

                                                // If the value is a variable placeholder, update the value input
                                                if (variable) {
                                                    $valueContainer.find('.pfb-option-value').val('{' + variable + '}');
                                                }

                                                if (index === 0) {
                                                    $option.append('<button type="button" class="pfb-btn pfb-btn-secondary pfb-add-option">+</button>');
                                                } else {
                                                    $option.append('<button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option">-</button>');
                                                }

                                                $optionsContainer.append($option);
                                            });
                                        }

                                        if (options.variable) {
                                            $field.find('.pfb-field-variable-input').val(options.variable);
                                        }
                                        break;

                                    case 'subtotal':
                                        console.log('Loading subtotal field options:', options);

                                        // Set empty value display
                                        if (options.empty_value) {
                                            $field.find('.pfb-subtotal-empty-value-input').val(options.empty_value);
                                        }

                                        // Load subtotal lines
                                        if (options.lines && options.lines.length > 0) {
                                            console.log('Loading subtotal lines:', options.lines);

                                            // Clear existing lines
                                            const $linesContainer = $field.find('.pfb-subtotal-lines');
                                            $linesContainer.empty();

                                            // Add each line
                                            options.lines.forEach(function(line, index) {
                                                console.log(`Loading subtotal line ${index}:`, line);

                                                const $line = $('<div class="pfb-subtotal-line">');

                                                // Add label input
                                                $line.append('<input type="text" class="pfb-form-control pfb-subtotal-line-label" placeholder="Line Label" value="' + (line.label || 'Line ' + (index + 1)) + '">');

                                                // Add formula input container
                                                const $formulaContainer = $('<div class="pfb-formula-input-container">');
                                                $formulaContainer.append('<textarea class="pfb-form-control pfb-subtotal-line-formula" rows="2" placeholder="Formula">' + (line.formula || '') + '</textarea>');
                                                $formulaContainer.append('<button type="button" class="pfb-dynamic-value-button pfb-select-subtotal-formula"><span class="dashicons dashicons-editor-code"></span></button>');
                                                $line.append($formulaContainer);

                                                // Add add/remove button
                                                if (index === 0) {
                                                    $line.append('<button type="button" class="pfb-btn pfb-btn-secondary pfb-add-subtotal-line">+</button>');
                                                } else {
                                                    $line.append('<button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-subtotal-line">-</button>');
                                                }

                                                $linesContainer.append($line);
                                            });
                                        } else {
                                            console.log('No subtotal lines found, using default');

                                            // If no lines are defined, make sure we have at least one default line
                                            const $linesContainer = $field.find('.pfb-subtotal-lines');

                                            // Check if there's already a line
                                            if ($linesContainer.find('.pfb-subtotal-line').length === 0) {
                                                console.log('Adding default subtotal line');

                                                const $line = $('<div class="pfb-subtotal-line">');

                                                // Add label input
                                                $line.append('<input type="text" class="pfb-form-control pfb-subtotal-line-label" placeholder="Line Label" value="Line 1">');

                                                // Add formula input container
                                                const $formulaContainer = $('<div class="pfb-formula-input-container">');
                                                $formulaContainer.append('<textarea class="pfb-form-control pfb-subtotal-line-formula" rows="2" placeholder="Formula"></textarea>');
                                                $formulaContainer.append('<button type="button" class="pfb-dynamic-value-button pfb-select-subtotal-formula"><span class="dashicons dashicons-editor-code"></span></button>');
                                                $line.append($formulaContainer);

                                                // Add add button
                                                $line.append('<button type="button" class="pfb-btn pfb-btn-secondary pfb-add-subtotal-line">+</button>');

                                                $linesContainer.append($line);
                                            }
                                        }
                                        break;

                                    case 'total':
                                        if (options.formula) {
                                            $field.find('.pfb-field-formula-input').val(options.formula);
                                        }
                                        break;
                                }
                            }

                                // Add the field to the form
                                $('.pfb-form-fields').append($field);
                                console.log('PFB Admin: Field added to form successfully');

                                // Move to the next field
                                fieldIndex++;
                                setTimeout(processNextField, 10);
                            } catch (error) {
                                console.error('PFB Admin: Error processing field:', error);
                                fieldIndex++;
                                setTimeout(processNextField, 10);
                            }
                        }

                        // Start processing fields
                        processNextField();
                    } else {
                        console.log('PFB Admin: No fields to process');
                        $('.pfb-form-fields').html('<div class="pfb-empty-form-message">No fields found. Add fields using the buttons above.</div>');
                    }
                } else {
                    console.error('PFB Admin: Failed to load form data:', response);
                    showNotification('error', 'Failed to load form data.');
                    $('.pfb-form-fields').html('<div class="pfb-empty-form-message">Failed to load form data. Please try again.</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('PFB Admin: AJAX error loading form:', status, error);
                showNotification('error', 'An error occurred while loading the form: ' + error);
                $('.pfb-form-fields').html('<div class="pfb-empty-form-message">Error loading form data. Please try again.</div>');
            },
            complete: function() {
                $('#pfb-form-editor-form').removeClass('loading');

                // Initialize conditional logic for all fields after form is loaded
                if (typeof window.PFBConditionalLogic !== 'undefined' && typeof window.PFBConditionalLogic.init === 'function') {
                    console.log('PFB Admin: Initializing conditional logic for all fields after form load');
                    window.PFBConditionalLogic.init();
                }
            }
        });
    }

    /**
     * Initialize price variables functionality.
     */
    function initPriceVariables() {
        // Implementation will be added in the next phase
    }

    /**
     * Initialize currencies functionality.
     */
    function initCurrencies() {
        // Implementation will be added in the next phase
    }

    /**
     * Initialize settings functionality.
     */
    function initSettings() {
        // Implementation will be added in the next phase
    }

    /**
     * Get field options from a field element
     */
    function getFieldOptions($field) {
        const options = [];

        $field.find('.pfb-field-option').each(function() {
            const $option = $(this);
            const label = $option.find('.pfb-option-label').val();
            const value = $option.find('.pfb-option-value').val();
            const variable = $option.find('.pfb-option-variable').val();

            options.push({
                label: label || 'Option',
                value: value || '0',
                variable: variable || ''
            });
        });

        return options;
    }

    /**
     * Validate a formula for syntax errors.
     */
    function validateFormula(formula) {
        // Empty formula is valid (will return 0)
        if (!formula.trim()) {
            return true;
        }

        // Check for balanced parentheses
        const openCount = (formula.match(/\(/g) || []).length;
        const closeCount = (formula.match(/\)/g) || []).length;

        if (openCount !== closeCount) {
            console.warn('Unbalanced parentheses in formula:', formula);
            return false;
        }

        // Check for proper nesting of parentheses
        try {
            let level = 0;
            for (let i = 0; i < formula.length; i++) {
                if (formula[i] === '(') {
                    level++;
                } else if (formula[i] === ')') {
                    level--;
                    if (level < 0) {
                        console.warn('Improper nesting of parentheses in formula:', formula);
                        return false;
                    }
                }
            }
        } catch (e) {
            console.error('Error validating formula:', e);
            return false;
        }

        // Check for proper function syntax
        const functionPattern = /\b(ceil|floor|round|min|max)\s*\(/g;
        let match;
        while ((match = functionPattern.exec(formula)) !== null) {
            const funcPos = match.index;
            const openParenPos = funcPos + match[0].length - 1;

            // Find the matching closing parenthesis
            let level = 1;
            let closeParenPos = -1;

            for (let i = openParenPos + 1; i < formula.length; i++) {
                if (formula[i] === '(') {
                    level++;
                } else if (formula[i] === ')') {
                    level--;
                    if (level === 0) {
                        closeParenPos = i;
                        break;
                    }
                }
            }

            if (closeParenPos === -1) {
                console.warn('Missing closing parenthesis for function in formula:', formula);
                return false;
            }
        }

        // The following checks are warnings but not errors

        // Check for consecutive operators
        if (/[\+\-\*\/]{2,}/.test(formula)) {
            console.warn('Warning: Consecutive operators in formula:', formula);
        }

        // Check for empty parentheses
        if (/\(\s*\)/.test(formula)) {
            console.warn('Warning: Empty parentheses in formula:', formula);
        }

        // Check for missing operands
        if (/[\+\-\*\/]\s*$/.test(formula)) {
            console.warn('Warning: Missing operand at the end of formula:', formula);
        }

        // Check for function calls without arguments
        if (/\b(ceil|floor|round|min|max)\s*\(\s*\)/.test(formula)) {
            console.warn('Warning: Function call without arguments in formula:', formula);
        }

        return true;
    }

    /**
     * Show a warning message in the formula builder.
     */
    function showFormulaWarning($field, message) {
        // Check if warning element already exists
        let $warning = $field.find('.pfb-formula-message');

        if (!$warning.length) {
            // Create warning element
            $warning = $('<div class="pfb-formula-message pfb-formula-warning"></div>');
            $field.find('.pfb-formula-input-container').append($warning);
        } else {
            // Make sure it has the warning class
            $warning.removeClass('pfb-formula-info').addClass('pfb-formula-warning');
        }

        // Set message and show
        $warning.text(message).fadeIn();

        // Hide after 3 seconds
        setTimeout(function() {
            $warning.fadeOut();
        }, 3000);
    }

    /**
     * Show an informational message in the formula builder.
     */
    function showFormulaInfo($field, message) {
        // Check if info element already exists
        let $info = $field.find('.pfb-formula-message');

        if (!$info.length) {
            // Create info element
            $info = $('<div class="pfb-formula-message pfb-formula-info"></div>');
            $field.find('.pfb-formula-input-container').append($info);
        } else {
            // Make sure it has the info class
            $info.removeClass('pfb-formula-warning').addClass('pfb-formula-info');
        }

        // Set message and show
        $info.text(message).fadeIn();

        // Hide after 5 seconds
        setTimeout(function() {
            $info.fadeOut();
        }, 5000);
    }

    /**
     * Update field preview based on the options.
     */
    function updateFieldPreview($field) {
        const fieldType = $field.data('type');
        const $preview = $field.find('.pfb-field-preview');

        // Get all options
        const options = [];
        $field.find('.pfb-field-option').each(function() {
            const label = $(this).find('.pfb-option-label').val() || 'Option';
            options.push(label);
        });

        if (options.length === 0) {
            return;
        }

        // Create preview HTML based on field type
        let previewHtml = '';

        switch (fieldType) {
            case 'dropdown':
                previewHtml = '<select class="pfb-form-control" disabled>';
                options.forEach(function(label) {
                    previewHtml += '<option>' + label + '</option>';
                });
                previewHtml += '</select>';
                break;

            case 'radio':
                previewHtml = '<div>';
                options.forEach(function(label) {
                    previewHtml += '<label><input type="radio" name="radio_preview" disabled> ' + label + '</label><br>';
                });
                previewHtml += '</div>';
                break;

            case 'checkbox':
                previewHtml = '<div>';
                options.forEach(function(label) {
                    previewHtml += '<label><input type="checkbox" disabled> ' + label + '</label><br>';
                });
                previewHtml += '</div>';
                break;
        }

        // Update preview
        if (previewHtml) {
            $preview.html(previewHtml);
        }
    }

    /**
     * Get conditional logic data from a field element.
     */
    function getConditionalLogicData($field) {
        console.log('PFB Admin: Getting conditional logic data for field:', $field.attr('id'));

        // Check if conditional logic is enabled
        if (!$field.find('.pfb-enable-conditional-logic').is(':checked')) {
            console.log('PFB Admin: Conditional logic is not enabled for this field');
            return {
                enabled: false,
                logic_type: 'all',
                rules: []
            };
        }

        // Get logic type
        const logicType = $field.find('.pfb-logic-type').val() || 'all';
        console.log('PFB Admin: Logic type:', logicType);

        // Get rules
        const rules = [];
        $field.find('.pfb-conditional-rule').each(function() {
            const $rule = $(this);
            const ruleField = $rule.find('.pfb-rule-field').val();
            const ruleOperator = $rule.find('.pfb-rule-operator').val();
            const ruleValue = $rule.find('.pfb-rule-value').val();

            // Get the field name from the selected option
            const $selectedOption = $rule.find('.pfb-rule-field option:selected');
            const fieldName = $selectedOption.data('field-name') || '';

            console.log('PFB Admin: Rule field:', ruleField, 'field name:', fieldName);

            if (ruleField && ruleOperator) {
                // Make sure the field ID is a valid value, not 0 or empty
                if (!ruleField || ruleField === '0' || ruleField === 0) {
                    console.error('PFB Admin: Invalid field ID in conditional rule:', ruleField);
                    // Try to find the field by name
                    if (fieldName) {
                        // Look for a field with this name
                        $('.pfb-field').each(function() {
                            const $f = $(this);
                            const fName = $f.find('.pfb-field-name-input').val();
                            if (fName === fieldName) {
                                const fId = $f.attr('id');
                                console.log('PFB Admin: Found field by name:', fieldName, 'ID:', fId);
                                ruleField = fId;
                                return false; // Break the loop
                            }
                        });
                    }
                }

                rules.push({
                    field: ruleField,
                    field_name: fieldName,
                    operator: ruleOperator,
                    value: ruleValue || ''
                });
            }
        });

        console.log('PFB Admin: Conditional logic rules:', rules);

        const result = {
            enabled: true,
            logic_type: logicType,
            rules: rules
        };

        console.log('PFB Admin: Final conditional logic data:', result);

        return result;
    }

    /**
     * Show notification message.
     */
    function showNotification(type, message) {
        const $notification = $('<div class="pfb-notification pfb-notification-' + type + '">' + message + '</div>');

        $('.pfb-admin-content').prepend($notification);

        setTimeout(function() {
            $notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    // Expose functions to the global PFBAdmin object
    window.PFBAdmin.saveForm = saveForm;
    window.PFBAdmin.getFormData = getFormData;

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('PFB Admin: Document ready, initializing...');
        init();

        // Add a direct click handler to the save buttons for debugging
        console.log('PFB Admin: Adding direct click handlers to save buttons');
        $('#pfb-save-form, #pfb-save-form-bottom').off('click.debug').on('click.debug', function(e) {
            console.log('PFB Admin: Save button clicked (direct handler):', this.id);
        });
    });

})(jQuery);
