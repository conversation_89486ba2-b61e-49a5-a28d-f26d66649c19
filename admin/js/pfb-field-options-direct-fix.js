/**
 * Field Options Direct Fix
 * 
 * This script directly fixes the field options saving issue by ensuring
 * that dropdown/select field options are properly collected and saved.
 */
(function($) {
    'use strict';
    
    /**
     * Field Options Direct Fix
     */
    var PFB_FieldOptionsDirectFix = {
        
        /**
         * Initialize the fix
         */
        init: function() {
            console.log('PFB Field Options Direct Fix: Initializing');
            
            // Override the save form function completely
            this.overrideSaveFunction();
            
            // Ensure field options UI is created when fields are added
            this.ensureFieldOptionsUI();
        },
        
        /**
         * Override save function
         */
        overrideSaveFunction: function() {
            // Override all save button clicks
            $(document).off('click', '#pfb-save-form, #pfb-save-form-bottom');
            $(document).on('click', '#pfb-save-form, #pfb-save-form-bottom', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                console.log('PFB Field Options Direct Fix: Save button clicked');
                PFB_FieldOptionsDirectFix.saveFormWithOptions();
                
                return false;
            });
        },
        
        /**
         * Ensure field options UI is created
         */
        ensureFieldOptionsUI: function() {
            // Monitor when fields are added
            $(document).on('click', '.pfb-add-field-btn', function() {
                const fieldType = $(this).data('type');
                console.log('PFB Field Options Direct Fix: Adding field of type:', fieldType);
                
                // Wait for field to be created, then ensure options UI
                setTimeout(function() {
                    PFB_FieldOptionsDirectFix.ensureOptionsUIForLastField();
                }, 1000);
            });
            
            // Monitor when field settings are opened
            $(document).on('click', '.pfb-field-edit', function() {
                const $field = $(this).closest('.pfb-field');
                const fieldType = $field.data('type');
                
                if (fieldType === 'dropdown' || fieldType === 'select' || fieldType === 'radio' || fieldType === 'checkbox') {
                    setTimeout(function() {
                        PFB_FieldOptionsDirectFix.ensureOptionsUIForField($field);
                    }, 100);
                }
            });
        },
        
        /**
         * Ensure options UI for the last field
         */
        ensureOptionsUIForLastField: function() {
            const $lastField = $('.pfb-field').last();
            this.ensureOptionsUIForField($lastField);
        },
        
        /**
         * Ensure options UI for a specific field
         */
        ensureOptionsUIForField: function($field) {
            const fieldType = $field.data('type');
            
            if (fieldType === 'dropdown' || fieldType === 'select' || fieldType === 'radio' || fieldType === 'checkbox') {
                console.log('PFB Field Options Direct Fix: Ensuring options UI for field:', fieldType);
                
                let $optionsContainer = $field.find('.pfb-field-options');
                
                // If options container doesn't exist, create it
                if ($optionsContainer.length === 0) {
                    console.log('PFB Field Options Direct Fix: Creating options container');
                    
                    const optionsHTML = `
                        <div class="pfb-form-group">
                            <label>Options</label>
                            <div class="pfb-field-options-header" style="display: flex; margin-bottom: 5px;">
                                <div style="flex: 1; font-weight: bold;">Label</div>
                                <div style="flex: 1; font-weight: bold;">Value</div>
                                <div style="width: 40px;"></div>
                            </div>
                            <div class="pfb-field-options">
                                <!-- Options will be added here -->
                            </div>
                            <button type="button" class="pfb-add-option pfb-btn pfb-btn-secondary" style="margin-top: 5px;">Add Option</button>
                        </div>
                    `;
                    
                    // Find the field settings and add options UI
                    const $fieldSettings = $field.find('.pfb-field-settings');
                    if ($fieldSettings.length > 0) {
                        // Add before the last form group (which should be the save/cancel buttons)
                        const $lastGroup = $fieldSettings.find('.pfb-form-group').last();
                        if ($lastGroup.length > 0) {
                            $lastGroup.before(optionsHTML);
                        } else {
                            $fieldSettings.append(optionsHTML);
                        }
                        
                        $optionsContainer = $field.find('.pfb-field-options');
                    }
                }
                
                // Ensure at least one option exists
                if ($optionsContainer.length > 0 && $optionsContainer.find('.pfb-field-option').length === 0) {
                    console.log('PFB Field Options Direct Fix: Adding default option');
                    
                    const defaultOptionHTML = `
                        <div class="pfb-field-option" style="display: flex; margin-bottom: 5px;">
                            <input type="text" class="pfb-form-control pfb-option-label" placeholder="Label" value="Option 1" style="flex: 1; margin-right: 5px;">
                            <input type="text" class="pfb-form-control pfb-option-value" placeholder="Value" value="option_1" style="flex: 1; margin-right: 5px;">
                            <button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option" style="width: 40px;">-</button>
                        </div>
                    `;
                    
                    $optionsContainer.append(defaultOptionHTML);
                }
            }
        },
        
        /**
         * Save form with proper options handling
         */
        saveFormWithOptions: function() {
            console.log('PFB Field Options Direct Fix: Starting form save with options');
            
            // Show saving indicator
            const $savingIndicator = $('<div id="pfb-saving-indicator" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 5px; z-index: 10000;">Saving form with field options...</div>')
                .appendTo('body');
            
            // Disable save buttons
            $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', true).text('Saving...');
            
            try {
                // Collect form data with proper options handling
                const formData = this.collectFormDataWithOptions();
                
                console.log('PFB Field Options Direct Fix: Form data collected:', formData);
                
                // Save via AJAX using the complete handler
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'pfb_save_form_complete',
                        nonce: pfb_data.nonce,
                        form_data: formData
                    },
                    success: function(response) {
                        console.log('PFB Field Options Direct Fix: Save response:', response);
                        
                        if (response.success) {
                            $savingIndicator.text('Form saved successfully!').css('background', 'rgba(0,128,0,0.8)');
                            
                            // Get form ID from URL or response
                            const urlParams = new URLSearchParams(window.location.search);
                            const formId = urlParams.get('form_id');
                            
                            // Redirect to edit page if new form
                            if (!formId && response.data && response.data.form_id) {
                                $savingIndicator.text('Form saved successfully! Redirecting...');
                                setTimeout(function() {
                                    window.location.href = 'admin.php?page=pfb-form-editor&form_id=' + response.data.form_id;
                                }, 1000);
                            } else {
                                // Remove the indicator after a delay
                                setTimeout(function() {
                                    $savingIndicator.fadeOut(500, function() {
                                        $(this).remove();
                                    });
                                }, 2000);
                            }
                        } else {
                            const errorMessage = response.data && response.data.message
                                ? response.data.message
                                : 'Unknown error';
                            
                            console.error('PFB Field Options Direct Fix: Error response:', errorMessage);
                            $savingIndicator.text('Error saving form: ' + errorMessage).css('background', 'rgba(200,0,0,0.8)');
                            
                            // Remove the indicator after a delay
                            setTimeout(function() {
                                $savingIndicator.fadeOut(500, function() {
                                    $(this).remove();
                                });
                            }, 5000);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('PFB Field Options Direct Fix: AJAX error:', status, error);
                        console.error('PFB Field Options Direct Fix: Response text:', xhr.responseText);
                        
                        $savingIndicator.text('Error saving form: ' + error).css('background', 'rgba(200,0,0,0.8)');
                        
                        // Remove the indicator after a delay
                        setTimeout(function() {
                            $savingIndicator.fadeOut(500, function() {
                                $(this).remove();
                            });
                        }, 5000);
                    },
                    complete: function() {
                        // Re-enable save buttons
                        $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');
                    }
                });
            } catch (error) {
                console.error('PFB Field Options Direct Fix: Error collecting form data:', error);
                $savingIndicator.text('Error collecting form data: ' + error.message).css('background', 'rgba(200,0,0,0.8)');
                
                // Re-enable save buttons
                $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');
                
                // Remove the indicator after a delay
                setTimeout(function() {
                    $savingIndicator.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 5000);
            }
        },
        
        /**
         * Collect form data with proper options handling
         */
        collectFormDataWithOptions: function() {
            console.log('PFB Field Options Direct Fix: Collecting form data with options');
            
            // Get form ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const formId = urlParams.get('form_id');
            
            // Basic form data
            const formData = {
                id: formId || null,
                title: $('#form_title').val() || 'Untitled Form',
                description: $('#form_description').val() || '',
                status: $('#form_status').val() || 'draft',
                settings: {
                    template: $('#form_template').val() || 'default',
                    show_currency_selector: $('#form_show_currency_selector').val() || '1',
                    submit_button_text: $('#form_submit_button_text').val() || 'Calculate Price'
                },
                fields: []
            };
            
            // Collect fields
            $('.pfb-field').each(function(index) {
                const $field = $(this);
                const fieldId = $field.attr('id');
                const fieldType = $field.data('type');
                
                console.log(`PFB Field Options Direct Fix: Processing field #${index + 1}: ${fieldType} (${fieldId})`);
                
                if (!fieldType) {
                    console.log(`PFB Field Options Direct Fix: Skipping field #${index + 1}, no type specified`);
                    return;
                }
                
                // Basic field data
                const field = {
                    id: fieldId,
                    type: fieldType,
                    label: $field.find('.pfb-field-label-input').val() || `Field ${index + 1}`,
                    name: $field.find('.pfb-field-name-input').val() || `field_${index + 1}`,
                    required: $field.find('.pfb-field-required-input').is(':checked'),
                    width: $field.find('.pfb-field-width-input').val() || '100',
                    options: {}
                };
                
                // Collect field options for choice fields
                if (fieldType === 'dropdown' || fieldType === 'select' || fieldType === 'radio' || fieldType === 'checkbox') {
                    field.options.items = [];
                    
                    $field.find('.pfb-field-option').each(function() {
                        const $option = $(this);
                        const label = $option.find('.pfb-option-label').val();
                        const value = $option.find('.pfb-option-value').val();
                        const variable = $option.find('.pfb-option-variable').val() || '';
                        
                        if (label && value) {
                            field.options.items.push({
                                label: label,
                                value: value,
                                variable: variable
                            });
                        }
                    });
                    
                    // Ensure at least one option exists
                    if (field.options.items.length === 0) {
                        field.options.items.push({
                            label: 'Option 1',
                            value: 'option_1',
                            variable: ''
                        });
                    }
                    
                    console.log(`PFB Field Options Direct Fix: Field ${fieldId} has ${field.options.items.length} options:`, field.options.items);
                }
                
                // Add field to form data
                formData.fields.push(field);
            });
            
            console.log('PFB Field Options Direct Fix: Complete form data collected:', formData);
            return formData;
        }
    };
    
    // Initialize on document ready
    $(document).ready(function() {
        // Wait a bit to ensure other scripts are loaded
        setTimeout(function() {
            PFB_FieldOptionsDirectFix.init();
        }, 1000);
    });
    
})(jQuery);
