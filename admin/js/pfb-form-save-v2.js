/**
 * Form Save V2
 * A complete rewrite of the form saving functionality
 */
(function($) {
    'use strict';

    // Wait for document ready
    $(document).ready(function() {
        console.log('PFB Form Save V2: Initializing...');

        // Wait a moment to ensure all other scripts have loaded
        setTimeout(initFormSaveV2, 1000);
    });

    /**
     * Initialize the form save functionality
     */
    function initFormSaveV2() {
        console.log('PFB Form Save V2: Setting up save handlers');

        // Remove ALL existing click handlers from both save buttons
        $('#pfb-save-form, #pfb-save-form-bottom').off('click');

        // Add new save handler to top button
        $('#pfb-save-form').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('PFB Form Save V2: Top save button clicked');

            // Collect form data
            const formData = collectFormData();

            // Save the form
            saveForm(formData);

            return false;
        });

        // Make bottom button trigger top button
        $('#pfb-save-form-bottom').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('PFB Form Save V2: Bottom save button clicked');

            // Trigger top save button
            $('#pfb-save-form').trigger('click');

            return false;
        });

        // Add visual indicator
        $('<div id="pfb-form-save-v2-indicator" style="position: fixed; top: 32px; right: 0; background: #4CAF50; color: white; padding: 5px 10px; z-index: 9999;">Form Save V2 Active</div>')
            .appendTo('body');

        console.log('PFB Form Save V2: Save handlers set up successfully');
    }

    /**
     * Collect form data
     */
    function collectFormData() {
        console.log('PFB Form Save V2: Collecting form data');

        // Basic form data
        const formData = {
            title: $('#form_title').val() || 'Untitled Form',
            description: $('#form_description').val() || '',
            status: $('#form_status').val() || 'publish',
            settings: {
                template: $('#form_template').val() || 'default',
                show_currency_selector: $('#form_show_currency_selector').is(':checked') ? '1' : '0',
                submit_button_text: $('#form_submit_button_text').val() || 'Submit'
            },
            fields: []
        };

        // Get form ID if editing
        const urlParams = new URLSearchParams(window.location.search);
        const formId = urlParams.get('form_id');

        if (formId) {
            formData.id = formId;
        }

        // Get all fields
        $('.pfb-form-fields .pfb-field').each(function(index) {
            const $field = $(this);
            const fieldId = $field.attr('id');
            const fieldType = $field.data('type');

            console.log(`PFB Form Save V2: Processing field #${index + 1}, ID: ${fieldId}, Type: ${fieldType}`);

            // Skip fields without type
            if (!fieldType) {
                console.log(`PFB Form Save V2: Skipping field #${index + 1}, no type specified`);
                return;
            }

            // Basic field data
            const field = {
                id: fieldId,
                type: fieldType,
                label: $field.find('.pfb-field-label-input').val() || `Field ${index + 1}`,
                name: $field.find('.pfb-field-name-input').val() || `field_${index + 1}`,
                required: $field.find('.pfb-field-required-input').is(':checked'),
                width: $field.find('.pfb-field-width-input').val() || '100',
                options: {}
            };

            // Collect field options based on field type
            switch (fieldType) {
                case 'select':
                case 'radio':
                case 'checkbox':
                    field.options.items = [];

                    $field.find('.pfb-field-option').each(function() {
                        const $option = $(this);
                        field.options.items.push({
                            label: $option.find('.pfb-option-label').val() || 'Option',
                            value: $option.find('.pfb-option-value').val() || 'option',
                            variable: $option.find('.pfb-option-variable').val() || ''
                        });
                    });

                    // Ensure at least one option exists
                    if (field.options.items.length === 0) {
                        field.options.items.push({
                            label: 'Option 1',
                            value: 'option_1',
                            variable: ''
                        });
                    }
                    break;

                case 'number':
                    field.options.min = $field.find('.pfb-field-min').val() || '0';
                    field.options.max = $field.find('.pfb-field-max').val() || '100';
                    field.options.step = $field.find('.pfb-field-step').val() || '1';
                    break;

                case 'slider':
                    field.options.min = parseInt($field.find('.pfb-slider-min-input').val() || '0');
                    field.options.max = parseInt($field.find('.pfb-slider-max-input').val() || '100');
                    field.options.step = parseInt($field.find('.pfb-slider-step-input').val() || '1');
                    field.options.default = parseInt($field.find('.pfb-slider-default-input').val() || '50');
                    break;

                case 'price':
                case 'total':
                    field.options.formula = $field.find('.pfb-field-formula-input').val() || '';

                    if (fieldType === 'price') {
                        field.options.price = $field.find('.pfb-field-price').val() || '0';
                    }
                    break;

                case 'subtotal':
                    field.options.lines = [];

                    $field.find('.pfb-subtotal-line').each(function() {
                        const $line = $(this);
                        field.options.lines.push({
                            label: $line.find('.pfb-subtotal-line-label, .pfb-line-label').val() || 'Line',
                            formula: $line.find('.pfb-subtotal-line-formula, .pfb-line-formula').val() || ''
                        });
                    });

                    // Ensure at least one line exists
                    if (field.options.lines.length === 0) {
                        field.options.lines.push({
                            label: 'Line 1',
                            formula: ''
                        });
                    }

                    field.options.empty_value = $field.find('.pfb-subtotal-empty-value-input').val() || '---';
                    break;

                case 'text':
                    // Handle hidden field properties
                    if ($field.find('.pfb-field-hidden-input').is(':checked')) {
                        field.hidden = true;
                        field.variable = $field.find('.pfb-hidden-field-variable').val() || '';
                        field.default_value = $field.find('.pfb-hidden-field-value').val() || '';
                    }
                    break;
            }

            // Common options
            if ($field.find('.pfb-field-placeholder').length) {
                field.options.placeholder = $field.find('.pfb-field-placeholder').val() || '';
            }

            if ($field.find('.pfb-field-default').length) {
                field.options.default = $field.find('.pfb-field-default').val() || '';
            }

            if ($field.find('.pfb-field-description').length) {
                field.options.description = $field.find('.pfb-field-description').val() || '';
            }

            // Collect conditional logic
            if ($field.find('.pfb-enable-conditional-logic').length) {
                const isEnabled = $field.find('.pfb-enable-conditional-logic').is(':checked');

                field.conditional_logic = {
                    enabled: isEnabled,
                    logic_type: $field.find('.pfb-logic-type').val() || 'all',
                    rules: []
                };

                if (isEnabled) {
                    $field.find('.pfb-conditional-rule').each(function() {
                        const $rule = $(this);
                        const ruleField = $rule.find('.pfb-rule-field').val();
                        const ruleOperator = $rule.find('.pfb-rule-operator').val();
                        const ruleValue = $rule.find('.pfb-rule-value').val();

                        if (ruleField && ruleOperator) {
                            field.conditional_logic.rules.push({
                                field: ruleField,
                                field_name: $rule.find('.pfb-rule-field option:selected').data('field-name') || '',
                                operator: ruleOperator,
                                value: ruleValue || ''
                            });
                        }
                    });
                }
            }

            // Add field to form data
            formData.fields.push(field);
        });

        console.log('PFB Form Save V2: Form data collected:', formData);
        return formData;
    }

    /**
     * Save the form
     */
    function saveForm(formData) {
        console.log('PFB Form Save V2: Saving form data');

        // Show saving indicator
        const $savingIndicator = $('<div id="pfb-saving-indicator" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 5px; z-index: 10000;">Saving form...</div>')
            .appendTo('body');

        // Disable save buttons
        $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', true).text('Saving...');

        // Log the form data for debugging
        console.log('PFB Form Save V2: Form data to save:', formData);

        // Save via AJAX
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'pfb_save_form_v2',
                nonce: pfb_data.nonce,
                form_data: formData
            },
            success: function(response) {
                console.log('PFB Form Save V2: Save response:', response);

                if (response.success) {
                    // Show success message
                    $savingIndicator.text('Form saved successfully!').css('background', 'rgba(0,128,0,0.8)');

                    // Get form ID from URL or response
                    const urlParams = new URLSearchParams(window.location.search);
                    const formId = urlParams.get('form_id');

                    // Redirect to edit page if new form
                    if (!formId && response.data && response.data.form_id) {
                        $savingIndicator.text('Form saved successfully! Redirecting...');
                        setTimeout(function() {
                            window.location.href = 'admin.php?page=pfb-form-editor&form_id=' + response.data.form_id;
                        }, 1000);
                    } else {
                        // Remove the indicator after a delay
                        setTimeout(function() {
                            $savingIndicator.fadeOut(500, function() {
                                $(this).remove();
                            });
                        }, 2000);
                    }
                } else {
                    const errorMessage = response.data && response.data.message
                        ? response.data.message
                        : 'Unknown error';

                    console.error('PFB Form Save V2: Error response:', errorMessage);
                    $savingIndicator.text('Error saving form: ' + errorMessage).css('background', 'rgba(200,0,0,0.8)');

                    // Remove the indicator after a delay
                    setTimeout(function() {
                        $savingIndicator.fadeOut(500, function() {
                            $(this).remove();
                        });
                    }, 5000);
                }
            },
            error: function(xhr, status, error) {
                console.error('PFB Form Save V2: AJAX error:', status, error);
                console.error('PFB Form Save V2: Response text:', xhr.responseText);

                $savingIndicator.text('Error saving form: ' + error).css('background', 'rgba(200,0,0,0.8)');

                // Remove the indicator after a delay
                setTimeout(function() {
                    $savingIndicator.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 5000);
            },
            complete: function() {
                // Re-enable save buttons
                $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');
            }
        });
    }
})(jQuery);
