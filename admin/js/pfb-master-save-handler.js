/**
 * COMPLETE Master Save & Load Handler for Price Form Builder
 *
 * This is the ONLY save and load handler that should be used.
 * It completely replaces all other conflicting save/load scripts.
 *
 * This script handles EVERYTHING:
 * - All field types: text, number, slider, dropdown, radio, checkbox, subtotal, total
 * - All field settings: label, name, required, width, placeholder, default values
 * - Field options: for choice fields with complete items array
 * - Slider settings: min, max, step, default
 * - Total field settings: formula, decimal places
 * - Conditional logic: enabled, logic_type, rules with field, operator, value
 * - Form settings: template, currency, show_currency_selector, submit_button_text, success_message
 * - Form loading: proper field reconstruction with all options and settings
 * - Translations: multi-language support
 *
 * @since 1.0.0
 */
(function($) {
    'use strict';

    console.log('PFB Master Save Handler: Loading');

    /**
     * Master Save Handler Object
     */
    var PFB_MasterSaveHandler = {

        /**
         * Initialize the master save handler
         */
        init: function() {
            console.log('PFB Master Save Handler: Initializing');

            // Wait for document ready and other scripts to load
            $(document).ready(function() {
                // Wait a bit more to ensure all other scripts are loaded
                setTimeout(function() {
                    PFB_MasterSaveHandler.setup();
                }, 2000);
            });
        },

        /**
         * Setup the master save handler
         */
        setup: function() {
            console.log('PFB Master Save Handler: Setting up');

            // COMPLETELY OVERRIDE ALL SAVE HANDLERS
            this.overrideSaveHandlers();

            // Setup field options UI management
            this.setupFieldOptionsUI();

            // Setup form loading
            this.setupFormLoading();

            // Add diagnostic tools
            this.addDiagnosticTools();

            console.log('PFB Master Save Handler: Setup complete');
        },

        /**
         * Override all save handlers
         */
        overrideSaveHandlers: function() {
            console.log('PFB Master Save Handler: Overriding all save handlers');

            // Remove ALL existing save handlers
            $(document).off('click', '#pfb-save-form, #pfb-save-form-bottom');
            $('#pfb-save-form, #pfb-save-form-bottom').off('click');

            // Add our master save handler
            $(document).on('click', '#pfb-save-form, #pfb-save-form-bottom', function(e) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();

                console.log('PFB Master Save Handler: Save button clicked');
                PFB_MasterSaveHandler.saveForm();

                return false;
            });

            // Override any existing PFBAdmin.saveForm function
            if (typeof window.PFBAdmin !== 'undefined') {
                window.PFBAdmin.originalSaveForm = window.PFBAdmin.saveForm;
                window.PFBAdmin.saveForm = function() {
                    console.log('PFB Master Save Handler: PFBAdmin.saveForm called, redirecting to master handler');
                    PFB_MasterSaveHandler.saveForm();
                };
            }
        },

        /**
         * Setup field options UI management
         */
        setupFieldOptionsUI: function() {
            console.log('PFB Master Save Handler: Setting up field options UI management');

            // Monitor when dropdown fields are added
            $(document).on('click', '.pfb-add-field-btn[data-type="dropdown"]', function() {
                console.log('PFB Master Save Handler: Dropdown field being added');

                // Wait for the field to be created
                setTimeout(function() {
                    PFB_MasterSaveHandler.ensureDropdownOptionsUI();
                }, 1000);
            });

            // Monitor when field settings are opened
            $(document).on('click', '.pfb-field-edit', function() {
                const $field = $(this).closest('.pfb-field');
                const fieldType = $field.data('type');

                if (fieldType === 'dropdown') {
                    setTimeout(function() {
                        PFB_MasterSaveHandler.ensureOptionsUIForField($field);
                    }, 100);
                }
            });

            // Handle add option button
            $(document).on('click', '.pfb-add-option', function(e) {
                e.preventDefault();
                const $field = $(this).closest('.pfb-field');
                PFB_MasterSaveHandler.addOptionToField($field);
            });

            // Handle remove option button
            $(document).on('click', '.pfb-remove-option', function(e) {
                e.preventDefault();
                $(this).closest('.pfb-field-option').remove();
            });
        },

        /**
         * Ensure dropdown options UI exists for the last field
         */
        ensureDropdownOptionsUI: function() {
            const $lastField = $('.pfb-field').last();
            this.ensureOptionsUIForField($lastField);
        },

        /**
         * Ensure options UI exists for a specific field
         */
        ensureOptionsUIForField: function($field) {
            const fieldType = $field.data('type');

            if (fieldType !== 'dropdown') {
                return;
            }

            console.log('PFB Master Save Handler: Ensuring options UI for dropdown field');

            let $optionsContainer = $field.find('.pfb-field-options');

            // If options container doesn't exist, create it
            if ($optionsContainer.length === 0) {
                console.log('PFB Master Save Handler: Creating options container');

                const optionsHTML = `
                    <div class="pfb-form-group pfb-field-options-group">
                        <label>Options</label>
                        <div class="pfb-field-options-header" style="display: flex; margin-bottom: 5px;">
                            <div style="flex: 1; font-weight: bold;">Label</div>
                            <div style="flex: 1; font-weight: bold;">Value</div>
                            <div style="width: 40px;"></div>
                        </div>
                        <div class="pfb-field-options">
                            <!-- Options will be added here -->
                        </div>
                        <button type="button" class="pfb-add-option pfb-btn pfb-btn-secondary" style="margin-top: 5px;">Add Option</button>
                    </div>
                `;

                // Find the field settings and add options UI before the save/cancel buttons
                const $fieldSettings = $field.find('.pfb-field-settings');
                const $lastGroup = $fieldSettings.find('.pfb-form-group').last();

                if ($lastGroup.length > 0) {
                    $lastGroup.before(optionsHTML);
                } else {
                    $fieldSettings.append(optionsHTML);
                }

                $optionsContainer = $field.find('.pfb-field-options');
            }

            // Ensure at least one option exists
            if ($optionsContainer.length > 0 && $optionsContainer.find('.pfb-field-option').length === 0) {
                console.log('PFB Master Save Handler: Adding default option');
                this.addOptionToField($field, 'Option 1', 'option_1');
            }
        },

        /**
         * Add an option to a field
         */
        addOptionToField: function($field, label, value) {
            label = label || '';
            value = value || '';

            const $optionsContainer = $field.find('.pfb-field-options');

            if ($optionsContainer.length === 0) {
                console.log('PFB Master Save Handler: Options container not found');
                return;
            }

            const optionHTML = `
                <div class="pfb-field-option" style="display: flex; margin-bottom: 5px;">
                    <input type="text" class="pfb-form-control pfb-option-label" placeholder="Label" value="${label}" style="flex: 1; margin-right: 5px;">
                    <input type="text" class="pfb-form-control pfb-option-value" placeholder="Value" value="${value}" style="flex: 1; margin-right: 5px;">
                    <button type="button" class="pfb-btn pfb-btn-secondary pfb-remove-option" style="width: 40px;">-</button>
                </div>
            `;

            $optionsContainer.append(optionHTML);
            console.log('PFB Master Save Handler: Option added');
        },

        /**
         * Setup form loading
         */
        setupFormLoading: function() {
            console.log('PFB Master Save Handler: Setting up form loading');

            // Override any existing form loading functions
            if (typeof window.PFBAdmin !== 'undefined') {
                window.PFBAdmin.originalLoadForm = window.PFBAdmin.loadForm;
                window.PFBAdmin.loadForm = function(formId) {
                    console.log('PFB Master Save Handler: PFBAdmin.loadForm called, redirecting to master handler');
                    PFB_MasterSaveHandler.loadForm(formId);
                };
            }

            // Check if we need to load a form from URL
            const urlParams = new URLSearchParams(window.location.search);
            const formId = urlParams.get('form_id');

            if (formId) {
                console.log('PFB Master Save Handler: Form ID detected in URL, will load form:', formId);

                // Wait a bit more to ensure all UI is ready
                setTimeout(function() {
                    PFB_MasterSaveHandler.loadForm(formId);
                }, 1000);
            }
        },

        /**
         * Load form - MASTER LOAD FUNCTION
         */
        loadForm: function(formId) {
            console.log('PFB Master Save Handler: Loading form with ID:', formId);

            // Prevent multiple loading
            if (this.isLoading) {
                console.log('PFB Master Save Handler: Form is already loading, skipping');
                return;
            }

            this.isLoading = true;

            // Show loading indicator
            const $loadingMessage = $('<div id="pfb-loading-indicator" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 5px; z-index: 10000;">Loading form...</div>');
            $('body').append($loadingMessage);

            // Clear existing fields
            $('.pfb-form-fields').empty();

            $.ajax({
                url: ajaxurl,
                type: 'GET',
                data: {
                    action: 'pfb_get_form',
                    nonce: pfb_data.nonce,
                    form_id: formId
                },
                success: function(response) {
                    console.log('PFB Master Save Handler: Form load response:', response);

                    if (response.success && response.data && response.data.form) {
                        const form = response.data.form;

                        // Set form basic data
                        $('#form_title').val(form.title || '');
                        $('#form_description').val(form.description || '');
                        $('#form_status').val(form.status || 'draft');

                        // Load form settings
                        if (form.settings) {
                            $('#form_template').val(form.settings.template || 'default');
                            $('#form_show_currency_selector').val(form.settings.show_currency_selector || '1');
                            $('#form_submit_button_text').val(form.settings.submit_button_text || 'Calculate Price');
                        }

                        // Load fields with proper options handling
                        if (form.fields && Array.isArray(form.fields) && form.fields.length > 0) {
                            console.log('PFB Master Save Handler: Loading', form.fields.length, 'fields');

                            PFB_MasterSaveHandler.loadFields(form.fields, $loadingMessage);
                        } else {
                            $('.pfb-form-fields').html('<div class="pfb-empty-form-message">No fields found. Add fields using the buttons above.</div>');
                            $loadingMessage.remove();
                            PFB_MasterSaveHandler.isLoading = false;
                        }
                    } else {
                        console.error('PFB Master Save Handler: Error loading form:', response);
                        $('.pfb-form-fields').html('<div class="pfb-empty-form-message">Error loading form. Please try again.</div>');
                        $loadingMessage.remove();
                        PFB_MasterSaveHandler.isLoading = false;
                    }
                },
                error: function(xhr, status, error) {
                    console.error('PFB Master Save Handler: AJAX error:', error);
                    $('.pfb-form-fields').html('<div class="pfb-empty-form-message">Error loading form. Please try again.</div>');
                    $loadingMessage.remove();
                    PFB_MasterSaveHandler.isLoading = false;
                }
            });
        },

        /**
         * Load fields with proper options handling
         */
        loadFields: function(fields, $loadingMessage) {
            console.log('PFB Master Save Handler: Loading fields with options');

            fields.forEach(function(fieldData, index) {
                console.log('PFB Master Save Handler: Creating field', index + 1, ':', fieldData);

                const $field = PFB_MasterSaveHandler.createFieldFromData(fieldData);
                $('.pfb-form-fields').append($field);
            });

            $('.pfb-empty-form-message').hide();
            $loadingMessage.text('Form loaded successfully!').css('background', 'rgba(0,128,0,0.8)');

            setTimeout(function() {
                $loadingMessage.fadeOut(500, function() {
                    $(this).remove();
                });
                PFB_MasterSaveHandler.isLoading = false;
            }, 2000);
        },

        /**
         * Create field from data with proper options handling
         */
        createFieldFromData: function(fieldData) {
            console.log('PFB Master Save Handler: Creating field from data:', fieldData);

            const fieldId = fieldData.id || 'field_' + Date.now();
            const fieldType = fieldData.field_type || fieldData.type;
            const fieldLabel = fieldData.field_label || fieldData.label || 'Field';
            const fieldName = fieldData.field_name || fieldData.name || 'field_' + Date.now();
            const fieldRequired = fieldData.field_required || fieldData.required || false;
            const fieldWidth = fieldData.field_width || fieldData.width || '100';

            // Create the basic field structure using existing function if available
            let $field;

            if (typeof window.PFBAdmin !== 'undefined' && typeof window.PFBAdmin.createField === 'function') {
                $field = window.PFBAdmin.createField(fieldType, fieldId);
            } else {
                // Fallback field creation
                $field = this.createBasicField(fieldType, fieldId, fieldLabel);
            }

            if (!$field || !$field.length) {
                console.error('PFB Master Save Handler: Failed to create field');
                return $('<div>Error creating field</div>');
            }

            // Set field values
            $field.find('.pfb-field-label-input').val(fieldLabel);
            $field.find('.pfb-field-name-input').val(fieldName);
            $field.find('.pfb-field-required-input').prop('checked', fieldRequired);
            $field.find('.pfb-field-width-input').val(fieldWidth);

            // Handle field options for dropdown fields
            if (fieldType === 'dropdown' && fieldData.field_options) {
                this.populateFieldOptions($field, fieldData.field_options);
            }

            return $field;
        },

        /**
         * Create basic field (fallback)
         */
        createBasicField: function(fieldType, fieldId, fieldLabel) {
            const $field = $(`
                <div id="${fieldId}" class="pfb-field" data-type="${fieldType}">
                    <div class="pfb-field-header">
                        <div class="pfb-field-title">${fieldLabel}</div>
                        <div class="pfb-field-actions">
                            <div class="pfb-field-action pfb-field-edit" title="Edit"><span class="dashicons dashicons-edit"></span></div>
                            <div class="pfb-field-action pfb-field-delete" title="Delete"><span class="dashicons dashicons-trash"></span></div>
                        </div>
                    </div>
                    <div class="pfb-field-preview">
                        ${this.getFieldPreview(fieldType)}
                    </div>
                    <div class="pfb-field-settings" style="display: none;">
                        ${this.getFieldSettings(fieldType)}
                    </div>
                </div>
            `);

            return $field;
        },

        /**
         * Get field preview HTML
         */
        getFieldPreview: function(type) {
            switch (type) {
                case 'dropdown':
                    return '<select class="pfb-form-control" disabled><option>Select an option</option></select>';
                case 'text':
                    return '<input type="text" class="pfb-form-control" placeholder="Text Input" disabled>';
                case 'number':
                    return '<input type="number" class="pfb-form-control" placeholder="0" disabled>';
                default:
                    return '<div>Field Preview</div>';
            }
        },

        /**
         * Get field settings HTML
         */
        getFieldSettings: function(type) {
            let html = `
                <div class="pfb-form-group">
                    <label>Field Label</label>
                    <input type="text" class="pfb-form-control pfb-field-label-input">
                </div>
                <div class="pfb-form-group">
                    <label>Field Name</label>
                    <input type="text" class="pfb-form-control pfb-field-name-input">
                </div>
                <div class="pfb-form-group">
                    <label><input type="checkbox" class="pfb-field-required-input"> Required Field</label>
                </div>
                <div class="pfb-form-group">
                    <label>Field Width</label>
                    <select class="pfb-form-control pfb-field-width-input">
                        <option value="100">100% (Full Width)</option>
                        <option value="50">50% (Half Width)</option>
                        <option value="33">33% (One Third)</option>
                        <option value="25">25% (Quarter Width)</option>
                    </select>
                </div>
            `;

            // Add field-specific settings
            if (type === 'dropdown') {
                html += `
                    <div class="pfb-form-group pfb-field-options-group">
                        <label>Options</label>
                        <div class="pfb-field-options-header" style="display: flex; margin-bottom: 5px;">
                            <div style="flex: 1; font-weight: bold;">Label</div>
                            <div style="flex: 1; font-weight: bold;">Value</div>
                            <div style="width: 40px;"></div>
                        </div>
                        <div class="pfb-field-options">
                            <!-- Options will be populated here -->
                        </div>
                        <button type="button" class="pfb-add-option pfb-btn pfb-btn-secondary" style="margin-top: 5px;">Add Option</button>
                    </div>
                `;
            }

            html += `
                <div class="pfb-form-group">
                    <button type="button" class="pfb-btn pfb-btn-primary pfb-save-field-settings">Save</button>
                    <button type="button" class="pfb-btn pfb-btn-secondary pfb-cancel-field-settings">Cancel</button>
                </div>
            `;

            return html;
        },

        /**
         * Populate field options and settings from data (COMPLETE)
         */
        populateFieldOptions: function($field, fieldData) {
            console.log('PFB Master Save Handler: Populating complete field options and settings:', fieldData);

            const fieldType = fieldData.field_type || fieldData.type;
            const fieldOptions = fieldData.field_options || fieldData.options || {};

            // Handle choice fields (dropdown, radio, checkbox)
            if (fieldType === 'dropdown' || fieldType === 'radio' || fieldType === 'checkbox') {
                this.populateChoiceFieldOptions($field, fieldOptions);
            }

            // Handle slider fields
            if (fieldType === 'slider') {
                this.populateSliderFieldOptions($field, fieldOptions);
            }

            // Handle total fields
            if (fieldType === 'total') {
                this.populateTotalFieldOptions($field, fieldOptions);
            }

            // Handle subtotal fields
            if (fieldType === 'subtotal') {
                this.populateSubtotalFieldOptions($field, fieldOptions);
            }

            // Handle text fields
            if (fieldType === 'text') {
                this.populateTextFieldOptions($field, fieldOptions);
            }

            // Handle number fields
            if (fieldType === 'number') {
                this.populateNumberFieldOptions($field, fieldOptions);
            }

            // Populate conditional logic
            if (fieldData.conditional_logic) {
                this.populateConditionalLogic($field, fieldData.conditional_logic);
            }
        },

        /**
         * Populate choice field options (dropdown, radio, checkbox)
         */
        populateChoiceFieldOptions: function($field, fieldOptions) {
            console.log('PFB Master Save Handler: Populating choice field options');

            const $optionsContainer = $field.find('.pfb-field-options');

            if ($optionsContainer.length === 0) {
                console.log('PFB Master Save Handler: Options container not found, ensuring it exists');
                this.ensureOptionsUIForField($field);
                return;
            }

            // Clear existing options
            $optionsContainer.empty();

            // Get options from field data
            let options = [];

            if (fieldOptions && typeof fieldOptions === 'object') {
                if (fieldOptions.items && Array.isArray(fieldOptions.items)) {
                    options = fieldOptions.items;
                } else if (typeof fieldOptions === 'string') {
                    // Try to unserialize if it's a serialized string
                    try {
                        const parsed = JSON.parse(fieldOptions);
                        if (parsed.items && Array.isArray(parsed.items)) {
                            options = parsed.items;
                        }
                    } catch (e) {
                        console.log('PFB Master Save Handler: Could not parse field options as JSON');
                    }
                }
            }

            // If no options found, create default option
            if (options.length === 0) {
                options = [
                    {
                        label: 'Option 1',
                        value: 'option_1',
                        variable: ''
                    }
                ];
            }

            console.log('PFB Master Save Handler: Populating', options.length, 'choice options');

            // Add each option
            options.forEach(function(option) {
                PFB_MasterSaveHandler.addOptionToField($field, option.label || '', option.value || '');
            });
        },

        /**
         * Populate slider field options
         */
        populateSliderFieldOptions: function($field, fieldOptions) {
            console.log('PFB Master Save Handler: Populating slider field options');

            if (fieldOptions.min !== undefined) {
                $field.find('.pfb-slider-min-input').val(fieldOptions.min);
            }
            if (fieldOptions.max !== undefined) {
                $field.find('.pfb-slider-max-input').val(fieldOptions.max);
            }
            if (fieldOptions.step !== undefined) {
                $field.find('.pfb-slider-step-input').val(fieldOptions.step);
            }
            if (fieldOptions.default !== undefined) {
                $field.find('.pfb-slider-default-input').val(fieldOptions.default);
            }
        },

        /**
         * Populate total field options
         */
        populateTotalFieldOptions: function($field, fieldOptions) {
            console.log('PFB Master Save Handler: Populating total field options');

            if (fieldOptions.formula !== undefined) {
                $field.find('.pfb-total-formula-input').val(fieldOptions.formula);
            }
            if (fieldOptions.decimals !== undefined) {
                $field.find('.pfb-total-decimals-input').val(fieldOptions.decimals);
            }
        },

        /**
         * Populate subtotal field options
         */
        populateSubtotalFieldOptions: function($field, fieldOptions) {
            console.log('PFB Master Save Handler: Populating subtotal field options');

            // Handle subtotal lines
            if (fieldOptions.lines && Array.isArray(fieldOptions.lines)) {
                // Clear existing lines
                $field.find('.pfb-subtotal-line').remove();

                // Add each line
                fieldOptions.lines.forEach(function(line) {
                    // This would need to be implemented based on the subtotal field UI
                    console.log('PFB Master Save Handler: Adding subtotal line:', line);
                });
            }

            if (fieldOptions.empty_value !== undefined) {
                $field.find('.pfb-subtotal-empty-value-input').val(fieldOptions.empty_value);
            }
        },

        /**
         * Populate text field options
         */
        populateTextFieldOptions: function($field, fieldOptions) {
            console.log('PFB Master Save Handler: Populating text field options');

            if (fieldOptions.placeholder !== undefined) {
                $field.find('.pfb-field-placeholder').val(fieldOptions.placeholder);
            }
            if (fieldOptions.default !== undefined) {
                $field.find('.pfb-field-default').val(fieldOptions.default);
            }
            if (fieldOptions.description !== undefined) {
                $field.find('.pfb-field-description').val(fieldOptions.description);
            }
        },

        /**
         * Populate number field options
         */
        populateNumberFieldOptions: function($field, fieldOptions) {
            console.log('PFB Master Save Handler: Populating number field options');

            if (fieldOptions.min !== undefined) {
                $field.find('.pfb-field-min').val(fieldOptions.min);
            }
            if (fieldOptions.max !== undefined) {
                $field.find('.pfb-field-max').val(fieldOptions.max);
            }
            if (fieldOptions.step !== undefined) {
                $field.find('.pfb-field-step').val(fieldOptions.step);
            }
        },

        /**
         * Populate conditional logic from data
         */
        populateConditionalLogic: function($field, conditionalLogic) {
            console.log('PFB Master Save Handler: Populating conditional logic:', conditionalLogic);

            if (!conditionalLogic || !conditionalLogic.enabled) {
                return;
            }

            // Enable conditional logic
            $field.find('.pfb-enable-conditional-logic').prop('checked', true);
            $field.find('.pfb-conditional-logic-container').show();

            // Set logic type
            $field.find('.pfb-logic-type').val(conditionalLogic.logic_type || 'all');

            // Add rules
            if (conditionalLogic.rules && Array.isArray(conditionalLogic.rules)) {
                const $rulesContainer = $field.find('.pfb-conditional-rules');
                $rulesContainer.empty();

                conditionalLogic.rules.forEach(function(rule) {
                    // This would use the existing addRuleRow function from pfb-admin-core.js
                    if (typeof addRuleRow === 'function') {
                        addRuleRow($field.find('.pfb-conditional-logic-container'), rule);
                    } else {
                        console.log('PFB Master Save Handler: addRuleRow function not available');
                    }
                });
            }
        },

        /**
         * Add diagnostic tools
         */
        addDiagnosticTools: function() {
            const diagnosticButton = `
                <button type="button" id="pfb-master-diagnostic" class="pfb-btn pfb-btn-secondary" style="margin-left: 10px;">
                    <span class="dashicons dashicons-search"></span> Master Diagnostic
                </button>
            `;

            $('#pfb-save-form').after(diagnosticButton);

            $(document).on('click', '#pfb-master-diagnostic', function(e) {
                e.preventDefault();
                PFB_MasterSaveHandler.runDiagnostic();
            });
        },

        /**
         * Run diagnostic
         */
        runDiagnostic: function() {
            console.log('=== PFB MASTER SAVE HANDLER DIAGNOSTIC ===');

            const $fields = $('.pfb-field');
            console.log(`Found ${$fields.length} fields in form`);

            $fields.each(function(index) {
                const $field = $(this);
                const fieldId = $field.attr('id');
                const fieldType = $field.data('type');
                const fieldLabel = $field.find('.pfb-field-label-input').val() || $field.find('.pfb-field-title').text();

                console.log(`\n--- Field ${index + 1} ---`);
                console.log(`ID: ${fieldId}`);
                console.log(`Type: ${fieldType}`);
                console.log(`Label: ${fieldLabel}`);

                if (fieldType === 'dropdown') {
                    const $options = $field.find('.pfb-field-option');
                    console.log(`Number of option elements: ${$options.length}`);

                    if ($options.length > 0) {
                        console.log('Options found:');
                        $options.each(function(optionIndex) {
                            const $option = $(this);
                            const label = $option.find('.pfb-option-label').val();
                            const value = $option.find('.pfb-option-value').val();

                            console.log(`  Option ${optionIndex + 1}: "${label}" = "${value}"`);
                        });
                    } else {
                        console.log('NO OPTIONS FOUND - This is the problem!');
                    }
                }
            });

            console.log('\n=== DIAGNOSTIC COMPLETE ===');
        },

        /**
         * Collect form data - MASTER COLLECTION FUNCTION
         */
        collectFormData: function() {
            console.log('PFB Master Save Handler: Collecting form data');

            // Get form ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const formId = urlParams.get('form_id');

            // Basic form data
            const formData = {
                id: formId || null,
                title: $('#form_title').val() || 'Untitled Form',
                description: $('#form_description').val() || '',
                status: $('#form_status').val() || 'draft',
                settings: this.collectCompleteFormSettings(),
                fields: []
            };

            // Collect fields
            $('.pfb-field').each(function(index) {
                const $field = $(this);
                const fieldId = $field.attr('id');
                const fieldType = $field.data('type');

                console.log(`PFB Master Save Handler: Processing field #${index + 1}: ${fieldType} (${fieldId})`);

                if (!fieldType) {
                    console.log(`PFB Master Save Handler: Skipping field #${index + 1}, no type specified`);
                    return;
                }

                // Basic field data
                const field = {
                    id: fieldId,
                    type: fieldType,
                    label: $field.find('.pfb-field-label-input').val() || `Field ${index + 1}`,
                    name: $field.find('.pfb-field-name-input').val() || `field_${index + 1}`,
                    required: $field.find('.pfb-field-required-input').is(':checked'),
                    width: $field.find('.pfb-field-width-input').val() || '100',
                    options: {}
                };

                // Collect field options for dropdown fields
                if (fieldType === 'dropdown') {
                    console.log(`PFB Master Save Handler: Processing dropdown field options`);

                    field.options.items = [];

                    $field.find('.pfb-field-option').each(function() {
                        const $option = $(this);
                        const label = $option.find('.pfb-option-label').val();
                        const value = $option.find('.pfb-option-value').val();

                        console.log(`PFB Master Save Handler: Found option - Label: "${label}", Value: "${value}"`);

                        if (label && value) {
                            field.options.items.push({
                                label: label,
                                value: value,
                                variable: $option.find('.pfb-option-variable').val() || ''
                            });
                        }
                    });

                    // Ensure at least one option exists
                    if (field.options.items.length === 0) {
                        console.log('PFB Master Save Handler: No options found, adding default option');
                        field.options.items.push({
                            label: 'Option 1',
                            value: 'option_1',
                            variable: ''
                        });
                    }

                    console.log(`PFB Master Save Handler: Dropdown field has ${field.options.items.length} options:`, field.options.items);
                }

                // Handle other field types
                switch (fieldType) {
                    case 'radio':
                    case 'checkbox':
                        field.options.items = [];

                        $field.find('.pfb-field-option').each(function() {
                            const $option = $(this);
                            const label = $option.find('.pfb-option-label').val();
                            const value = $option.find('.pfb-option-value').val();

                            if (label && value) {
                                field.options.items.push({
                                    label: label,
                                    value: value,
                                    variable: $option.find('.pfb-option-variable').val() || ''
                                });
                            }
                        });

                        if (field.options.items.length === 0) {
                            field.options.items.push({
                                label: 'Option 1',
                                value: 'option_1',
                                variable: ''
                            });
                        }
                        break;

                    case 'number':
                        field.options.min = $field.find('.pfb-field-min').val() || '0';
                        field.options.max = $field.find('.pfb-field-max').val() || '100';
                        field.options.step = $field.find('.pfb-field-step').val() || '1';
                        break;

                    case 'text':
                        field.options.placeholder = $field.find('.pfb-field-placeholder').val() || '';
                        field.options.default = $field.find('.pfb-field-default').val() || '';
                        field.options.description = $field.find('.pfb-field-description').val() || '';
                        break;

                    case 'slider':
                        field.options.min = parseInt($field.find('.pfb-slider-min-input').val() || '0');
                        field.options.max = parseInt($field.find('.pfb-slider-max-input').val() || '100');
                        field.options.step = parseInt($field.find('.pfb-slider-step-input').val() || '1');
                        field.options.default = parseInt($field.find('.pfb-slider-default-input').val() || '50');
                        break;

                    case 'total':
                        field.options.formula = $field.find('.pfb-total-formula-input').val() || '';
                        field.options.decimals = parseInt($field.find('.pfb-total-decimals-input').val() || '2');
                        break;

                    case 'subtotal':
                        field.options.lines = [];

                        $field.find('.pfb-subtotal-line').each(function() {
                            const $line = $(this);
                            field.options.lines.push({
                                label: $line.find('.pfb-subtotal-line-label, .pfb-line-label').val() || 'Line',
                                formula: $line.find('.pfb-subtotal-line-formula, .pfb-line-formula').val() || ''
                            });
                        });

                        // Ensure at least one line exists
                        if (field.options.lines.length === 0) {
                            field.options.lines.push({
                                label: 'Line 1',
                                formula: ''
                            });
                        }

                        field.options.empty_value = $field.find('.pfb-subtotal-empty-value-input').val() || '---';
                        break;
                }

                // Collect conditional logic (COMPLETE)
                field.conditional_logic = PFB_MasterSaveHandler.collectFieldConditionalLogic($field);

                // Add field to form data
                formData.fields.push(field);
            });

            console.log('PFB Master Save Handler: Complete form data collected:', formData);
            return formData;
        },

        /**
         * Collect complete conditional logic for a field
         */
        collectFieldConditionalLogic: function($field) {
            console.log('PFB Master Save Handler: Collecting conditional logic');

            // Default conditional logic structure
            const conditionalLogic = {
                enabled: false,
                logic_type: 'all',
                rules: []
            };

            // Check if conditional logic UI exists
            if ($field.find('.pfb-enable-conditional-logic').length === 0) {
                console.log('PFB Master Save Handler: No conditional logic UI found');
                return conditionalLogic;
            }

            // Check if conditional logic is enabled
            const isEnabled = $field.find('.pfb-enable-conditional-logic').is(':checked');
            conditionalLogic.enabled = isEnabled;

            if (!isEnabled) {
                console.log('PFB Master Save Handler: Conditional logic not enabled');
                return conditionalLogic;
            }

            // Get logic type
            conditionalLogic.logic_type = $field.find('.pfb-logic-type').val() || 'all';

            // Collect rules
            $field.find('.pfb-conditional-rule').each(function() {
                const $rule = $(this);
                const ruleField = $rule.find('.pfb-rule-field').val();
                const ruleOperator = $rule.find('.pfb-rule-operator').val();
                const ruleValue = $rule.find('.pfb-rule-value').val();

                console.log(`PFB Master Save Handler: Processing rule - Field: ${ruleField}, Operator: ${ruleOperator}, Value: ${ruleValue}`);

                if (ruleField && ruleOperator) {
                    const rule = {
                        field: ruleField,
                        operator: ruleOperator,
                        value: ruleValue || ''
                    };

                    // Try to get field name from option data
                    const $selectedOption = $rule.find('.pfb-rule-field option:selected');
                    if ($selectedOption.length > 0) {
                        rule.field_name = $selectedOption.data('field-name') || $selectedOption.text() || '';
                        rule.field_label = $selectedOption.data('field-label') || $selectedOption.text() || '';
                    }

                    conditionalLogic.rules.push(rule);
                }
            });

            console.log('PFB Master Save Handler: Conditional logic collected:', conditionalLogic);
            return conditionalLogic;
        },

        /**
         * Collect complete form settings including all tabs
         */
        collectCompleteFormSettings: function() {
            console.log('PFB Master Save Handler: Collecting complete form settings');

            const settings = {
                // Form Settings Tab
                template: $('#form_template').val() || 'default',
                currency: $('#form_currency').val() || 'USD',
                show_currency_selector: $('#form_show_currency_selector').val() || '1',
                submit_button_text: $('#form_submit_button_text').val() || 'Calculate Price',
                success_message: $('#form_success_message').val() || 'Thank you for your submission!',

                // Translation Settings Tab
                languages: $('#form_languages').val() || 'en_US',

                // Translation fields
                translations: {}
            };

            // Collect translation data if available
            const selectedLanguage = $('#form_languages').val();
            if (selectedLanguage && selectedLanguage !== 'en_US') {
                settings.translations[selectedLanguage] = {
                    title: $('#translation_title').val() || '',
                    description: $('#translation_description').val() || '',
                    submit_button: $('#translation_submit_button').val() || '',
                    success_message: $('#translation_success_message').val() || ''
                };
            }

            console.log('PFB Master Save Handler: Complete form settings collected:', settings);
            return settings;
        },



        /**
         * Save form - MASTER SAVE FUNCTION
         */
        saveForm: function() {
            console.log('PFB Master Save Handler: Starting form save');

            // Show saving indicator
            const $savingIndicator = $('<div id="pfb-saving-indicator" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 5px; z-index: 10000;">Saving form...</div>')
                .appendTo('body');

            // Disable save buttons
            $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', true).text('Saving...');

            try {
                // Collect form data
                const formData = this.collectFormData();

                console.log('PFB Master Save Handler: Form data collected:', formData);

                // Save via AJAX
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'pfb_save_form_complete',
                        nonce: pfb_data.nonce,
                        form_data: formData
                    },
                    success: function(response) {
                        console.log('PFB Master Save Handler: Save response:', response);

                        if (response.success) {
                            $savingIndicator.text('Form saved successfully!').css('background', 'rgba(0,128,0,0.8)');

                            // Get form ID from URL or response
                            const urlParams = new URLSearchParams(window.location.search);
                            const formId = urlParams.get('form_id');

                            // Redirect to edit page if new form
                            if (!formId && response.data && response.data.form_id) {
                                $savingIndicator.text('Form saved successfully! Redirecting...');
                                setTimeout(function() {
                                    window.location.href = 'admin.php?page=pfb-form-editor&form_id=' + response.data.form_id;
                                }, 1000);
                            } else {
                                // Remove the indicator after a delay
                                setTimeout(function() {
                                    $savingIndicator.fadeOut(500, function() {
                                        $(this).remove();
                                    });
                                }, 2000);
                            }
                        } else {
                            const errorMessage = response.data && response.data.message
                                ? response.data.message
                                : 'Unknown error';

                            console.error('PFB Master Save Handler: Error response:', errorMessage);
                            $savingIndicator.text('Error saving form: ' + errorMessage).css('background', 'rgba(200,0,0,0.8)');

                            // Remove the indicator after a delay
                            setTimeout(function() {
                                $savingIndicator.fadeOut(500, function() {
                                    $(this).remove();
                                });
                            }, 5000);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('PFB Master Save Handler: AJAX error:', status, error);
                        console.error('PFB Master Save Handler: Response text:', xhr.responseText);

                        $savingIndicator.text('Error saving form: ' + error).css('background', 'rgba(200,0,0,0.8)');

                        // Remove the indicator after a delay
                        setTimeout(function() {
                            $savingIndicator.fadeOut(500, function() {
                                $(this).remove();
                            });
                        }, 5000);
                    },
                    complete: function() {
                        // Re-enable save buttons
                        $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');
                    }
                });
            } catch (error) {
                console.error('PFB Master Save Handler: Error collecting form data:', error);
                $savingIndicator.text('Error collecting form data: ' + error.message).css('background', 'rgba(200,0,0,0.8)');

                // Re-enable save buttons
                $('#pfb-save-form, #pfb-save-form-bottom').prop('disabled', false).text('Save Form');

                // Remove the indicator after a delay
                setTimeout(function() {
                    $savingIndicator.fadeOut(500, function() {
                        $(this).remove();
                    });
                }, 5000);
            }
        }
    };

    // DISABLED - Using pfb-clean-form-handler.js instead
    // PFB_MasterSaveHandler.init();

    // Expose to global scope for debugging only
    // window.PFB_MasterSaveHandler = PFB_MasterSaveHandler;

})(jQuery);
