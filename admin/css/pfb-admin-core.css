/**
 * Core admin styles for Price Form Builder.
 */

/* Field options styling */
.pfb-field-options-header {
    display: flex;
    margin-bottom: 5px;
    font-weight: bold;
}

.pfb-field-options-header > div {
    flex: 1;
    padding: 5px;
}

.pfb-field-options-header > div:last-child {
    width: 40px;
    flex: none;
}

.pfb-field-option {
    display: flex;
    margin-bottom: 5px;
    align-items: center;
}

.pfb-field-option input {
    flex: 1;
    margin-right: 5px;
}

.pfb-field-option button {
    width: 40px;
    height: 30px;
    padding: 0;
    text-align: center;
}

/* Field settings styling */
.pfb-field-settings {
    padding: 15px;
    background-color: #f9f9f9;
    border-top: 1px solid #ddd;
    display: none;
}

.pfb-field-settings.active {
    display: block;
}

/* Conditional logic placeholder */
.pfb-conditional-logic-placeholder {
    padding: 10px;
    background-color: #f0f0f0;
    border: 1px dashed #ccc;
    border-radius: 4px;
    color: #666;
}

/* Form tabs styling */
.pfb-tabs {
    display: flex;
    border-bottom: 1px solid #ccc;
    margin-bottom: 20px;
}

.pfb-tab {
    padding: 10px 15px;
    cursor: pointer;
    border: 1px solid transparent;
    border-bottom: none;
    margin-bottom: -1px;
}

.pfb-tab.active {
    background-color: #fff;
    border-color: #ccc;
    border-bottom-color: #fff;
}

.pfb-tab-content {
    display: none;
}

.pfb-tab-content.active {
    display: block;
}

/* Field styling */
.pfb-field {
    margin-bottom: 15px;
    border: 1px solid #ddd;
    background-color: #fff;
}

.pfb-field-header {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.pfb-field-title {
    font-weight: bold;
}

.pfb-field-actions {
    display: flex;
}

.pfb-field-action {
    cursor: pointer;
    margin-left: 5px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
}

.pfb-field-action:hover {
    background-color: #eee;
}

.pfb-field-preview {
    padding: 15px;
    border-bottom: 1px solid #ddd;
}

/* Form group styling */
.pfb-form-group {
    margin-bottom: 15px;
}

.pfb-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.pfb-form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.pfb-form-help {
    margin-top: 5px;
    color: #666;
    font-size: 0.9em;
}

/* Button styling */
.pfb-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.pfb-btn-primary {
    background-color: #0073aa;
    color: #fff;
}

.pfb-btn-primary:hover {
    background-color: #006291;
}

.pfb-btn-secondary {
    background-color: #f7f7f7;
    border: 1px solid #ccc;
    color: #555;
}

.pfb-btn-secondary:hover {
    background-color: #f0f0f0;
}
