/**
 * CSS for conditional logic and formula UI.
 *
 * @since      1.0.2
 */

/* Conditional Logic Styles */
.pfb-conditional-logic-toggle,
.pfb-conditional-formula-toggle {
    margin-top: 15px !important;
    margin-bottom: 15px !important;
    border: 5px solid #ff0000 !important;
    border-radius: 4px !important;
    padding: 15px !important;
    background-color: #ffe0e0 !important;
    display: block !important;
    position: relative !important;
    z-index: 100 !important;
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.5) !important;
}

.pfb-conditional-logic-toggle label,
.pfb-conditional-formula-toggle label {
    font-weight: 700 !important;
    color: #2271b1 !important;
    font-size: 16px !important;
    display: flex !important;
    align-items: center !important;
    margin-bottom: 10px !important;
    text-transform: uppercase !important;
}

.pfb-conditional-logic-toggle input[type="checkbox"],
.pfb-conditional-formula-toggle input[type="checkbox"] {
    margin-right: 8px !important;
    width: 20px !important;
    height: 20px !important;
}

.pfb-conditional-logic-container,
.pfb-conditional-formula-container {
    margin-top: 10px !important;
    padding: 15px !important;
    background-color: #f9f9f9 !important;
    border: 1px solid #e5e5e5 !important;
    border-radius: 4px !important;
    display: block !important;
}

.pfb-conditional-rules {
    margin-bottom: 10px;
}

.pfb-conditional-rule {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.pfb-rule-field,
.pfb-rule-operator,
.pfb-rule-value,
.pfb-condition-field,
.pfb-condition-operator,
.pfb-condition-value {
    margin-right: 10px;
}

.pfb-rule-field,
.pfb-rule-operator,
.pfb-condition-field,
.pfb-condition-operator {
    min-width: 150px;
}

.pfb-rule-value,
.pfb-condition-value {
    flex: 1;
}

.pfb-remove-rule {
    background: none;
    border: none;
    color: #a00;
    cursor: pointer;
    padding: 0;
}

.pfb-conditional-logic-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.pfb-add-rule {
    display: flex;
    align-items: center;
}

.pfb-add-rule .dashicons {
    margin-right: 5px;
}

.pfb-logic-type {
    min-width: 200px;
}

/* Conditional Formula Styles */
.pfb-conditional-formula {
    margin-top: 10px;
}

.pfb-conditional-formula-row {
    margin-bottom: 15px;
}

.pfb-conditional-formula-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.pfb-conditional-formula-condition {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.pfb-formula-editor {
    position: relative;
}

.pfb-formula-input {
    width: 100%;
    min-height: 60px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: monospace;
}

.pfb-formula-builder-toggle {
    position: absolute;
    right: 5px;
    top: 5px;
}

.pfb-open-formula-builder {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .pfb-conditional-rule,
    .pfb-conditional-formula-condition {
        flex-direction: column;
        align-items: flex-start;
    }

    .pfb-rule-field,
    .pfb-rule-operator,
    .pfb-rule-value,
    .pfb-condition-field,
    .pfb-condition-operator,
    .pfb-condition-value {
        width: 100%;
        margin-right: 0;
        margin-bottom: 10px;
    }

    .pfb-conditional-logic-actions {
        flex-direction: column;
        align-items: flex-start;
    }

    .pfb-logic-type {
        width: 100%;
        margin-top: 10px;
    }
}
