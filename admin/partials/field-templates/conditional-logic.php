<?php
/**
 * Conditional Logic Template
 *
 * @since      1.0.0
 * @package    PFB
 * @subpackage PFB/admin/partials/field-templates
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}
?>

<div class="pfb-field-conditional-logic">
    <div class="pfb-field-conditional-logic-toggle">
        <label>
            <input type="checkbox" class="pfb-toggle-conditional-logic" />
            <?php _e('Enable Conditional Logic', 'price-form-builder'); ?>
        </label>
    </div>
    
    <div class="pfb-conditional-logic-container" style="display: none;">
        <div class="pfb-conditional-logic-header">
            <p><?php _e('Show this field if', 'price-form-builder'); ?></p>
            <select class="pfb-logic-type">
                <option value="all"><?php _e('All', 'price-form-builder'); ?></option>
                <option value="any"><?php _e('Any', 'price-form-builder'); ?></option>
            </select>
            <p><?php _e('of the following conditions match:', 'price-form-builder'); ?></p>
        </div>
        
        <div class="pfb-rules-container">
            <!-- Rules will be added here dynamically -->
        </div>
        
        <div class="pfb-conditional-logic-actions">
            <button type="button" class="pfb-add-rule button"><?php _e('Add Rule', 'price-form-builder'); ?></button>
        </div>
    </div>
</div>
