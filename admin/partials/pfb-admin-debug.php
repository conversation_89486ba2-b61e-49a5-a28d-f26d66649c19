<?php
/**
 * Debug page for Price Form Builder
 *
 * @since      1.0.0
 * @package    PFB
 * @subpackage PFB/admin/partials
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

global $wpdb;
?>

<div class="wrap">
    <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
    
    <div class="pfb-debug-section">
        <h2>Database Tables</h2>
        
        <table class="widefat">
            <thead>
                <tr>
                    <th>Table Name</th>
                    <th>Exists</th>
                    <th>Columns</th>
                </tr>
            </thead>
            <tbody>
                <?php
                // Check forms table
                $forms_table = $wpdb->prefix . 'pfb_forms';
                $forms_exists = $wpdb->get_var("SHOW TABLES LIKE '{$forms_table}'") === $forms_table;
                $forms_columns = $forms_exists ? $wpdb->get_results("SHOW COLUMNS FROM {$forms_table}") : array();
                ?>
                <tr>
                    <td><?php echo esc_html($forms_table); ?></td>
                    <td><?php echo $forms_exists ? '<span style="color: green;">Yes</span>' : '<span style="color: red;">No</span>'; ?></td>
                    <td>
                        <?php if ($forms_exists): ?>
                            <ul>
                                <?php foreach ($forms_columns as $column): ?>
                                    <li><?php echo esc_html($column->Field); ?> (<?php echo esc_html($column->Type); ?>)</li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <span style="color: red;">Table does not exist</span>
                        <?php endif; ?>
                    </td>
                </tr>
                
                <?php
                // Check form fields table
                $fields_table = $wpdb->prefix . 'pfb_form_fields';
                $fields_exists = $wpdb->get_var("SHOW TABLES LIKE '{$fields_table}'") === $fields_table;
                $fields_columns = $fields_exists ? $wpdb->get_results("SHOW COLUMNS FROM {$fields_table}") : array();
                ?>
                <tr>
                    <td><?php echo esc_html($fields_table); ?></td>
                    <td><?php echo $fields_exists ? '<span style="color: green;">Yes</span>' : '<span style="color: red;">No</span>'; ?></td>
                    <td>
                        <?php if ($fields_exists): ?>
                            <ul>
                                <?php foreach ($fields_columns as $column): ?>
                                    <li><?php echo esc_html($column->Field); ?> (<?php echo esc_html($column->Type); ?>)</li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <span style="color: red;">Table does not exist</span>
                        <?php endif; ?>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="pfb-debug-section">
        <h2>Forms</h2>
        
        <?php
        if ($forms_exists) {
            $forms = $wpdb->get_results("SELECT * FROM {$forms_table}");
            
            if (!empty($forms)) {
                echo '<table class="widefat">';
                echo '<thead><tr><th>ID</th><th>Title</th><th>Description</th><th>Status</th><th>Fields</th><th>Settings</th></tr></thead>';
                echo '<tbody>';
                
                foreach ($forms as $form) {
                    $fields_count = $wpdb->get_var($wpdb->prepare("SELECT COUNT(*) FROM {$fields_table} WHERE form_id = %d", $form->id));
                    $settings = get_option('pfb_form_settings_' . $form->id, array());
                    
                    echo '<tr>';
                    echo '<td>' . esc_html($form->id) . '</td>';
                    echo '<td>' . esc_html($form->title) . '</td>';
                    echo '<td>' . esc_html($form->description) . '</td>';
                    echo '<td>' . esc_html($form->status) . '</td>';
                    echo '<td>' . esc_html($fields_count) . '</td>';
                    echo '<td><pre>' . esc_html(print_r($settings, true)) . '</pre></td>';
                    echo '</tr>';
                }
                
                echo '</tbody></table>';
            } else {
                echo '<p>No forms found.</p>';
            }
        } else {
            echo '<p style="color: red;">Forms table does not exist.</p>';
        }
        ?>
    </div>
    
    <div class="pfb-debug-section">
        <h2>Database Repair</h2>
        
        <form method="post" action="">
            <?php wp_nonce_field('pfb_repair_database', 'pfb_repair_nonce'); ?>
            
            <p>
                <button type="submit" name="pfb_repair_database" class="button button-primary">Repair Database</button>
            </p>
        </form>
        
        <?php
        // Handle database repair
        if (isset($_POST['pfb_repair_database']) && isset($_POST['pfb_repair_nonce']) && wp_verify_nonce($_POST['pfb_repair_nonce'], 'pfb_repair_database')) {
            echo '<h3>Repair Results</h3>';
            
            // Check if forms table exists
            if (!$forms_exists) {
                echo '<p>Creating forms table...</p>';
                
                $sql = "CREATE TABLE {$forms_table} (
                    id bigint(20) NOT NULL AUTO_INCREMENT,
                    title varchar(255) NOT NULL,
                    description text,
                    status varchar(20) NOT NULL DEFAULT 'publish',
                    PRIMARY KEY (id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
                
                $result = $wpdb->query($sql);
                
                if ($result !== false) {
                    echo '<p style="color: green;">Forms table created successfully.</p>';
                } else {
                    echo '<p style="color: red;">Failed to create forms table: ' . $wpdb->last_error . '</p>';
                }
            }
            
            // Check if fields table exists
            if (!$fields_exists) {
                echo '<p>Creating form fields table...</p>';
                
                $sql = "CREATE TABLE {$fields_table} (
                    id bigint(20) NOT NULL AUTO_INCREMENT,
                    form_id bigint(20) NOT NULL,
                    field_type varchar(50) NOT NULL,
                    field_label varchar(255) NOT NULL,
                    field_name varchar(255) NOT NULL,
                    field_required tinyint(1) NOT NULL DEFAULT 0,
                    field_width varchar(10) NOT NULL DEFAULT '100',
                    field_order int(11) NOT NULL DEFAULT 0,
                    field_options longtext,
                    conditional_logic longtext,
                    PRIMARY KEY (id),
                    KEY form_id (form_id)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";
                
                $result = $wpdb->query($sql);
                
                if ($result !== false) {
                    echo '<p style="color: green;">Form fields table created successfully.</p>';
                } else {
                    echo '<p style="color: red;">Failed to create form fields table: ' . $wpdb->last_error . '</p>';
                }
            } else {
                // Check if field_width column exists
                $field_width_exists = false;
                foreach ($fields_columns as $column) {
                    if ($column->Field === 'field_width') {
                        $field_width_exists = true;
                        break;
                    }
                }
                
                if (!$field_width_exists) {
                    echo '<p>Adding field_width column to form fields table...</p>';
                    
                    $sql = "ALTER TABLE {$fields_table} ADD COLUMN field_width varchar(10) NOT NULL DEFAULT '100' AFTER field_required";
                    
                    $result = $wpdb->query($sql);
                    
                    if ($result !== false) {
                        echo '<p style="color: green;">field_width column added successfully.</p>';
                    } else {
                        echo '<p style="color: red;">Failed to add field_width column: ' . $wpdb->last_error . '</p>';
                    }
                }
                
                // Check if conditional_logic column exists
                $conditional_logic_exists = false;
                foreach ($fields_columns as $column) {
                    if ($column->Field === 'conditional_logic') {
                        $conditional_logic_exists = true;
                        break;
                    }
                }
                
                if (!$conditional_logic_exists) {
                    echo '<p>Adding conditional_logic column to form fields table...</p>';
                    
                    $sql = "ALTER TABLE {$fields_table} ADD COLUMN conditional_logic longtext AFTER field_options";
                    
                    $result = $wpdb->query($sql);
                    
                    if ($result !== false) {
                        echo '<p style="color: green;">conditional_logic column added successfully.</p>';
                    } else {
                        echo '<p style="color: red;">Failed to add conditional_logic column: ' . $wpdb->last_error . '</p>';
                    }
                }
            }
            
            echo '<p>Database repair completed.</p>';
        }
        ?>
    </div>
</div>
