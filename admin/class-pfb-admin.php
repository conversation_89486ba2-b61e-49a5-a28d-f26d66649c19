<?php
/**
 * The admin-specific functionality of the plugin.
 *
 * @since      1.0.0
 */
class PFB_Admin {

    /**
     * The ID of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $plugin_name    The ID of this plugin.
     */
    private $plugin_name;

    /**
     * The version of this plugin.
     *
     * @since    1.0.0
     * @access   private
     * @var      string    $version    The current version of this plugin.
     */
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name       The name of this plugin.
     * @param    string    $version           The version of this plugin.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;

        // Register AJAX actions
        add_action('wp_ajax_pfb_get_form', array($this, 'ajax_get_form'));
        add_action('wp_ajax_pfb_delete_form', array($this, 'ajax_delete_form'));
        add_action('wp_ajax_pfb_save_form', array($this, 'ajax_save_form'));
        add_action('wp_ajax_pfb_get_price_categories', array($this, 'ajax_get_price_categories'));
        add_action('wp_ajax_pfb_save_price_category', array($this, 'ajax_save_price_category'));
        add_action('wp_ajax_pfb_delete_price_category', array($this, 'ajax_delete_price_category'));
        add_action('wp_ajax_pfb_get_variables', array($this, 'ajax_get_variables'));
        add_action('wp_ajax_pfb_save_variable', array($this, 'ajax_save_variable'));
        add_action('wp_ajax_pfb_delete_variable', array($this, 'ajax_delete_variable'));
        add_action('wp_ajax_pfb_get_translations', array($this, 'ajax_get_translations'));
        add_action('wp_ajax_pfb_save_translations', array($this, 'ajax_save_translations'));
        add_action('wp_ajax_pfb_test_formula', array($this, 'ajax_test_formula'));
        add_action('wp_ajax_pfb_check_database', array($this, 'ajax_check_database'));
        add_action('wp_ajax_pfb_save_form_v2', array($this, 'ajax_save_form_v2'));
        add_action('wp_ajax_pfb_save_form_complete', array($this, 'ajax_save_form_complete'));
    }

    /**
     * Register the stylesheets for the admin area.
     *
     * @since    1.0.0
     */
    public function enqueue_styles() {
        wp_enqueue_style($this->plugin_name, PFB_PLUGIN_URL . 'admin/css/pfb-admin.css', array(), $this->version, 'all');

        // Add the subtotal field specific styles
        wp_enqueue_style($this->plugin_name . '-subtotal', PFB_PLUGIN_URL . 'admin/css/pfb-subtotal-admin.css', array($this->plugin_name), $this->version, 'all');

        // Only load on plugin pages
        if (isset($_GET['page']) && strpos($_GET['page'], 'pfb') === 0) {
            // Add Material Design Icons
            wp_enqueue_style('material-icons', 'https://fonts.googleapis.com/icon?family=Material+Icons', array(), $this->version);

            // Add Google Fonts
            wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap', array(), $this->version);

            // Add conditional logic styles for form editor
            if (isset($_GET['page']) && ($_GET['page'] === 'pfb-form-editor' || isset($_GET['form_id']))) {
                wp_enqueue_style($this->plugin_name . '-conditional-logic', PFB_PLUGIN_URL . 'admin/css/pfb-conditional-logic.css', array($this->plugin_name), $this->version, 'all');
            }
        }
    }

    /**
     * Register the JavaScript for the admin area.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts() {
        wp_enqueue_script($this->plugin_name, PFB_PLUGIN_URL . 'admin/js/pfb-admin.js', array('jquery', 'jquery-ui-sortable', 'jquery-ui-draggable', 'jquery-ui-droppable'), $this->version, false);

        // Only load on plugin pages
        if (isset($_GET['page']) && strpos($_GET['page'], 'pfb') === 0) {
            // React components are not used in the current version
            // Removed to prevent 404 errors

            // Add conditional logic and formula scripts for form editor
            if (isset($_GET['page']) && ($_GET['page'] === 'pfb-form-editor' || isset($_GET['form_id']))) {
                // Add our simplified core admin CSS
                wp_enqueue_style('pfb-admin-core', PFB_PLUGIN_URL . 'admin/css/pfb-admin-core.css', array(), $this->version, 'all');

                // Add our simplified core admin JS (this will override the main admin.js for form editor)
                wp_enqueue_script('pfb-admin-core', PFB_PLUGIN_URL . 'admin/js/pfb-admin-core.js', array('jquery', 'jquery-ui-sortable'), $this->version, true);

                // Add conditional logic scripts
                wp_enqueue_script('pfb-conditional-logic', PFB_PLUGIN_URL . 'admin/js/pfb-conditional-logic.js', array('jquery', $this->plugin_name), $this->version, true);
                wp_enqueue_script('pfb-conditional-formula', PFB_PLUGIN_URL . 'admin/js/pfb-conditional-formula.js', array('jquery', $this->plugin_name), $this->version, true);

                // Add debug script
                wp_enqueue_script('pfb-debug', PFB_PLUGIN_URL . 'admin/js/pfb-debug.js', array('jquery', $this->plugin_name), $this->version, true);

                // DISABLED - All conflicting save handlers
                // wp_enqueue_script('pfb-form-save-v2', PFB_PLUGIN_URL . 'admin/js/pfb-form-save-v2.js', array('jquery', $this->plugin_name, 'pfb-admin-core', 'pfb-conditional-logic'), $this->version, true);
                // wp_enqueue_script('pfb-field-options-fix', PFB_PLUGIN_URL . 'admin/js/pfb-field-options-fix.js', array('jquery', $this->plugin_name, 'pfb-admin-core'), $this->version, true);
                // wp_enqueue_script('pfb-complete-form-handler', PFB_PLUGIN_URL . 'admin/js/pfb-complete-form-handler.js', array('jquery', $this->plugin_name, 'pfb-admin-core'), $this->version, true);
                // wp_enqueue_script('pfb-field-options-direct-fix', PFB_PLUGIN_URL . 'admin/js/pfb-field-options-direct-fix.js', array('jquery', $this->plugin_name, 'pfb-admin-core'), $this->version, true);
                // wp_enqueue_script('pfb-simple-field-options-fix', PFB_PLUGIN_URL . 'admin/js/pfb-simple-field-options-fix.js', array('jquery', $this->plugin_name, 'pfb-admin-core', 'pfb-field-options-direct-fix'), $this->version, true);

                // ONLY load the essential scripts
                wp_enqueue_script('pfb-conditional-logic-editor', PFB_PLUGIN_URL . 'admin/js/pfb-conditional-logic-editor.js', array('jquery', $this->plugin_name, 'pfb-admin-core'), $this->version, true);

                // Clean form handler - no diagnostics, no conflicts
                wp_enqueue_script('pfb-clean-form-handler', PFB_PLUGIN_URL . 'admin/js/pfb-clean-form-handler.js', array('jquery', $this->plugin_name, 'pfb-admin-core'), $this->version, true);

                // Add data to JavaScript
                wp_localize_script('pfb-clean-form-handler', 'pfb_data', array(
                    'nonce' => wp_create_nonce('pfb_nonce'),
                    'ajax_url' => admin_url('admin-ajax.php')
                ));

                // Make sure ajaxurl is defined
                wp_add_inline_script('jquery', 'var ajaxurl = "' . admin_url('admin-ajax.php') . '";');
            }

            // Load formula parser and calculator for formula testing
            if (isset($_GET['page']) && $_GET['page'] === 'pfb-settings') {
                require_once PFB_PLUGIN_DIR . 'includes/class-pfb-formula-parser.php';
                require_once PFB_PLUGIN_DIR . 'includes/class-pfb-simple-calculator.php';
            }

            // Add localized script data
            wp_localize_script($this->plugin_name, 'pfb_data', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('pfb_nonce'),
                'currencies' => PFB_Currency::get_all_currencies(),
                'default_currency' => PFB_Currency::get_default_currency(),
                'field_types' => PFB_Form::get_field_types(),
                'i18n' => array(
                    'save' => __('Save', 'price-form-builder'),
                    'save_settings' => __('Save Settings', 'price-form-builder'),
                    'cancel' => __('Cancel', 'price-form-builder'),
                    'delete' => __('Delete', 'price-form-builder'),
                    'confirm_delete' => __('Are you sure you want to delete this item?', 'price-form-builder'),
                    'success' => __('Success', 'price-form-builder'),
                    'error' => __('Error', 'price-form-builder'),
                    'loading' => __('Loading...', 'price-form-builder'),
                    'field_label' => __('Field Label', 'price-form-builder'),
                    'field_name' => __('Field Name', 'price-form-builder'),
                    'field_identifier' => __('Used as the field identifier in formulas.', 'price-form-builder'),
                    'required_field' => __('Required Field', 'price-form-builder'),
                    'field_width' => __('Field Width', 'price-form-builder'),
                    'field_width_help' => __('Select the width of this field to place multiple fields in the same row', 'price-form-builder'),
                    'width_100' => __('100% (Full Width)', 'price-form-builder'),
                    'width_50' => __('50% (Half Width)', 'price-form-builder'),
                    'width_33' => __('33% (One Third)', 'price-form-builder'),
                    'width_25' => __('25% (Quarter Width)', 'price-form-builder'),
                    'form_template' => __('Form Template', 'price-form-builder'),
                    'form_template_help' => __('Select a template for the form appearance.', 'price-form-builder'),
                    'default_currency' => __('Default Currency', 'price-form-builder'),
                    'thank_you' => __('Thank you for your submission!', 'price-form-builder'),
                    'price_formula' => __('Price Formula', 'price-form-builder'),
                    'clear' => __('Clear', 'price-form-builder'),
                    'form_fields' => __('Form Fields', 'price-form-builder'),
                    'price_variables' => __('Price Variables', 'price-form-builder'),
                    'select_variables' => __('Select Variables', 'price-form-builder'),
                    'operators' => __('Operators', 'price-form-builder'),
                    'numbers' => __('Numbers', 'price-form-builder'),
                    'functions' => __('Functions', 'price-form-builder'),
                    'no_fields' => __('No fields available yet', 'price-form-builder'),
                    'formula_help' => __('Click on fields, operators, numbers, and functions to add them to your formula. Example: {field1} * {field2} + 10', 'price-form-builder'),
                    'ceil_help' => __('Use ceil() to round up to the next integer (e.g., ceil(10.1) = 11).', 'price-form-builder'),
                    'options' => __('Options', 'price-form-builder'),
                    'label' => __('Label', 'price-form-builder'),
                    'value' => __('Value', 'price-form-builder'),
                    'option' => __('Option', 'price-form-builder'),
                    'option_help' => __('Enter a fixed value or click the database icon to select a dynamic value from price variables.', 'price-form-builder'),
                    'round' => __('round', 'price-form-builder'),
                    'ceil' => __('ceil', 'price-form-builder'),
                    'floor' => __('floor', 'price-form-builder'),
                    'min' => __('min', 'price-form-builder'),
                    'max' => __('max', 'price-form-builder'),
                    'if' => __('if', 'price-form-builder'),
                    'subtotal' => __('Subtotal', 'price-form-builder'),
                    'subtotal_lines' => __('Subtotal Lines', 'price-form-builder'),
                    'line_label' => __('Line Label', 'price-form-builder'),
                    'formula' => __('Formula', 'price-form-builder'),
                    'line' => __('Line', 'price-form-builder'),
                    'subtotal_lines_help' => __('Add multiple lines with labels and formulas. The subtotal will be the sum of all lines.', 'price-form-builder'),
                    // Conditional logic translations
                    'conditionalLogic' => __('Conditional Logic', 'price-form-builder'),
                    'showThisFieldWhen' => __('Show this field when:', 'price-form-builder'),
                    'selectField' => __('Select a field', 'price-form-builder'),
                    'is' => __('is', 'price-form-builder'),
                    'isNot' => __('is not', 'price-form-builder'),
                    'greaterThan' => __('greater than', 'price-form-builder'),
                    'lessThan' => __('less than', 'price-form-builder'),
                    'contains' => __('contains', 'price-form-builder'),
                    'addRule' => __('Add Rule', 'price-form-builder'),
                    'allConditions' => __('All conditions must match (AND)', 'price-form-builder'),
                    'anyCondition' => __('Any condition can match (OR)', 'price-form-builder'),
                    'conditionalFormula' => __('Conditional Formula', 'price-form-builder'),
                    'ifCondition' => __('If Condition', 'price-form-builder'),
                    'thenFormula' => __('Then Formula', 'price-form-builder'),
                    'elseFormula' => __('Else Formula', 'price-form-builder'),
                    'addCondition' => __('Add Condition', 'price-form-builder')
                )
            ));

            // Add localized script data for conditional logic
            wp_localize_script('pfb-conditional-logic', 'pfbAdminL10n', array(
                'conditionalLogic' => __('Conditional Logic', 'price-form-builder'),
                'showThisFieldWhen' => __('Show this field when:', 'price-form-builder'),
                'selectField' => __('Select a field', 'price-form-builder'),
                'is' => __('is', 'price-form-builder'),
                'isNot' => __('is not', 'price-form-builder'),
                'greaterThan' => __('greater than', 'price-form-builder'),
                'lessThan' => __('less than', 'price-form-builder'),
                'contains' => __('contains', 'price-form-builder'),
                'addRule' => __('Add Rule', 'price-form-builder'),
                'allConditions' => __('All conditions must match (AND)', 'price-form-builder'),
                'anyCondition' => __('Any condition can match (OR)', 'price-form-builder'),
                'value' => __('Value', 'price-form-builder')
            ));
        }
    }

    /**
     * Register the administration menu for this plugin.
     *
     * @since    1.0.0
     */
    public function add_plugin_admin_menu() {
        // Main menu item
        add_menu_page(
            __('Price Form Builder', 'price-form-builder'),
            __('Price Forms', 'price-form-builder'),
            'manage_options',
            'pfb-forms',
            array($this, 'display_forms_page'),
            'dashicons-calculator',
            30
        );

        // Forms submenu
        add_submenu_page(
            'pfb-forms',
            __('Forms', 'price-form-builder'),
            __('Forms', 'price-form-builder'),
            'manage_options',
            'pfb-forms',
            array($this, 'display_forms_page')
        );

        // Add new form submenu
        add_submenu_page(
            'pfb-forms',
            __('Add New Form', 'price-form-builder'),
            __('Add New', 'price-form-builder'),
            'manage_options',
            'pfb-form-editor',
            array($this, 'display_form_editor_page')
        );

        // Price variables submenu
        add_submenu_page(
            'pfb-forms',
            __('Price Variables', 'price-form-builder'),
            __('Price Variables', 'price-form-builder'),
            'manage_options',
            'pfb-price-variables',
            array($this, 'display_price_variables_page')
        );

        // Price sheet submenu (hidden from menu)
        add_submenu_page(
            null, // Hidden from menu
            __('Price Sheet', 'price-form-builder'),
            __('Price Sheet', 'price-form-builder'),
            'manage_options',
            'pfb-price-sheet',
            array($this, 'display_price_sheet_page')
        );

        // Currencies submenu
        add_submenu_page(
            'pfb-forms',
            __('Currencies', 'price-form-builder'),
            __('Currencies', 'price-form-builder'),
            'manage_options',
            'pfb-currencies',
            array($this, 'display_currencies_page')
        );

        // Settings submenu
        add_submenu_page(
            'pfb-forms',
            __('Settings', 'price-form-builder'),
            __('Settings', 'price-form-builder'),
            'manage_options',
            'pfb-settings',
            array($this, 'display_settings_page')
        );

        // Debug submenu
        add_submenu_page(
            'pfb-forms',
            __('Debug', 'price-form-builder'),
            __('Debug', 'price-form-builder'),
            'manage_options',
            'pfb-debug',
            array($this, 'display_debug_page')
        );
    }

    /**
     * Render the forms page.
     *
     * @since    1.0.0
     */
    public function display_forms_page() {
        include_once PFB_PLUGIN_DIR . 'admin/partials/pfb-admin-forms.php';
    }

    /**
     * Render the form editor page.
     *
     * @since    1.0.0
     */
    public function display_form_editor_page() {
        include_once PFB_PLUGIN_DIR . 'admin/partials/pfb-admin-form-editor.php';
    }

    /**
     * Render the price variables page.
     *
     * @since    1.0.0
     */
    public function display_price_variables_page() {
        include_once PFB_PLUGIN_DIR . 'admin/partials/pfb-admin-price-variables.php';
    }

    /**
     * Render the currencies page.
     *
     * @since    1.0.0
     */
    public function display_currencies_page() {
        include_once PFB_PLUGIN_DIR . 'admin/partials/pfb-admin-currencies.php';
    }

    /**
     * Render the settings page.
     *
     * @since    1.0.0
     */
    public function display_settings_page() {
        include_once PFB_PLUGIN_DIR . 'admin/partials/pfb-admin-settings.php';
    }

    /**
     * Render the debug page.
     *
     * @since    1.0.0
     */
    public function display_debug_page() {
        include_once PFB_PLUGIN_DIR . 'admin/partials/pfb-admin-debug.php';
    }

    /**
     * Render the price sheet page.
     *
     * @since    1.0.0
     */
    public function display_price_sheet_page() {
        include_once PFB_PLUGIN_DIR . 'admin/partials/pfb-admin-price-sheet.php';
    }

    /**
     * AJAX handler for saving a form.
     *
     * @since    1.0.0
     */
    public function ajax_save_form() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Check if field_width column exists
        global $wpdb;
        $table_name = $wpdb->prefix . 'pfb_form_fields';
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'field_width'");

        // If the column doesn't exist, add it
        if (empty($column_exists)) {
            require_once PFB_PLUGIN_DIR . 'includes/update-db.php';
            pfb_add_field_width_column();
        }

        // Check if conditional_logic column exists
        $conditional_logic_column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'conditional_logic'");

        // If the column doesn't exist, add it
        if (empty($conditional_logic_column_exists)) {
            // Include the update-db.php file which contains the function
            require_once PFB_PLUGIN_DIR . 'includes/update-db.php';

            // Call the function to add the conditional_logic column
            if (function_exists('pfb_add_conditional_logic_column')) {
                pfb_add_conditional_logic_column();
            } else {
                // Fallback: add the column directly if the function doesn't exist
                $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN conditional_logic LONGTEXT AFTER field_options");
                error_log('PFB: Added conditional_logic column directly');
            }
        }

        // Get form data
        $form_data = isset($_POST['form_data']) ? $_POST['form_data'] : array();

        if (empty($form_data)) {
            wp_send_json_error(array('message' => __('No form data provided.', 'price-form-builder')));
        }

        // Sanitize form data
        $form_data = $this->sanitize_form_data($form_data);

        // Save form
        $form = new PFB_Form($form_data);
        $form_id = $form->save();

        if (!$form_id) {
            wp_send_json_error(array('message' => __('Failed to save form.', 'price-form-builder')));
        }

        // Save translations if provided
        if (isset($_POST['translations']) && is_array($_POST['translations'])) {
            foreach ($_POST['translations'] as $language_code => $translation) {
                PFB_DB::save_translations('form', $form_id, $language_code, $translation);
            }
        }

        wp_send_json_success(array(
            'message' => __('Form saved successfully.', 'price-form-builder'),
            'form_id' => $form_id
        ));
    }

    /**
     * AJAX handler for getting a form.
     *
     * @since    1.0.0
     */
    public function ajax_get_form() {
        try {
            // Check nonce
            if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'pfb_nonce')) {
                error_log('PFB: Security check failed in ajax_get_form');
                wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
                return;
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                error_log('PFB: Permission check failed in ajax_get_form');
                wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
                return;
            }

            // Get form ID
            $form_id = isset($_GET['form_id']) ? intval($_GET['form_id']) : 0;

            if (!$form_id) {
                error_log('PFB: No form ID provided in ajax_get_form');
                wp_send_json_error(array('message' => __('No form ID provided.', 'price-form-builder')));
                return;
            }

            error_log('PFB: Processing ajax_get_form for form ID: ' . $form_id);

            // Debug: Check if field_width column exists
            global $wpdb;
            $table_name = $wpdb->prefix . 'pfb_form_fields';
            $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'field_width'");
            error_log('PFB: field_width column exists in ajax_get_form: ' . (empty($column_exists) ? 'NO' : 'YES'));

            // If the column doesn't exist, add it
            if (empty($column_exists)) {
                error_log('PFB: Adding field_width column in ajax_get_form');
                require_once PFB_PLUGIN_DIR . 'includes/update-db.php';
                pfb_add_field_width_column();
            }

            // Check if conditional_logic column exists
            $conditional_logic_column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'conditional_logic'");
            error_log('PFB: conditional_logic column exists in ajax_get_form: ' . (empty($conditional_logic_column_exists) ? 'NO' : 'YES'));

            // If the column doesn't exist, add it
            if (empty($conditional_logic_column_exists)) {
                error_log('PFB: Adding conditional_logic column in ajax_get_form');

                // Include the update-db.php file which contains the function
                require_once PFB_PLUGIN_DIR . 'includes/update-db.php';

                // Call the function to add the conditional_logic column
                if (function_exists('pfb_add_conditional_logic_column')) {
                    pfb_add_conditional_logic_column();
                } else {
                    // Fallback: add the column directly if the function doesn't exist
                    $wpdb->query("ALTER TABLE {$table_name} ADD COLUMN conditional_logic LONGTEXT AFTER field_options");
                    error_log('PFB: Added conditional_logic column directly');
                }
            }

            // Get form
            error_log('PFB: Creating PFB_Form object');
            $form = new PFB_Form();

            error_log('PFB: Attempting to load form with ID: ' . $form_id);
            if (!$form->load($form_id)) {
                error_log('PFB: Failed to load form with ID: ' . $form_id);
                wp_send_json_error(array('message' => __('Form not found.', 'price-form-builder')));
                return;
            }

            // Debug: Log the form data
            $form_data = $form->get_data();
            error_log('PFB: Form data in ajax_get_form: ' . print_r($form_data, true));

            // Debug: Check if fields exist and are properly formatted
            if (isset($form_data['fields']) && is_array($form_data['fields'])) {
                error_log('PFB: Form has ' . count($form_data['fields']) . ' fields');

                // Log the first field for debugging
                if (!empty($form_data['fields'])) {
                    error_log('PFB: First field: ' . print_r($form_data['fields'][0], true));
                }
            } else {
                error_log('PFB: Form fields missing or not an array');
            }

            // Get translations
            $translations = array();

            if (isset($_GET['languages']) && is_array($_GET['languages'])) {
                foreach ($_GET['languages'] as $language_code) {
                    $translations[$language_code] = PFB_DB::get_translations('form', $form_id, $language_code);
                }
            }

            // Prepare response
            $response = array(
                'form' => $form_data,
                'translations' => $translations
            );

            error_log('PFB: Successfully prepared form data for response');
            wp_send_json_success($response);

        } catch (Exception $e) {
            error_log('PFB: Exception in ajax_get_form: ' . $e->getMessage());
            error_log('PFB: Exception trace: ' . $e->getTraceAsString());
            wp_send_json_error(array('message' => 'Error loading form: ' . $e->getMessage()));
        }
    }

    /**
     * AJAX handler for deleting a form.
     *
     * @since    1.0.0
     */
    public function ajax_delete_form() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get form ID
        $form_id = isset($_POST['form_id']) ? intval($_POST['form_id']) : 0;

        if (!$form_id) {
            wp_send_json_error(array('message' => __('No form ID provided.', 'price-form-builder')));
        }

        // Delete form
        $form = new PFB_Form();

        if (!$form->load($form_id)) {
            wp_send_json_error(array('message' => __('Form not found.', 'price-form-builder')));
        }

        if (!$form->delete()) {
            wp_send_json_error(array('message' => __('Failed to delete form.', 'price-form-builder')));
        }

        wp_send_json_success(array(
            'message' => __('Form deleted successfully.', 'price-form-builder')
        ));
    }

    /**
     * AJAX handler for saving a price variable.
     *
     * @since    1.0.0
     */
    public function ajax_save_price_variable() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get variable data
        $variable_data = isset($_POST['variable_data']) ? $_POST['variable_data'] : array();

        if (empty($variable_data)) {
            wp_send_json_error(array('message' => __('No variable data provided.', 'price-form-builder')));
        }

        // Save category if needed
        if (isset($variable_data['category']) && !empty($variable_data['category'])) {
            $category_data = array(
                'name' => sanitize_text_field($variable_data['category']),
                'description' => ''
            );

            $category_id = PFB_DB::save_price_category($category_data);
            $variable_data['category_id'] = $category_id;
        }

        // Save variable
        $variable_id = PFB_DB::save_price_variable($variable_data);

        if (!$variable_id) {
            wp_send_json_error(array('message' => __('Failed to save price variable.', 'price-form-builder')));
        }

        // Save translations if provided
        if (isset($_POST['translations']) && is_array($_POST['translations'])) {
            foreach ($_POST['translations'] as $language_code => $translation) {
                PFB_DB::save_translations('price_variable', $variable_id, $language_code, $translation);
            }
        }

        wp_send_json_success(array(
            'message' => __('Price variable saved successfully.', 'price-form-builder'),
            'variable_id' => $variable_id
        ));
    }

    /**
     * AJAX handler for getting price variables.
     *
     * @since    1.0.0
     */
    public function ajax_get_price_variables() {
        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get category ID
        $category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;

        // Get variables
        $args = array();

        if ($category_id) {
            $args['category_id'] = $category_id;
        }

        $variables = PFB_DB::get_price_variables($args);
        $categories = PFB_DB::get_price_categories();

        wp_send_json_success(array(
            'variables' => $variables,
            'categories' => $categories
        ));
    }

    /**
     * AJAX handler for deleting a price variable.
     *
     * @since    1.0.0
     */
    public function ajax_delete_price_variable() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get variable ID
        $variable_id = isset($_POST['variable_id']) ? intval($_POST['variable_id']) : 0;

        if (!$variable_id) {
            wp_send_json_error(array('message' => __('No variable ID provided.', 'price-form-builder')));
        }

        // Delete variable
        global $wpdb;
        $table_name = $wpdb->prefix . 'pfb_price_variables';
        $result = $wpdb->delete($table_name, array('id' => $variable_id));

        if ($result === false) {
            wp_send_json_error(array('message' => __('Failed to delete price variable.', 'price-form-builder')));
        }

        wp_send_json_success(array(
            'message' => __('Price variable deleted successfully.', 'price-form-builder')
        ));
    }

    /**
     * AJAX handler for saving a currency.
     *
     * @since    1.0.0
     */
    public function ajax_save_currency() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get currency data
        $currency_data = isset($_POST['currency_data']) ? $_POST['currency_data'] : array();

        if (empty($currency_data)) {
            wp_send_json_error(array('message' => __('No currency data provided.', 'price-form-builder')));
        }

        // Save currency
        $currency_id = PFB_DB::save_currency($currency_data);

        if (!$currency_id) {
            wp_send_json_error(array('message' => __('Failed to save currency.', 'price-form-builder')));
        }

        wp_send_json_success(array(
            'message' => __('Currency saved successfully.', 'price-form-builder'),
            'currency_id' => $currency_id
        ));
    }

    /**
     * AJAX handler for getting currencies.
     *
     * @since    1.0.0
     */
    public function ajax_get_currencies() {
        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get currencies
        $currencies = PFB_Currency::get_all_currencies();
        $default_currency = PFB_Currency::get_default_currency();

        wp_send_json_success(array(
            'currencies' => $currencies,
            'default_currency' => $default_currency
        ));
    }

    /**
     * AJAX handler for saving a price sheet.
     *
     * @since    1.0.0
     */
    public function ajax_save_price_sheet() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get form data
        parse_str($_POST['form_data'], $form_data);

        // Get category ID
        $category_id = isset($form_data['category_id']) ? intval($form_data['category_id']) : 0;

        if (!$category_id) {
            wp_send_json_error(array('message' => __('No category ID provided.', 'price-form-builder')));
        }

        // Get variables
        $variables = isset($form_data['variables']) ? $form_data['variables'] : array();

        if (empty($variables)) {
            wp_send_json_error(array('message' => __('No variables provided.', 'price-form-builder')));
        }

        // Save variables
        $saved_count = 0;

        foreach ($variables as $variable_id => $variable_data) {
            // Skip empty rows
            if (empty($variable_data['name']) || empty($variable_data['variable_key'])) {
                continue;
            }

            // Prepare variable data
            $data = array(
                'category_id' => $category_id,
                'name' => sanitize_text_field($variable_data['name']),
                'variable_key' => sanitize_key($variable_data['variable_key']),
                'price_type' => sanitize_text_field($variable_data['price_type'])
            );

            // Add ID if not a new variable
            if (strpos($variable_id, 'new_') !== 0) {
                $data['id'] = intval($variable_id);
            }

            // Add price value or ranges
            if ($data['price_type'] === 'fixed') {
                $data['price_value'] = floatval($variable_data['price_value']);
            } else {
                $ranges = json_decode($variable_data['price_ranges'], true);
                $data['price_ranges'] = $ranges;
            }

            // Save variable
            $result = PFB_DB::save_price_variable($data);

            if ($result) {
                $saved_count++;
            }
        }

        wp_send_json_success(array(
            'message' => sprintf(__('%d variables saved successfully.', 'price-form-builder'), $saved_count)
        ));
    }

    /**
     * AJAX handler for saving a price category.
     *
     * @since    1.0.0
     */
    public function ajax_save_price_category() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get category data
        $category_data = isset($_POST['category_data']) ? $_POST['category_data'] : array();

        if (empty($category_data)) {
            wp_send_json_error(array('message' => __('No category data provided.', 'price-form-builder')));
        }

        // Save category
        $category_id = PFB_DB::save_price_category($category_data);

        if (!$category_id) {
            wp_send_json_error(array('message' => __('Failed to save price category.', 'price-form-builder')));
        }

        wp_send_json_success(array(
            'message' => __('Price category saved successfully.', 'price-form-builder'),
            'category_id' => $category_id
        ));
    }

    /**
     * AJAX handler for getting a price category.
     *
     * @since    1.0.0
     */
    public function ajax_get_price_category() {
        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get category ID
        $category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;

        if (!$category_id) {
            wp_send_json_error(array('message' => __('No category ID provided.', 'price-form-builder')));
        }

        // Get category
        global $wpdb;
        $table_name = $wpdb->prefix . 'pfb_price_categories';

        $category = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $category_id),
            ARRAY_A
        );

        if (!$category) {
            wp_send_json_error(array('message' => __('Category not found.', 'price-form-builder')));
        }

        wp_send_json_success(array(
            'category' => $category
        ));
    }

    /**
     * AJAX handler for deleting a price category.
     *
     * @since    1.0.0
     */
    public function ajax_delete_price_category() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get category ID
        $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;

        if (!$category_id) {
            wp_send_json_error(array('message' => __('No category ID provided.', 'price-form-builder')));
        }

        // Check if category has variables
        global $wpdb;
        $variables_table = $wpdb->prefix . 'pfb_price_variables';

        $variables_count = $wpdb->get_var(
            $wpdb->prepare("SELECT COUNT(*) FROM $variables_table WHERE category_id = %d", $category_id)
        );

        if ($variables_count > 0) {
            wp_send_json_error(array('message' => __('Cannot delete a category that contains variables. Delete the variables first.', 'price-form-builder')));
        }

        // Delete category
        $table_name = $wpdb->prefix . 'pfb_price_categories';
        $result = $wpdb->delete($table_name, array('id' => $category_id));

        if ($result === false) {
            wp_send_json_error(array('message' => __('Failed to delete price category.', 'price-form-builder')));
        }

        wp_send_json_success(array(
            'message' => __('Price category deleted successfully.', 'price-form-builder')
        ));
    }

    /**
     * AJAX handler for getting a currency.
     *
     * @since    1.0.0
     */
    public function ajax_get_currency() {
        // Check nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get currency ID
        $currency_id = isset($_GET['currency_id']) ? intval($_GET['currency_id']) : 0;

        if (!$currency_id) {
            wp_send_json_error(array('message' => __('No currency ID provided.', 'price-form-builder')));
        }

        // Get currency
        global $wpdb;
        $table_name = $wpdb->prefix . 'pfb_currencies';

        $currency = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $currency_id),
            ARRAY_A
        );

        if (!$currency) {
            wp_send_json_error(array('message' => __('Currency not found.', 'price-form-builder')));
        }

        wp_send_json_success(array(
            'currency' => $currency
        ));
    }

    /**
     * AJAX handler for deleting a currency.
     *
     * @since    1.0.0
     */
    public function ajax_delete_currency() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get currency ID
        $currency_id = isset($_POST['currency_id']) ? intval($_POST['currency_id']) : 0;

        if (!$currency_id) {
            wp_send_json_error(array('message' => __('No currency ID provided.', 'price-form-builder')));
        }

        // Check if this is the default currency
        global $wpdb;
        $table_name = $wpdb->prefix . 'pfb_currencies';

        $is_default = $wpdb->get_var(
            $wpdb->prepare("SELECT is_default FROM $table_name WHERE id = %d", $currency_id)
        );

        if ($is_default == 1) {
            wp_send_json_error(array('message' => __('Cannot delete the default currency.', 'price-form-builder')));
        }

        // Delete currency
        $result = $wpdb->delete($table_name, array('id' => $currency_id));

        if ($result === false) {
            wp_send_json_error(array('message' => __('Failed to delete currency.', 'price-form-builder')));
        }

        wp_send_json_success(array(
            'message' => __('Currency deleted successfully.', 'price-form-builder')
        ));
    }

    /**
     * AJAX handler for setting a currency as default.
     *
     * @since    1.0.0
     */
    public function ajax_set_default_currency() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get currency ID
        $currency_id = isset($_POST['currency_id']) ? intval($_POST['currency_id']) : 0;

        if (!$currency_id) {
            wp_send_json_error(array('message' => __('No currency ID provided.', 'price-form-builder')));
        }

        // Set as default
        global $wpdb;
        $table_name = $wpdb->prefix . 'pfb_currencies';

        // First, unset all default currencies
        $wpdb->update(
            $table_name,
            array('is_default' => 0),
            array('is_default' => 1)
        );

        // Then set this currency as default
        $result = $wpdb->update(
            $table_name,
            array('is_default' => 1),
            array('id' => $currency_id)
        );

        if ($result === false) {
            wp_send_json_error(array('message' => __('Failed to set currency as default.', 'price-form-builder')));
        }

        wp_send_json_success(array(
            'message' => __('Currency set as default successfully.', 'price-form-builder')
        ));
    }

    /**
     * AJAX handler for saving settings.
     *
     * @since    1.0.0
     */
    public function ajax_save_settings() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get form data
        $form_data = isset($_POST['form_data']) ? $_POST['form_data'] : '';

        if (empty($form_data)) {
            wp_send_json_error(array('message' => __('No settings data provided.', 'price-form-builder')));
        }

        // Parse form data
        parse_str($form_data, $settings);

        // Save settings
        foreach ($settings as $key => $value) {
            update_option($key, $value);
        }

        wp_send_json_success(array(
            'message' => __('Settings saved successfully.', 'price-form-builder')
        ));
    }

    /**
     * Sanitize form data.
     *
     * @since    1.0.0
     * @param    array    $form_data    Form data to sanitize.
     * @return   array                  Sanitized form data.
     */
    private function sanitize_form_data($form_data) {
        $sanitized = array();

        if (isset($form_data['id'])) {
            $sanitized['id'] = intval($form_data['id']);
        }

        if (isset($form_data['title'])) {
            $sanitized['title'] = sanitize_text_field($form_data['title']);
        }

        if (isset($form_data['description'])) {
            $sanitized['description'] = sanitize_textarea_field($form_data['description']);
        }

        if (isset($form_data['status'])) {
            $sanitized['status'] = sanitize_text_field($form_data['status']);
        }

        // Handle form settings
        if (isset($form_data['settings']) && is_array($form_data['settings'])) {
            $sanitized['settings'] = array();

            // Sanitize each setting
            if (isset($form_data['settings']['template'])) {
                $sanitized['settings']['template'] = sanitize_text_field($form_data['settings']['template']);
            }

            if (isset($form_data['settings']['show_currency_selector'])) {
                $sanitized['settings']['show_currency_selector'] = sanitize_text_field($form_data['settings']['show_currency_selector']);
            }

            if (isset($form_data['settings']['submit_button_text'])) {
                $sanitized['settings']['submit_button_text'] = sanitize_text_field($form_data['settings']['submit_button_text']);
            }

            // Debug log for settings
            error_log('PFB: Sanitized form settings: ' . print_r($sanitized['settings'], true));
        }

        if (isset($form_data['fields']) && is_array($form_data['fields'])) {
            $sanitized['fields'] = array();

            foreach ($form_data['fields'] as $field) {
                $sanitized_field = array();

                if (isset($field['id'])) {
                    $sanitized_field['id'] = intval($field['id']);
                }

                if (isset($field['type'])) {
                    $sanitized_field['type'] = sanitize_text_field($field['type']);
                }

                if (isset($field['label'])) {
                    $sanitized_field['label'] = sanitize_text_field($field['label']);
                }

                if (isset($field['name'])) {
                    $sanitized_field['name'] = sanitize_key($field['name']);
                }

                if (isset($field['required'])) {
                    $sanitized_field['required'] = (bool) $field['required'];
                }

                // Handle field width
                if (isset($field['width'])) {
                    // Remove % symbol if present
                    $width = str_replace('%', '', $field['width']);

                    // Ensure it's a valid numeric value
                    if (is_numeric($width)) {
                        $sanitized_field['width'] = $width;
                    } else {
                        $sanitized_field['width'] = '100';
                    }
                } else {
                    $sanitized_field['width'] = '100';
                }

                // Handle conditional logic
                if (isset($field['conditional_logic']) && is_array($field['conditional_logic'])) {
                    $sanitized_field['conditional_logic'] = array();

                    // Sanitize logic type
                    if (isset($field['conditional_logic']['logic_type'])) {
                        $sanitized_field['conditional_logic']['logic_type'] = sanitize_text_field($field['conditional_logic']['logic_type']);
                    } else {
                        $sanitized_field['conditional_logic']['logic_type'] = 'all';
                    }

                    // Sanitize rules
                    if (isset($field['conditional_logic']['rules']) && is_array($field['conditional_logic']['rules'])) {
                        $sanitized_field['conditional_logic']['rules'] = array();

                        foreach ($field['conditional_logic']['rules'] as $rule) {
                            $sanitized_rule = array();

                            if (isset($rule['field'])) {
                                $sanitized_rule['field'] = intval($rule['field']);
                            }

                            if (isset($rule['operator'])) {
                                $sanitized_rule['operator'] = sanitize_text_field($rule['operator']);
                            }

                            if (isset($rule['value'])) {
                                $sanitized_rule['value'] = sanitize_text_field($rule['value']);
                            }

                            $sanitized_field['conditional_logic']['rules'][] = $sanitized_rule;
                        }
                    }

                    // Debug log
                    error_log('Sanitized conditional logic: ' . print_r($sanitized_field['conditional_logic'], true));
                }

                // Initialize options array if not set
                if (!isset($sanitized_field['options'])) {
                    $sanitized_field['options'] = array();
                }

                // Store hidden field properties in the options array
                if (isset($field['hidden']) && $field['hidden'] && $field['type'] === 'text') {
                    $sanitized_field['options']['is_hidden'] = true;

                    if (isset($field['variable'])) {
                        $sanitized_field['options']['variable'] = sanitize_text_field($field['variable']);
                    }

                    if (isset($field['default_value'])) {
                        $sanitized_field['options']['default_value'] = sanitize_text_field($field['default_value']);
                    }

                    // Debug log
                    error_log('Hidden field properties: ' . print_r(array(
                        'is_hidden' => true,
                        'variable' => isset($field['variable']) ? $field['variable'] : 'not set',
                        'default_value' => isset($field['default_value']) ? $field['default_value'] : 'not set'
                    ), true));
                }

                if (isset($field['options']) && is_array($field['options'])) {
                    // Store current options to merge later
                    $current_options = $sanitized_field['options'];
                    $sanitized_field['options'] = array();

                    // Special handling for subtotal fields
                    if ($field['type'] === 'subtotal') {
                        error_log('Processing subtotal field options in sanitize_form_data: ' . print_r($field['options'], true));

                        // Handle lines array
                        if (isset($field['options']['lines']) && is_array($field['options']['lines'])) {
                            $sanitized_field['options']['lines'] = array();

                            foreach ($field['options']['lines'] as $line) {
                                $sanitized_line = array();

                                // Ensure all required properties are set
                                $sanitized_line['label'] = isset($line['label']) ? sanitize_text_field($line['label']) : 'Line';
                                $sanitized_line['formula'] = isset($line['formula']) ? $line['formula'] : '';

                                $sanitized_field['options']['lines'][] = $sanitized_line;
                            }
                        } else {
                            // Create default lines array if not set
                            $sanitized_field['options']['lines'] = array(
                                array(
                                    'label' => 'Line 1',
                                    'formula' => ''
                                )
                            );
                        }

                        // Set empty value
                        $sanitized_field['options']['empty_value'] = isset($field['options']['empty_value']) ?
                            sanitize_text_field($field['options']['empty_value']) : '---';

                        error_log('Sanitized subtotal field options: ' . print_r($sanitized_field['options'], true));
                    } else {
                        // Standard processing for other field types
                        foreach ($field['options'] as $key => $value) {
                            if ($key === 'items' && is_array($value)) {
                                // Special handling for field options items (dropdown, radio, checkbox)
                                $sanitized_field['options'][$key] = array();

                                foreach ($value as $item) {
                                    if (is_array($item)) {
                                        $sanitized_item = array();

                                        // Ensure all required properties are set
                                        $sanitized_item['label'] = isset($item['label']) ? sanitize_text_field($item['label']) : 'Option';
                                        $sanitized_item['value'] = isset($item['value']) ? sanitize_text_field($item['value']) : '0';
                                        $sanitized_item['variable'] = isset($item['variable']) ? sanitize_text_field($item['variable']) : '';

                                        $sanitized_field['options'][$key][] = $sanitized_item;
                                    }
                                }
                            } elseif (is_array($value)) {
                                $sanitized_field['options'][$key] = $value; // Don't sanitize arrays
                            } else {
                                $sanitized_field['options'][$key] = sanitize_text_field($value);
                            }
                        }
                    }

                    // Merge with any previously set options (like hidden field properties)
                    $sanitized_field['options'] = array_merge($current_options, $sanitized_field['options']);

                    // Log sanitized options for debugging
                    error_log('Sanitized field options: ' . print_r($sanitized_field['options'], true));
                }

                $sanitized['fields'][] = $sanitized_field;
            }
        }

        return $sanitized;
    }

    /**
     * AJAX handler for testing formulas.
     *
     * @since    1.0.0
     */
    public function ajax_test_formula() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
        }

        // Get formula and test values
        $formula = isset($_POST['formula']) ? sanitize_text_field($_POST['formula']) : '';
        $x_value = isset($_POST['x_value']) ? floatval($_POST['x_value']) : 0;
        $y_value = isset($_POST['y_value']) ? floatval($_POST['y_value']) : 0;
        $z_value = isset($_POST['z_value']) ? floatval($_POST['z_value']) : 0;

        if (empty($formula)) {
            wp_send_json_error(array('message' => __('No formula provided.', 'price-form-builder')));
        }

        // Set up field values
        $field_values = array(
            'X' => $x_value,
            'Y' => $y_value,
            'Z' => $z_value
        );

        // Start output buffering to capture debug logs
        ob_start();

        // Test with Simple Calculator
        $simple_calculator = new PFB_Simple_Calculator(array(), true);
        $simple_result = $simple_calculator->calculate($formula, $field_values);

        // Test with Formula Parser
        $formula_parser = new PFB_Formula_Parser(array(), $field_values, true);
        $parser_result = $formula_parser->evaluate($formula);

        // Test with a modified formula (try to fix nested functions issue)
        $modified_formula = $this->modify_formula_for_testing($formula);
        $modified_result = $simple_calculator->calculate($modified_formula, $field_values);

        // Calculate manually for comparison
        $manual_result = $this->calculate_manually($formula, $x_value, $y_value, $z_value);

        // Get debug output
        $debug_info = ob_get_clean();

        wp_send_json_success(array(
            'simple_result' => $simple_result,
            'parser_result' => $parser_result,
            'modified_result' => $modified_result,
            'manual_result' => $manual_result,
            'debug_info' => $debug_info,
            'original_formula' => $formula,
            'modified_formula' => $modified_formula
        ));
    }

    /**
     * Modify formula for testing to fix nested functions issue.
     *
     * @since    1.0.0
     * @param    string    $formula    Original formula.
     * @return   string                Modified formula.
     */
    private function modify_formula_for_testing($formula) {
        // Replace nested functions with a different approach
        // For example: (floor(max(0,{X}-5000)/1000))*{Z} => floor(max(0,{X}-5000)/1000)*{Z}

        // Remove unnecessary parentheses around function calls
        $formula = preg_replace('/\((\s*(?:floor|ceil|round|min|max)\s*\([^()]*(?:\([^()]*\)[^()]*)*\))\s*\)/', '$1', $formula);

        // Ensure proper spacing around operators
        $formula = preg_replace('/\s*\*\s*/', ' * ', $formula);
        $formula = preg_replace('/\s*\/\s*/', ' / ', $formula);
        $formula = preg_replace('/\s*\+\s*/', ' + ', $formula);
        $formula = preg_replace('/\s*\-\s*/', ' - ', $formula);

        return $formula;
    }

    /**
     * Calculate formula manually for comparison.
     *
     * @since    1.0.0
     * @param    string    $formula    Formula to calculate.
     * @param    float     $x          X value.
     * @param    float     $y          Y value.
     * @param    float     $z          Z value.
     * @return   float                 Calculated result.
     */
    private function calculate_manually($formula, $x, $y, $z) {
        // Check for common formula patterns and calculate them directly

        // Pattern: (floor(max(0,{X}-5000)/1000))*{Z}
        if (preg_match('/\(?floor\(max\(0,\s*\{X\}\s*-\s*(\d+)\)\s*\/\s*(\d+)\)\)?\s*\*\s*\{Z\}/', $formula, $matches)) {
            $threshold = floatval($matches[1]);
            $divisor = floatval($matches[2]);

            $diff = max(0, $x - $threshold);
            $divided = $diff / $divisor;
            $floored = floor($divided);
            $result = $floored * $z;

            return $result;
        }

        // Pattern: max(0, {X}-{Y})
        if (preg_match('/max\(0,\s*\{X\}\s*-\s*\{Y\}\)/', $formula)) {
            return max(0, $x - $y);
        }

        // Pattern: floor({X}/{Y})
        if (preg_match('/floor\(\{X\}\s*\/\s*\{Y\}\)/', $formula)) {
            return floor($x / $y);
        }

        // Pattern: ceil({X}/{Y})
        if (preg_match('/ceil\(\{X\}\s*\/\s*\{Y\}\)/', $formula)) {
            return ceil($x / $y);
        }

        // Default fallback - try to evaluate the formula by replacing variables
        $eval_formula = str_replace(
            array('{X}', '{Y}', '{Z}'),
            array($x, $y, $z),
            $formula
        );

        // Remove any remaining curly braces
        $eval_formula = preg_replace('/\{([^{}]+)\}/', '0', $eval_formula);

        // Try to evaluate using PHP's built-in functions
        try {
            $eval_formula = $this->sanitize_formula_for_eval($eval_formula);
            $result = 0;
            eval('$result = ' . $eval_formula . ';');
            return $result;
        } catch (Exception $e) {
            return 0;
        }
    }

    /**
     * Sanitize formula for safe evaluation.
     *
     * @since    1.0.0
     * @param    string    $formula    Formula to sanitize.
     * @return   string                Sanitized formula.
     */
    private function sanitize_formula_for_eval($formula) {
        // Only allow numbers, basic operators, parentheses, and specific function names
        $formula = preg_replace('/[^0-9\+\-\*\/\(\)\.\s,florceimnaxd]/', '', $formula);

        // Replace function names with PHP equivalents
        $formula = preg_replace('/floor\s*\(/', 'floor(', $formula);
        $formula = preg_replace('/ceil\s*\(/', 'ceil(', $formula);
        $formula = preg_replace('/round\s*\(/', 'round(', $formula);
        $formula = preg_replace('/min\s*\(/', 'min(', $formula);
        $formula = preg_replace('/max\s*\(/', 'max(', $formula);

        return $formula;
    }

    /**
     * AJAX handler for saving a form with complete field options handling.
     *
     * @since    1.0.0
     */
    public function ajax_save_form_complete() {
        try {
            error_log('PFB Admin: AJAX save form complete request received');

            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
                error_log('PFB Admin: Nonce verification failed');
                wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
                return;
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                error_log('PFB Admin: Permission check failed');
                wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
                return;
            }

            // Get form data
            $form_data = isset($_POST['form_data']) ? $_POST['form_data'] : array();

            if (empty($form_data)) {
                error_log('PFB Admin: No form data provided');
                wp_send_json_error(array('message' => __('No form data provided.', 'price-form-builder')));
                return;
            }

            error_log('PFB Admin: Form data received: ' . print_r($form_data, true));

            // Use the new form handler
            $form_handler = new PFB_Form_Handler();
            $result = $form_handler->save_form($form_data);

            if ($result['success']) {
                error_log('PFB Admin: Form saved successfully with ID: ' . $result['form_id']);
                wp_send_json_success(array(
                    'message' => __('Form saved successfully.', 'price-form-builder'),
                    'form_id' => $result['form_id']
                ));
            } else {
                error_log('PFB Admin: Form save failed: ' . $result['message']);
                wp_send_json_error(array('message' => $result['message']));
            }
        } catch (Exception $e) {
            error_log('PFB Admin: Exception in ajax_save_form_complete: ' . $e->getMessage());
            error_log('PFB Admin: Exception trace: ' . $e->getTraceAsString());
            wp_send_json_error(array('message' => 'Error saving form: ' . $e->getMessage()));
        }
    }

    /**
     * AJAX handler for saving a form using the new v2 handler.
     *
     * @since    1.0.0
     */
    public function ajax_save_form_v2() {
        try {
            error_log('PFB Admin: AJAX save form v2 request received');

            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
                error_log('PFB Admin: Nonce verification failed');
                wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
                return;
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                error_log('PFB Admin: Permission check failed');
                wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
                return;
            }

            // Get form data
            $form_data = isset($_POST['form_data']) ? $_POST['form_data'] : array();

            if (empty($form_data)) {
                error_log('PFB Admin: No form data provided');
                wp_send_json_error(array('message' => __('No form data provided.', 'price-form-builder')));
                return;
            }

            error_log('PFB Admin: Form data received: ' . print_r($form_data, true));

            // Create form save handler
            $form_save_handler = new PFB_Form_Save_Handler();

            // Save form
            $result = $form_save_handler->save_form($form_data);

            if ($result['success']) {
                error_log('PFB Admin: Form saved successfully with ID: ' . $result['form_id']);
                wp_send_json_success(array(
                    'message' => __('Form saved successfully.', 'price-form-builder'),
                    'form_id' => $result['form_id']
                ));
            } else {
                error_log('PFB Admin: Form save failed: ' . $result['message']);
                wp_send_json_error(array('message' => $result['message']));
            }
        } catch (Exception $e) {
            error_log('PFB Admin: Exception in ajax_save_form_v2: ' . $e->getMessage());
            error_log('PFB Admin: Exception trace: ' . $e->getTraceAsString());
            wp_send_json_error(array('message' => 'Error saving form: ' . $e->getMessage()));
        }
    }

    /**
     * AJAX handler for checking the database structure.
     *
     * @since    1.0.0
     */
    public function ajax_check_database() {
        try {
            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
                wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
                return;
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_send_json_error(array('message' => __('You do not have permission to do this.', 'price-form-builder')));
                return;
            }

            global $wpdb;
            $results = array();

            // Check forms table
            $forms_table = $wpdb->prefix . 'pfb_forms';
            $forms_exists = $wpdb->get_var("SHOW TABLES LIKE '{$forms_table}'") === $forms_table;
            $results['forms_table_exists'] = $forms_exists;

            if ($forms_exists) {
                // Check forms table columns
                $forms_columns = $wpdb->get_results("SHOW COLUMNS FROM {$forms_table}");
                $forms_column_names = array_map(function($col) { return $col->Field; }, $forms_columns);
                $results['forms_columns'] = $forms_column_names;
            }

            // Check form fields table
            $fields_table = $wpdb->prefix . 'pfb_form_fields';
            $fields_exists = $wpdb->get_var("SHOW TABLES LIKE '{$fields_table}'") === $fields_table;
            $results['fields_table_exists'] = $fields_exists;

            if ($fields_exists) {
                // Check form fields table columns
                $fields_columns = $wpdb->get_results("SHOW COLUMNS FROM {$fields_table}");
                $fields_column_names = array_map(function($col) { return $col->Field; }, $fields_columns);
                $results['fields_columns'] = $fields_column_names;

                // Check if field_width column exists
                $field_width_exists = in_array('field_width', $fields_column_names);
                $results['field_width_exists'] = $field_width_exists;

                // Check if conditional_logic column exists
                $conditional_logic_exists = in_array('conditional_logic', $fields_column_names);
                $results['conditional_logic_exists'] = $conditional_logic_exists;

                // Add columns if they don't exist
                if (!$field_width_exists || !$conditional_logic_exists) {
                    require_once PFB_PLUGIN_DIR . 'includes/update-db.php';

                    if (!$field_width_exists) {
                        pfb_add_field_width_column();
                        $results['field_width_added'] = true;
                    }

                    if (!$conditional_logic_exists) {
                        if (function_exists('pfb_add_conditional_logic_column')) {
                            pfb_add_conditional_logic_column();
                            $results['conditional_logic_added'] = true;
                        } else {
                            // Fallback: add the column directly if the function doesn't exist
                            $wpdb->query("ALTER TABLE {$fields_table} ADD COLUMN conditional_logic LONGTEXT AFTER field_options");
                            $results['conditional_logic_added_directly'] = true;
                        }
                    }

                    // Check again after adding columns
                    $fields_columns = $wpdb->get_results("SHOW COLUMNS FROM {$fields_table}");
                    $fields_column_names = array_map(function($col) { return $col->Field; }, $fields_columns);
                    $results['fields_columns_after_update'] = $fields_column_names;
                }
            }

            // Check price categories table
            $categories_table = $wpdb->prefix . 'pfb_price_categories';
            $categories_exists = $wpdb->get_var("SHOW TABLES LIKE '{$categories_table}'") === $categories_table;
            $results['categories_table_exists'] = $categories_exists;

            // Check price variables table
            $variables_table = $wpdb->prefix . 'pfb_price_variables';
            $variables_exists = $wpdb->get_var("SHOW TABLES LIKE '{$variables_table}'") === $variables_table;
            $results['variables_table_exists'] = $variables_exists;

            // Check translations table
            $translations_table = $wpdb->prefix . 'pfb_translations';
            $translations_exists = $wpdb->get_var("SHOW TABLES LIKE '{$translations_table}'") === $translations_table;
            $results['translations_table_exists'] = $translations_exists;

            // Check if we can get a form
            if ($forms_exists && $fields_exists) {
                $form = $wpdb->get_row("SELECT * FROM {$forms_table} LIMIT 1");
                $results['can_get_form'] = !empty($form);

                if (!empty($form)) {
                    $form_id = $form->id;
                    $results['form_id'] = $form_id;

                    // Check if we can get form fields
                    $fields = $wpdb->get_results($wpdb->prepare("SELECT * FROM {$fields_table} WHERE form_id = %d", $form_id));
                    $results['can_get_fields'] = !empty($fields);
                    $results['field_count'] = count($fields);

                    if (!empty($fields)) {
                        // Check first field
                        $first_field = $fields[0];
                        $results['first_field_id'] = $first_field->id;
                        $results['first_field_type'] = $first_field->field_type;
                        $results['first_field_has_options'] = !empty($first_field->field_options);
                        $results['first_field_has_conditional_logic'] = !empty($first_field->conditional_logic);

                        // Try to unserialize field options
                        if (!empty($first_field->field_options)) {
                            $unserialized = maybe_unserialize($first_field->field_options);
                            $results['first_field_options_unserialized'] = is_array($unserialized) || is_object($unserialized);
                        }

                        // Try to decode conditional logic
                        if (!empty($first_field->conditional_logic)) {
                            $decoded = json_decode($first_field->conditional_logic, true);
                            $results['first_field_conditional_logic_decoded'] = $decoded !== null;
                        }
                    }
                }
            }

            wp_send_json_success($results);
        } catch (Exception $e) {
            wp_send_json_error(array('message' => 'Error checking database: ' . $e->getMessage()));
        }
    }
}
