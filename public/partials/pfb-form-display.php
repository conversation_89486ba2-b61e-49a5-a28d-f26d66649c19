<?php
/**
 * Public form display template.
 *
 * @since      1.0.0
 */

// No includes here - we'll fix the issue directly

// Get form data
$form_id = $this->get_id();
$form_title = $this->get_title();
$form_description = $this->get_description();
$form_fields = $this->get_fields();

// Check if we should show the title
$show_title = isset($atts['title']) ? filter_var($atts['title'], FILTER_VALIDATE_BOOLEAN) : true;

// Get translations if needed
if (!empty($translations)) {
    if (isset($translations['title'])) {
        $form_title = $translations['title'];
    }

    if (isset($translations['description'])) {
        $form_description = $translations['description'];
    }
}
?>

<?php
// Get form template from settings or use default
$form_template = isset($form_settings['template']) ? $form_settings['template'] : 'default';

// Debug log for template
error_log('PFB: Using template for form ' . $form_id . ': ' . $form_template);
error_log('PFB: Full form settings: ' . print_r($form_settings, true));

// Add HTML comment for debugging
echo "<!-- Using template: " . esc_html($form_template) . " -->\n";
?>
<div class="pfb-form-container<?php echo is_rtl() ? ' is-rtl' : ' is-ltr'; ?> pfb-template-<?php echo esc_attr($form_template); ?>" id="pfb-form-<?php echo esc_attr($form_id); ?>" dir="<?php echo is_rtl() ? 'rtl' : 'ltr'; ?>">
    <?php if ($show_title && !empty($form_title)) : ?>
        <div class="pfb-form-header">
            <h2><?php echo esc_html($form_title); ?></h2>
            <?php if (!empty($form_description)) : ?>
                <p><?php echo esc_html($form_description); ?></p>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <div class="pfb-form-content">
        <form class="pfb-form" data-form-id="<?php echo esc_attr($form_id); ?>">
            <?php
            // Add hidden fields for all price variables
            // Get variables from the form object instead of directly calling PFB_Variable
            $price_variables = array();

            // Get variables directly from the database
            global $wpdb;
            $table_name = $wpdb->prefix . 'pfb_price_variables';
            $db_variables = $wpdb->get_results("SELECT * FROM $table_name", ARRAY_A);

            if ($db_variables) {
                foreach ($db_variables as $var) {
                    $price_variables[] = array(
                        'variable_key' => $var['variable_key'],
                        'value' => $var['price_value']
                    );
                }
                error_log('Got variables directly from database: ' . count($price_variables));
            }

            // If no variables found in database, try other methods
            if (empty($price_variables)) {
                // Get variables from the form object
                if (isset($form_object) && method_exists($form_object, 'get_variables')) {
                    $price_variables = $form_object->get_variables();
                    error_log('Got variables from form object: ' . count($price_variables));
                } else {
                    // Fallback to global variables if available
                    global $pfb_variables;
                    if (isset($pfb_variables) && is_array($pfb_variables)) {
                        $price_variables = $pfb_variables;
                        error_log('Got variables from global: ' . count($price_variables));
                    } else {
                        error_log('No variables found for form');
                    }
                }
            }

            // Always add a hidden field for the 'print' variable even if not in the variables list
            echo '<!-- Hidden fields for price variables -->';
            echo '<div class="pfb-hidden-variables" style="display:none;">';

            // First, check if 'print' is already in the variables list
            $print_found = false;
            if (!empty($price_variables)) {
                foreach ($price_variables as $variable) {
                    if (isset($variable['variable_key']) && isset($variable['value'])) {
                        $var_key = $variable['variable_key'];
                        $var_value = is_array($variable['value']) ? (isset($variable['value']['value']) ? $variable['value']['value'] : 0) : $variable['value'];

                        // Check if this is the 'print' variable
                        if ($var_key === 'print') {
                            $print_found = true;
                        }

                        echo '<input type="hidden" name="pfb_var_' . esc_attr($var_key) . '" value="' . esc_attr($var_value) . '" class="pfb-variable-input" data-variable="' . esc_attr($var_key) . '">';
                        error_log('Added hidden field for variable: ' . $var_key . ' with value: ' . $var_value);
                    }
                }
            }

            // If 'print' wasn't found, add it manually
            if (!$print_found) {
                // Try to get the print variable from the database
                global $wpdb;
                $table_name = $wpdb->prefix . 'pfb_variables';
                $print_var = $wpdb->get_row(
                    $wpdb->prepare("SELECT * FROM $table_name WHERE variable_key = %s", 'print'),
                    ARRAY_A
                );

                if ($print_var) {
                    $print_value = isset($print_var['value']) ? $print_var['value'] : (isset($print_var['price']) ? $print_var['price'] : 0);
                    echo '<input type="hidden" name="pfb_var_print" value="' . esc_attr($print_value) . '" class="pfb-variable-input" data-variable="print">';
                    error_log('Added hidden field for print variable with value: ' . $print_value);
                } else {
                    // If not found in database, add with default value 0
                    echo '<input type="hidden" name="pfb_var_print" value="0" class="pfb-variable-input" data-variable="print">';
                    error_log('Added hidden field for print variable with default value 0');
                }
            }

            echo '</div>';
            ?>

            <?php if (!empty($form_fields)) : ?>
                <?php foreach ($form_fields as $field) : ?>
                    <?php
                    // Get field data
                    $field_type = isset($field['field_type']) ? $field['field_type'] : 'text';
                    $field_label = isset($field['field_label']) ? $field['field_label'] : '';
                    $field_name = isset($field['field_name']) ? $field['field_name'] : '';
                    // Get field options
                    $field_options = isset($field['field_options']) ? maybe_unserialize($field['field_options']) : array();

                    // Check if field is hidden (either from field_hidden column or options array)
                    $field_hidden = false;
                    if ($field_type === 'text') {
                        $field_hidden = (isset($field['field_hidden']) && $field['field_hidden'] == 1) ||
                                       (isset($field_options['is_hidden']) && $field_options['is_hidden'] === true);
                    }

                    // If the field is hidden, it should never be required
                    $field_required = !$field_hidden && isset($field['field_required']) && $field['field_required'] == 1;

                    // Debug: Log field hidden status
                    error_log('Field ' . $field_name . ' hidden status: ' . ($field_hidden ? 'true' : 'false'));
                    error_log('Field ' . $field_name . ' required status: ' . ($field_required ? 'true' : 'false'));
                    error_log('Field ' . $field_name . ' options: ' . print_r($field_options, true));

                    // Debug: Log the raw field options before unserialization
                    if (isset($field['field_options'])) {
                        // Check if field_options is already an array (which would cause an error when converting to string)
                        if (is_array($field['field_options'])) {
                            error_log('Raw field options for field ' . $field['id'] . ' (' . $field_type . '): ' . print_r($field['field_options'], true));
                        } else {
                            error_log('Raw field options for field ' . $field['id'] . ' (' . $field_type . '): ' . $field['field_options']);
                        }
                    }

                    $field_options = isset($field['field_options']) ? maybe_unserialize($field['field_options']) : array();

                    // Debug: Log the unserialized field options
                    error_log('Unserialized field options for field ' . $field['id'] . ' (' . $field_type . '): ' . print_r($field_options, true));

                    // Check for translations
                    $field_key = 'field_' . (isset($field['id']) ? $field['id'] : '0') . '_label';
                    if (!empty($translations[$field_key])) {
                        $field_label = $translations[$field_key];
                    }
                    ?>

                    <?php
                    // Get field width if available
                    $field_options = isset($field['field_options']) ? maybe_unserialize($field['field_options']) : array();
                    $field_width = 100; // Default width

                    // Check if width is in field_options
                    if (isset($field_options['width'])) {
                        $field_width = intval($field_options['width']);
                    }
                    // Fallback to field_width if it exists
                    elseif (isset($field['field_width'])) {
                        $field_width = intval($field['field_width']);
                    }

                    $width_class = 'pfb-field-width-' . $field_width;

                    // Get conditional logic if available
                    $conditional_logic_attr = '';
                    $conditional_logic_data = '';
                    if (isset($field['conditional_logic']) && !empty($field['conditional_logic'])) {
                        // Debug log for conditional logic
                        error_log('PFB: Field ' . $field['id'] . ' has conditional logic: ' . print_r($field['conditional_logic'], true));

                        // If conditional_logic is already a string (JSON), use it directly
                        if (is_string($field['conditional_logic'])) {
                            // Make sure it's valid JSON
                            $decoded = json_decode($field['conditional_logic'], true);
                            if ($decoded !== null) {
                                $conditional_logic_data = htmlspecialchars($field['conditional_logic'], ENT_QUOTES, 'UTF-8');
                                error_log('PFB: Using existing JSON string for conditional logic');
                            } else {
                                // Not valid JSON, encode the array
                                $conditional_logic_data = htmlspecialchars(json_encode($field['conditional_logic']), ENT_QUOTES, 'UTF-8');
                                error_log('PFB: Encoded conditional logic as JSON');
                            }
                        } else {
                            // It's an array, encode it
                            $conditional_logic_data = htmlspecialchars(json_encode($field['conditional_logic']), ENT_QUOTES, 'UTF-8');
                            error_log('PFB: Encoded conditional logic array as JSON');
                        }

                        $conditional_logic_attr = 'data-conditional-logic="' . $conditional_logic_data . '"';
                        error_log('PFB: Conditional logic attribute for field ' . $field['id'] . ': ' . $conditional_logic_attr);
                    } else {
                        error_log('PFB: Field ' . $field['id'] . ' has no conditional logic');
                    }
                    ?>
                    <div class="pfb-field pfb-field-<?php echo esc_attr($field_type); ?> <?php echo esc_attr($width_class); ?><?php echo ($field_type === 'text' && $field_hidden) ? ' pfb-field-hidden' : ''; ?>" data-field-id="<?php echo esc_attr($field['id']); ?>" data-field-name="<?php echo esc_attr($field_name); ?>" data-type="<?php echo esc_attr($field_type); ?>" <?php echo $conditional_logic_attr; ?> <?php echo ($field_type === 'text' && $field_hidden) ? 'aria-hidden="true"' : ''; ?>>
                        <?php if ($field_type !== 'total' && !($field_type === 'text' && $field_hidden)) : ?>
                            <label class="pfb-field-label">
                                <?php echo esc_html($field_label); ?>
                                <?php if ($field_required) : ?>
                                    <span class="pfb-field-required">*</span>
                                <?php endif; ?>
                            </label>
                        <?php endif; ?>

                        <?php switch ($field_type) :
                            case 'text':
                                if ($field_hidden) :
                                    // Get default value or variable value
                                    $default_value = '';

                                    // Check for variable in field_options first (new method)
                                    if (isset($field_options['variable']) && !empty($field_options['variable'])) {
                                        $variable_key = $field_options['variable'];

                                        // Debug log
                                        error_log('Looking for variable: ' . $variable_key);

                                        // First check if we have a hidden input with this variable already
                                        $variable_input_name = 'pfb_var_' . $variable_key;
                                        $default_value = '';

                                        // Look for the variable in the price_variables array
                                        if (!empty($price_variables)) {
                                            foreach ($price_variables as $variable) {
                                                if (isset($variable['variable_key']) && $variable['variable_key'] === $variable_key) {
                                                    $default_value = isset($variable['value']) ? $variable['value'] : '';
                                                    error_log('Found variable in price_variables: ' . $variable_key . ' = ' . $default_value);
                                                    break;
                                                }
                                            }
                                        }

                                        // If not found in variables, try to get from database
                                        if (empty($default_value)) {
                                            global $wpdb;
                                            $table_name = $wpdb->prefix . 'pfb_price_variables';
                                            error_log('Looking for variable in database table: ' . $table_name);

                                            $var_data = $wpdb->get_row(
                                                $wpdb->prepare("SELECT * FROM $table_name WHERE variable_key = %s", $variable_key),
                                                ARRAY_A
                                            );

                                            if ($var_data) {
                                                $default_value = isset($var_data['price_value']) ? $var_data['price_value'] : '';
                                                error_log('Found variable in database: ' . $variable_key . ' = ' . $default_value);
                                            } else {
                                                error_log('Variable not found in database: ' . $variable_key);
                                            }
                                        }
                                    }
                                    // Check for default_value in field_options (new method)
                                    elseif (isset($field_options['default_value']) && !empty($field_options['default_value'])) {
                                        $default_value = $field_options['default_value'];
                                    }
                                    // Fallback to old method for backward compatibility
                                    elseif (isset($field['field_variable']) && !empty($field['field_variable'])) {
                                        // Try to get the variable value from the variables list
                                        $variable_key = $field['field_variable'];

                                        // Debug log
                                        error_log('Looking for variable (old method): ' . $variable_key);

                                        // Look for the variable in the price_variables array
                                        if (!empty($price_variables)) {
                                            foreach ($price_variables as $variable) {
                                                if (isset($variable['variable_key']) && $variable['variable_key'] === $variable_key) {
                                                    $default_value = isset($variable['value']) ? $variable['value'] : '';
                                                    error_log('Found variable in price_variables (old method): ' . $variable_key . ' = ' . $default_value);
                                                    break;
                                                }
                                            }
                                        }

                                        // If not found in variables, try to get from database
                                        if (empty($default_value)) {
                                            global $wpdb;
                                            $table_name = $wpdb->prefix . 'pfb_price_variables';
                                            error_log('Looking for variable in database table (old method): ' . $table_name);

                                            $var_data = $wpdb->get_row(
                                                $wpdb->prepare("SELECT * FROM $table_name WHERE variable_key = %s", $variable_key),
                                                ARRAY_A
                                            );

                                            if ($var_data) {
                                                $default_value = isset($var_data['price_value']) ? $var_data['price_value'] : '';
                                                error_log('Found variable in database (old method): ' . $variable_key . ' = ' . $default_value);
                                            } else {
                                                error_log('Variable not found in database (old method): ' . $variable_key);
                                            }
                                        }
                                    } elseif (isset($field['field_default_value']) && !empty($field['field_default_value'])) {
                                        // Use the specified default value
                                        $default_value = $field['field_default_value'];
                                        error_log('Using default value: ' . $default_value);
                                    }

                                    // Get the variable key from either the field options or the field variable
                                    $variable_key = '';
                                    if (isset($field_options['variable']) && !empty($field_options['variable'])) {
                                        $variable_key = $field_options['variable'];
                                    } elseif (isset($field['field_variable']) && !empty($field['field_variable'])) {
                                        $variable_key = $field['field_variable'];
                                    }

                                    // If we have a variable key but no value yet, try to get it directly from the database
                                    if (!empty($variable_key) && (empty($default_value) || $default_value == '0' || $default_value == '0.00')) {
                                        global $wpdb;
                                        $table_name = $wpdb->prefix . 'pfb_price_variables';

                                        $var_data = $wpdb->get_row(
                                            $wpdb->prepare("SELECT * FROM $table_name WHERE variable_key = %s", $variable_key),
                                            ARRAY_A
                                        );

                                        if ($var_data) {
                                            $default_value = isset($var_data['price_value']) ? $var_data['price_value'] : '';
                                            error_log('Direct database lookup for ' . $variable_key . ': ' . $default_value);
                                        }
                                    }

                                    // Debug log the value
                                    error_log('Hidden field ' . $field_name . ' final default value: ' . $default_value);

                                    // Log the final value for debugging
                                    error_log('Final value for ' . $variable_key . ': ' . $default_value);
                                    ?>
                                    <input type="hidden" name="<?php echo esc_attr($field_name); ?>" class="pfb-field-input pfb-hidden-input" value="<?php echo esc_attr($default_value); ?>" data-variable="<?php echo isset($variable_key) ? esc_attr($variable_key) : ''; ?>">
                                <?php else : ?>
                                    <input type="text" name="<?php echo esc_attr($field_name); ?>" class="pfb-field-input" <?php echo $field_required ? 'required' : ''; ?>>
                                <?php endif; ?>
                                <?php break;

                            case 'number': ?>
                                <input type="number" name="<?php echo esc_attr($field_name); ?>" class="pfb-field-input" <?php echo $field_required ? 'required' : ''; ?>>
                                <?php break;

                            case 'slider':
                                // Get slider options
                                $min = isset($field_options['min']) ? intval($field_options['min']) : 0;
                                $max = isset($field_options['max']) ? intval($field_options['max']) : 100;
                                $step = isset($field_options['step']) ? intval($field_options['step']) : 1;
                                $default = isset($field_options['default']) ? intval($field_options['default']) : 50;

                                // Ensure default is within min and max
                                $default = max($min, min($max, $default));
                                ?>
                                <div class="pfb-slider-container">
                                    <input type="range"
                                           name="<?php echo esc_attr($field_name); ?>"
                                           class="pfb-slider-input"
                                           min="<?php echo esc_attr($min); ?>"
                                           max="<?php echo esc_attr($max); ?>"
                                           step="<?php echo esc_attr($step); ?>"
                                           value="<?php echo esc_attr($default); ?>"
                                           <?php echo $field_required ? 'required' : ''; ?>>
                                    <div class="pfb-slider-value-display">
                                        <span class="pfb-slider-current-value"><?php echo esc_html($default); ?></span>
                                    </div>
                                </div>
                                <?php break;

                            case 'dropdown': ?>
                                <select name="<?php echo esc_attr($field_name); ?>" class="pfb-field-select" <?php echo $field_required ? 'required' : ''; ?>>
                                    <option value=""><?php _e('Select an option', 'price-form-builder'); ?></option>
                                    <?php
                                    // Debug output
                                    error_log('Field options for dropdown: ' . print_r($field_options, true));

                                    // Ensure field_options['items'] is an array
                                    if (!isset($field_options['items']) || !is_array($field_options['items'])) {
                                        $field_options['items'] = array(
                                            array(
                                                'label' => 'Option 1',
                                                'value' => 'option_1',
                                                'variable' => ''
                                            )
                                        );
                                        error_log('Created default options for dropdown field');
                                    }

                                    if (isset($field_options['items']) && is_array($field_options['items'])) : ?>
                                        <?php foreach ($field_options['items'] as $item) : ?>
                                            <?php
                                            // Ensure we have valid values
                                            $item_label = isset($item['label']) ? $item['label'] : 'Option';
                                            $item_value = isset($item['value']) ? $item['value'] : $item_label;
                                            $item_variable = isset($item['variable']) ? $item['variable'] : '';

                                            // Debug output for each option
                                            error_log("Dropdown option: label=$item_label, value=$item_value, variable=$item_variable");

                                            // Check for translations
                                            $item_key = 'field_' . (isset($field['id']) ? $field['id'] : '0') . '_option_' . sanitize_key($item_label);
                                            if (!empty($translations[$item_key])) {
                                                $item_label = $translations[$item_key];
                                            }

                                            // If the value is a variable placeholder, store it in a data attribute
                                            $data_variable = '';
                                            if (!empty($item_variable)) {
                                                $data_variable = ' data-variable="' . esc_attr($item_variable) . '"';
                                            }
                                            ?>
                                            <option value="<?php echo esc_attr($item_value); ?>"<?php echo $data_variable; ?>><?php echo esc_html($item_label); ?></option>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <option value=""><?php _e('No options available', 'price-form-builder'); ?></option>
                                    <?php endif; ?>
                                </select>
                                <?php break;

                            case 'radio': ?>
                                <div class="pfb-field-radio">
                                    <?php
                                    // Debug output
                                    error_log('Field options for radio: ' . print_r($field_options, true));

                                    // Ensure field_options['items'] is an array
                                    if (!isset($field_options['items']) || !is_array($field_options['items'])) {
                                        $field_options['items'] = array(
                                            array(
                                                'label' => 'Option 1',
                                                'value' => 'option_1',
                                                'variable' => ''
                                            )
                                        );
                                        error_log('Created default options for radio field');
                                    }

                                    if (isset($field_options['items']) && is_array($field_options['items'])) : ?>
                                        <?php foreach ($field_options['items'] as $item) : ?>
                                            <?php
                                            // Ensure we have valid values
                                            $item_label = isset($item['label']) ? $item['label'] : 'Option';
                                            $item_value = isset($item['value']) ? $item['value'] : $item_label;
                                            $item_variable = isset($item['variable']) ? $item['variable'] : '';

                                            // Debug output for each option
                                            error_log("Radio option: label=$item_label, value=$item_value, variable=$item_variable");

                                            // Check for translations
                                            $item_key = 'field_' . (isset($field['id']) ? $field['id'] : '0') . '_option_' . sanitize_key($item_label);
                                            if (!empty($translations[$item_key])) {
                                                $item_label = $translations[$item_key];
                                            }

                                            // If the value is a variable placeholder, store it in a data attribute
                                            $data_variable = '';
                                            if (!empty($item_variable)) {
                                                $data_variable = ' data-variable="' . esc_attr($item_variable) . '"';
                                            }
                                            ?>
                                            <div class="pfb-radio-item">
                                                <input type="radio" id="<?php echo esc_attr($field_name . '_' . sanitize_key($item_label)); ?>" name="<?php echo esc_attr($field_name); ?>" value="<?php echo esc_attr($item_value); ?>" class="pfb-radio-input" <?php echo $field_required ? 'required' : ''; ?><?php echo $data_variable; ?>>
                                                <label for="<?php echo esc_attr($field_name . '_' . sanitize_key($item_label)); ?>" class="pfb-radio-label"><?php echo esc_html($item_label); ?></label>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="pfb-radio-item">
                                            <p><?php _e('No options available', 'price-form-builder'); ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <?php break;

                            case 'checkbox': ?>
                                <div class="pfb-field-checkbox">
                                    <?php
                                    // Debug output
                                    error_log('Field options for checkbox: ' . print_r($field_options, true));

                                    // Ensure field_options['items'] is an array
                                    if (!isset($field_options['items']) || !is_array($field_options['items'])) {
                                        $field_options['items'] = array(
                                            array(
                                                'label' => 'Option 1',
                                                'value' => 'option_1',
                                                'variable' => ''
                                            )
                                        );
                                        error_log('Created default options for checkbox field');
                                    }

                                    if (isset($field_options['items']) && is_array($field_options['items'])) : ?>
                                        <?php foreach ($field_options['items'] as $item) : ?>
                                            <?php
                                            // Ensure we have valid values
                                            $item_label = isset($item['label']) ? $item['label'] : 'Option';
                                            $item_value = isset($item['value']) ? $item['value'] : $item_label;
                                            $item_variable = isset($item['variable']) ? $item['variable'] : '';

                                            // Debug output for each option
                                            error_log("Checkbox option: label=$item_label, value=$item_value, variable=$item_variable");

                                            // Check for translations
                                            $item_key = 'field_' . (isset($field['id']) ? $field['id'] : '0') . '_option_' . sanitize_key($item_label);
                                            if (!empty($translations[$item_key])) {
                                                $item_label = $translations[$item_key];
                                            }

                                            // If the value is a variable placeholder, store it in a data attribute
                                            $data_variable = '';
                                            if (!empty($item_variable)) {
                                                $data_variable = ' data-variable="' . esc_attr($item_variable) . '"';
                                            }
                                            ?>
                                            <div class="pfb-checkbox-item">
                                                <input type="checkbox" id="<?php echo esc_attr($field_name . '_' . sanitize_key($item_label)); ?>" name="<?php echo esc_attr($field_name); ?>" value="<?php echo esc_attr($item_value); ?>" class="pfb-checkbox-input"<?php echo $data_variable; ?>>
                                                <label for="<?php echo esc_attr($field_name . '_' . sanitize_key($item_label)); ?>" class="pfb-checkbox-label"><?php echo esc_html($item_label); ?></label>
                                            </div>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <div class="pfb-checkbox-item">
                                            <p><?php _e('No options available', 'price-form-builder'); ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <?php break;

                            case 'subtotal':
                                // Debug output
                                error_log('Field options for subtotal: ' . print_r($field_options, true));
                                ?>
                                <div class="pfb-field-subtotal" data-field-name="<?php echo esc_attr($field_name); ?>">
                                    <div class="pfb-subtotal-header">
                                        <div class="pfb-subtotal-label"><?php echo esc_html($field_label); ?></div>
                                    </div>
                                    <div class="pfb-subtotal-message">
                                        <?php esc_html_e('For Price Calculation, Please fill form above and click Calculate button', 'price-form-builder'); ?>
                                    </div>

                                    <?php
                                    // Ensure field_options is an array
                                    if (!is_array($field_options)) {
                                        error_log('Field options is not an array, trying to unserialize: ' . print_r($field_options, true));

                                        // Try to unserialize
                                        $field_options = maybe_unserialize($field_options);

                                        // If still not an array, create default options
                                        if (!is_array($field_options)) {
                                            $field_options = array(
                                                'lines' => array(
                                                    array(
                                                        'label' => 'Line 1',
                                                        'formula' => ''
                                                    )
                                                ),
                                                'empty_value' => '---'
                                            );
                                            error_log('Created default field options for subtotal field');
                                        } else {
                                            error_log('Successfully unserialized field options');
                                        }
                                    }

                                    // Check if this is a multi-line subtotal field
                                    $has_lines = isset($field_options['lines']) && is_array($field_options['lines']) && !empty($field_options['lines']);
                                    $empty_value = isset($field_options['empty_value']) ? $field_options['empty_value'] : '---';

                                    error_log('Subtotal has lines: ' . ($has_lines ? 'yes' : 'no'));
                                    if ($has_lines) {
                                        error_log('Subtotal lines: ' . print_r($field_options['lines'], true));
                                    }

                                    // Check if we're in RTL mode
                                    $is_rtl = is_rtl();

                                    if ($has_lines) : ?>
                                        <div class="pfb-subtotal-lines" style="display: none;">
                                            <?php foreach ($field_options['lines'] as $line) :
                                                $line_label = isset($line['label']) ? $line['label'] : '';
                                            ?>
                                                <?php if (is_rtl()) : ?>
                                                    <div class="pfb-subtotal-line is-rtl" data-formula="<?php echo esc_attr(isset($line['formula']) ? $line['formula'] : ''); ?>">
                                                        <div class="pfb-subtotal-line-value" dir="ltr" style="font-weight: 700; text-align: left;"><?php echo esc_html(isset($currency['symbol']) ? $currency['symbol'] : '$') . '0.00'; ?></div>
                                                        <div class="pfb-subtotal-line-label" style="text-align: right;"><?php echo esc_html($line_label); ?></div>
                                                    </div>
                                                <?php else : ?>
                                                    <div class="pfb-subtotal-line is-ltr" data-formula="<?php echo esc_attr(isset($line['formula']) ? $line['formula'] : ''); ?>">
                                                        <div class="pfb-subtotal-line-label"><?php echo esc_html($line_label); ?></div>
                                                        <div class="pfb-subtotal-line-value" dir="ltr" style="font-weight: 700;"><?php echo esc_html(isset($currency['symbol']) ? $currency['symbol'] : '$') . '0.00'; ?></div>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </div>
                                        <?php if (is_rtl()) : ?>
                                            <div class="pfb-subtotal-total is-rtl" style="display: none;">
                                                <div class="pfb-subtotal-value" dir="ltr" style="font-weight: 700; text-align: left;"><?php echo esc_html(isset($currency['symbol']) ? $currency['symbol'] : '$') . '0.00'; ?></div>
                                                <div class="pfb-subtotal-total-label" style="text-align: right;"><?php esc_html_e('Subtotal', 'price-form-builder'); ?></div>
                                            </div>
                                        <?php else : ?>
                                            <div class="pfb-subtotal-total is-ltr" style="display: none;">
                                                <div class="pfb-subtotal-total-label"><?php esc_html_e('Subtotal', 'price-form-builder'); ?></div>
                                                <div class="pfb-subtotal-value" dir="ltr" style="font-weight: 700;"><?php echo esc_html(isset($currency['symbol']) ? $currency['symbol'] : '$') . '0.00'; ?></div>
                                            </div>
                                        <?php endif; ?>
                                    <?php else : ?>
                                        <div class="pfb-subtotal-value" dir="ltr" style="display: none; font-weight: 700;"><?php echo esc_html(isset($currency['symbol']) ? $currency['symbol'] : '$') . '0.00'; ?></div>
                                    <?php endif; ?>

                                    <input type="hidden" class="pfb-subtotal-empty-value" value="<?php echo esc_attr($empty_value); ?>">
                                </div>
                                <?php break;

                            case 'total': ?>
                                <div class="pfb-field-total<?php echo is_rtl() ? ' is-rtl' : ' is-ltr'; ?>">
                                    <div class="pfb-total-label"><?php echo esc_html($field_label); ?></div>
                                    <div class="pfb-total-value" dir="ltr"><?php echo esc_html(isset($currency['symbol']) ? $currency['symbol'] : '$') . '0.00'; ?></div>
                                </div>
                                <?php break;

                        endswitch; ?>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </form>
    </div>

    <div class="pfb-form-footer">
        <div class="pfb-currency-selector">
            <span class="pfb-currency-label"><?php _e('Currency:', 'price-form-builder'); ?></span>
            <select class="pfb-currency-select">
                <?php
                $currencies = PFB_Currency::get_all_currencies();

                if (!empty($currencies)) {
                    foreach ($currencies as $curr) {
                        $curr_code = isset($curr['code']) ? $curr['code'] : '';
                        $curr_name = isset($curr['name']) ? $curr['name'] : '';
                        $curr_symbol = isset($curr['symbol']) ? $curr['symbol'] : '';
                        $selected = (isset($currency['code']) && $curr_code === $currency['code']) ? 'selected' : '';
                        echo '<option value="' . esc_attr($curr_code) . '" ' . $selected . '>' . esc_html($curr_name . ' (' . $curr_symbol . ')') . '</option>';
                    }
                } else {
                    // Fallback if no currencies are defined
                    echo '<option value="USD" selected>US Dollar ($)</option>';
                }
                ?>
            </select>
        </div>

        <!-- Calculate Button -->
        <button type="button" class="pfb-btn pfb-btn-primary pfb-calculate-button">
            <?php _e('Calculate Price', 'price-form-builder'); ?>
        </button>
    </div>
</div>
