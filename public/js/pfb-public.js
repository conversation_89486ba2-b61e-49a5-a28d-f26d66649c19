/**
 * Public JavaScript for the Price Form Builder plugin.
 *
 * @since      1.0.0
 */
(function($) {
    'use strict';

    // Check if jQuery is available
    if (typeof $ === 'undefined') {
        console.error('Price Form Builder: jQuery is not loaded');
        return;
    }

    /**
     * Initialize the public functionality.
     */
    function init() {
        try {
            console.log('Initializing Price Form Builder public functionality');

            // Make sure hidden fields are properly hidden
            ensureHiddenFieldsAreHidden();

            // Initialize sliders first so they're ready for calculations
            initSliders();

            // Initialize the price calculator
            initPriceCalculator();

            // Initialize conditional logic if available
            if (typeof window.PFBConditionalLogic !== 'undefined' && typeof window.PFBConditionalLogic.init === 'function') {
                console.log('Initializing conditional logic from public.js');
                window.PFBConditionalLogic.init();
            } else {
                console.log('Conditional logic module not available');
            }

            // Initialize other components
            initCurrencySelector();
            initFormValidation();

            console.log('Price Form Builder initialization complete');
        } catch (e) {
            console.error('Price Form Builder initialization error:', e);
        }
    }

    /**
     * Initialize slider functionality.
     */
    function initSliders() {
        // Find all slider inputs
        $('.pfb-slider-input').each(function() {
            const $slider = $(this);
            const $valueDisplay = $slider.siblings('.pfb-slider-value-display').find('.pfb-slider-current-value');

            // Update the value display when the slider changes
            $slider.on('input', function() {
                const value = $slider.val();
                $valueDisplay.text(value);
            });

            // Set initial value
            $valueDisplay.text($slider.val());
        });
    }

    /**
     * Ensure that slider values are properly included in form data for calculations.
     * This function is called by calculatePrice().
     *
     * @param {Object} formData - The form data object to update
     * @param {jQuery} $form - The form jQuery object
     * @return {Object} - The updated form data object
     */
    function includeSliderValues(formData, $form) {
        $form.find('.pfb-slider-input').each(function() {
            const $slider = $(this);
            const name = $slider.attr('name');
            const value = $slider.val();

            if (name && name !== 'undefined' && name !== '') {
                // Make sure we're getting a numeric value
                let numericValue = parseFloat(value);
                if (!isNaN(numericValue)) {
                    formData[name] = numericValue;
                    console.log('Added slider value:', name, '=', numericValue);
                }
            }
        });

        return formData;
    }

    /**
     * Ensure hidden fields are properly hidden.
     */
    function ensureHiddenFieldsAreHidden() {
        // Find all hidden fields and make sure they're properly hidden
        $('.pfb-field-hidden').each(function() {
            // Hide the field container
            $(this).hide();

            // Make sure any inputs inside are not required
            $(this).find('input, select, textarea').prop('required', false);

            // Add debug log
            console.log('Hidden field properly hidden:', $(this).find('input').attr('name'));
        });
    }

    /**
     * Initialize price calculator functionality.
     */
    function initPriceCalculator() {
        const $forms = $('.pfb-form');
        const $calculateButtons = $('.pfb-calculate-button');

        if (!$forms.length) {
            return;
        }

        console.log('Initializing price calculator with ' + $forms.length + ' forms and ' + $calculateButtons.length + ' calculate buttons');

        // Initialize total field with placeholder
        $forms.each(function() {
            const $form = $(this);
            const $totalField = $form.find('.pfb-field-total');

            if ($totalField.length) {
                const $totalValue = $totalField.find('.pfb-total-value');
                // Get the currency symbol but don't overwrite it yet
                $totalValue.text('---');
            }
        });

        // Remove any existing click handlers to prevent duplicates
        $calculateButtons.off('click');

        // Calculate price only when the calculate button is clicked
        $calculateButtons.on('click', function(e) {
            e.preventDefault();
            console.log('Calculate button clicked');

            const $formContainer = $(this).closest('.pfb-form-container');
            if ($formContainer.length) {
                const $form = $formContainer.find('.pfb-form');
                if ($form.length) {
                    console.log('Found form, calculating price');
                    calculatePrice($form);
                } else {
                    console.error('Form not found in form container');
                }
            } else {
                console.error('Form container not found');
            }
        });

        console.log('Price calculator initialized');
    }

    /**
     * Calculate price for a form.
     */
    function calculatePrice($form) {
        console.log('Calculate price function called');

        const formId = $form.data('form-id');
        console.log('Form ID:', formId);

        const $totalField = $form.find('.pfb-field-total');
        console.log('Total field found:', $totalField.length > 0);

        // Log all subtotal fields
        const $subtotalFields = $form.find('.pfb-field-subtotal');
        console.log('Subtotal fields found:', $subtotalFields.length);
        $subtotalFields.each(function(index) {
            const $field = $(this);
            const fieldName = $field.data('field-name');
            console.log(`Subtotal field ${index}: ${fieldName}`);

            // Check if this is a multi-line subtotal
            const $lines = $field.find('.pfb-subtotal-lines');
            if ($lines.length) {
                console.log(`Subtotal field ${fieldName} has lines container`);

                // Log each line
                const $lineElements = $lines.find('.pfb-subtotal-line');
                console.log(`Subtotal field ${fieldName} has ${$lineElements.length} lines`);

                $lineElements.each(function(lineIndex) {
                    const $line = $(this);
                    const formula = $line.data('formula');
                    const label = $line.find('.pfb-subtotal-line-label').text();
                    console.log(`Line ${lineIndex}: label=${label}, formula=${formula}`);
                });
            }
        });

        // If there's no form ID or no total field and no subtotal fields, we can't calculate
        if (!formId || (!$totalField.length && $subtotalFields.length === 0)) {
            console.error('Missing form ID or no calculation fields (total or subtotal)');
            return;
        }

        // Validate required fields first
        let isValid = true;

        // Clear previous errors
        $form.find('.pfb-field-error').remove();
        $form.find('.error').removeClass('error');

        // Validate required fields
        $form.find('[required]').each(function() {
            const $input = $(this);
            let hasValue = true;

            if ($input.is(':checkbox') || $input.is(':radio')) {
                const name = $input.attr('name');
                hasValue = $form.find('[name="' + name + '"]:checked').length > 0;
            } else {
                hasValue = $input.val().trim() !== '';
            }

            if (!hasValue) {
                isValid = false;
                $input.addClass('error');

                const $field = $input.closest('.pfb-field');
                $field.append('<div class="pfb-field-error">This field is required.</div>');
            }
        });

        // If validation fails, scroll to the first error and stop
        if (!isValid) {
            const $firstError = $form.find('.error').first();
            if ($firstError.length) {
                $('html, body').animate({
                    scrollTop: $firstError.offset().top - 100
                }, 500);
            }
            return;
        }

        // Get form data
        const formData = {};

        // First, get all hidden variable fields
        // Get all variables from the hidden variables container
        $form.find('.pfb-hidden-variables input[type="hidden"]').each(function() {
            const $input = $(this);
            const name = $input.attr('name');
            const value = $input.val();
            const variable = $input.data('variable');

            if (name && name !== 'undefined' && name !== '') {
                // Make sure we're getting a numeric value if possible
                let numericValue = value;
                if (!isNaN(parseFloat(value))) {
                    numericValue = parseFloat(value);
                }

                formData[name] = numericValue;
                console.log('Added variable from hidden variables:', name, '=', numericValue);
            }
        });

        // Now get all hidden fields from the form
        $form.find('.pfb-field-hidden input[type="hidden"], .pfb-hidden-input').each(function() {
            const $input = $(this);
            const name = $input.attr('name');
            const value = $input.val();
            const variable = $input.data('variable');

            if (name && name !== 'undefined' && name !== '') {
                // Make sure we're getting a numeric value if possible
                let numericValue = value;
                if (!isNaN(parseFloat(value))) {
                    numericValue = parseFloat(value);
                }

                formData[name] = numericValue;
                console.log('Added hidden field:', name, '=', numericValue, variable ? '(variable: ' + variable + ')' : '');

                // If this is a variable field, also add it directly as a variable
                if (variable && variable !== '') {
                    const varName = 'pfb_var_' + variable;
                    if (!formData[varName]) {
                        formData[varName] = numericValue;
                        console.log('Added variable directly:', varName, '=', numericValue);
                    }
                }
            }
        });

        // Then get all visible form fields
        $form.find('input:not([type="hidden"]):not(.pfb-slider-input), select').each(function() {
            const $input = $(this);
            const name = $input.attr('name');

            if (!name || name === 'undefined' || name === '') {
                return;
            }

            // Handle different input types
            if ($input.is(':checkbox')) {
                if ($input.is(':checked')) {
                    if (formData[name]) {
                        if (!Array.isArray(formData[name])) {
                            formData[name] = [formData[name]];
                        }
                        formData[name].push($input.val());
                    } else {
                        formData[name] = [$input.val()];
                    }
                }
            } else if ($input.is(':radio')) {
                if ($input.is(':checked')) {
                    formData[name] = $input.val();
                }
            } else {
                formData[name] = $input.val();
            }
        });

        // We don't need to call includeSliderValues separately
        // Just collect slider values directly
        $form.find('.pfb-slider-input').each(function() {
            const $slider = $(this);
            const name = $slider.attr('name');
            const value = $slider.val();

            if (name && name !== 'undefined' && name !== '') {
                // Make sure we're getting a numeric value
                let numericValue = parseFloat(value);
                if (!isNaN(numericValue)) {
                    formData[name] = numericValue;
                    console.log('Added slider value:', name, '=', numericValue);
                }
            }
        });

        // Get currency - make sure we're getting the currency from the correct form
        const $currencySelect = $form.closest('.pfb-form-container').find('.pfb-currency-select');
        const currency = $currencySelect.length ? $currencySelect.val() : '';

        // Debug output of all form data
        console.log('Form data for calculation:', JSON.parse(JSON.stringify(formData)));

        try {
            // Calculate price via AJAX
            $.ajax({
                url: pfb_data.ajax_url,
                type: 'POST',
                data: {
                    action: 'pfb_calculate_price',
                    nonce: pfb_data.nonce,
                    form_id: formId,
                    form_data: formData,
                    currency: currency,
                    debug: true
                },
                beforeSend: function() {
                    // Add loading state to total field if it exists
                    if ($totalField.length) {
                        $totalField.find('.pfb-total-value').addClass('loading');
                    }

                    // Add loading state to subtotal fields if they exist
                    $subtotalFields.each(function() {
                        $(this).find('.pfb-subtotal-value').addClass('loading');
                    });

                    // Add loading state to the calculate button
                    $form.closest('.pfb-form-container').find('.pfb-calculate-button').addClass('loading').prop('disabled', true);
                },
                success: function(response) {
                    console.log('Price calculation response:', response);

                    if (response.success) {
                        // Update total field
                        const $totalValue = $totalField.find('.pfb-total-value');

                        // Make sure the value has dir="ltr" attribute
                        if (!$totalValue.attr('dir')) {
                            $totalValue.attr('dir', 'ltr');
                        }

                        $totalValue.text(response.data.formatted_price);

                        // Update subtotal fields if they exist
                        if (response.data.subtotals) {
                            console.log('Subtotals data:', response.data.subtotals);

                            for (const fieldName in response.data.subtotals) {
                                console.log('Processing subtotal field:', fieldName);

                                const $subtotalField = $form.find('.pfb-field-subtotal[data-field-name="' + fieldName + '"]');
                                console.log('Found subtotal field element:', $subtotalField.length > 0);

                                if ($subtotalField.length) {
                                    const subtotalData = response.data.subtotals[fieldName];
                                    console.log('Subtotal data for ' + fieldName + ':', subtotalData);

                                    const emptyValue = $subtotalField.find('.pfb-subtotal-empty-value').val() || '---';

                                    // Hide the message and show the subtotal content
                                    $subtotalField.find('.pfb-subtotal-message').hide();

                                    // Check if this is a multi-line subtotal
                                    if (typeof subtotalData === 'object' && subtotalData.lines) {
                                        console.log('Processing multi-line subtotal');

                                        // Update each line
                                        const $lines = $subtotalField.find('.pfb-subtotal-lines');
                                        console.log('Found lines container:', $lines.length > 0);

                                        if ($lines.length) {
                                            // Show the lines container
                                            $lines.show();

                                            // Clear existing lines
                                            $lines.empty();

                                            // Add each line
                                            subtotalData.lines.forEach(function(line, index) {
                                                console.log('Processing line ' + index + ':', line);

                                                // Check if we need RTL support
                                                const isRtl = $('html').attr('dir') === 'rtl' || $('body').hasClass('rtl') || $('.pfb-form-container').hasClass('is-rtl');
                                                const rtlClass = isRtl ? ' is-rtl' : ' is-ltr';

                                                const $line = $('<div class="pfb-subtotal-line' + rtlClass + '">');

                                                // Process the label to replace variables with their values
                                                let processedLabel = line.label;

                                                // Look for variable references in the label {variable_name}
                                                const variablePattern = /\{([^{}]+)\}/g;
                                                let match;

                                                while ((match = variablePattern.exec(processedLabel)) !== null) {
                                                    const variableName = match[1];
                                                    console.log('Found variable in label:', variableName);

                                                    // Check if this variable exists in formData
                                                    if (formData[variableName] !== undefined) {
                                                        // Replace the variable with its value
                                                        const value = formData[variableName];
                                                        console.log('Replacing variable in label:', variableName, 'with', value);
                                                        processedLabel = processedLabel.replace('{' + variableName + '}', value);
                                                    } else if (response.data.variables && response.data.variables[variableName] !== undefined) {
                                                        // Check if it's in the variables from the response
                                                        const value = response.data.variables[variableName];
                                                        console.log('Replacing variable from response in label:', variableName, 'with', value);
                                                        processedLabel = processedLabel.replace('{' + variableName + '}', value);
                                                    }
                                                }

                                                // Create line value element
                                                let $lineValue;
                                                if (line.has_required_fields) {
                                                    console.log('Line has required fields, showing empty value');
                                                    $lineValue = $('<div class="pfb-subtotal-line-value" dir="ltr" style="font-weight: 700;">' + emptyValue + '</div>');
                                                } else {
                                                    console.log('Line value:', line.value);
                                                    $lineValue = $('<div class="pfb-subtotal-line-value pfb-value-changed" dir="ltr" style="font-weight: 700;">' + formatPrice(line.value, response.data.currency) + '</div>');
                                                }

                                                // Create line label element
                                                const $lineLabel = $('<div class="pfb-subtotal-line-label">' + processedLabel + '</div>');

                                                // Add elements in the correct order based on RTL/LTR
                                                if (isRtl) {
                                                    // For RTL: value first (on left), then label (on right)
                                                    $lineValue.css('text-align', 'left');
                                                    $lineLabel.css('text-align', 'right');
                                                    $line.append($lineValue).append($lineLabel);
                                                } else {
                                                    // For LTR: label first (on left), then value (on right)
                                                    $line.append($lineLabel).append($lineValue);
                                                }

                                                // Remove the animation class after animation completes
                                                if (!line.has_required_fields) {
                                                    setTimeout(function() {
                                                        $lineValue.removeClass('pfb-value-changed');
                                                    }, 500);
                                                }

                                                $lines.append($line);
                                            });

                                            // Update the total
                                            console.log('Updating subtotal total:', subtotalData.total);

                                            // Check if we need RTL support for the total
                                            const isRtlTotal = $('html').attr('dir') === 'rtl' || $('body').hasClass('rtl') || $('.pfb-form-container').hasClass('is-rtl');

                                            // Get the subtotal total container
                                            const $subtotalTotal = $subtotalField.find('.pfb-subtotal-total');

                                            // Show the subtotal total
                                            $subtotalTotal.show();

                                            // Get the subtotal value element
                                            const $subtotalValue = $subtotalField.find('.pfb-subtotal-value');

                                            // Make sure the value has dir="ltr" attribute
                                            if (!$subtotalValue.attr('dir')) {
                                                $subtotalValue.attr('dir', 'ltr');
                                            }

                                            // Add animation class when updating the value
                                            $subtotalValue.addClass('pfb-value-changed');
                                            $subtotalValue.text(formatPrice(subtotalData.total, response.data.currency));

                                            // Ensure proper RTL styling
                                            if (isRtlTotal) {
                                                $subtotalValue.css('text-align', 'left');
                                                $subtotalField.find('.pfb-subtotal-total-label').css('text-align', 'right');
                                            }

                                            // Remove the animation class after animation completes
                                            setTimeout(function() {
                                                $subtotalValue.removeClass('pfb-value-changed');
                                            }, 500);
                                        }
                                    } else {
                                        // Legacy single-value subtotal
                                        console.log('Processing legacy single-value subtotal:', subtotalData);

                                        // Make sure the value has dir="ltr" attribute
                                        const $subtotalValue = $subtotalField.find('.pfb-subtotal-value');
                                        if (!$subtotalValue.attr('dir')) {
                                            $subtotalValue.attr('dir', 'ltr');
                                        }

                                        // Show the subtotal value
                                        $subtotalValue.show();

                                        $subtotalValue.text(formatPrice(subtotalData, response.data.currency));
                                    }
                                }
                            }
                        } else {
                            console.log('No subtotals data in response');
                        }
                    } else {
                        console.error('Price calculation error:', response);

                        // Show error in total field if it exists
                        if ($totalField.length) {
                            $totalField.find('.pfb-total-value').text('Error');
                        }

                        // Show error in subtotal fields if they exist
                        $subtotalFields.each(function() {
                            $(this).find('.pfb-subtotal-value').text('Error');
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', status, error);
                    console.error('XHR details:', xhr.status, xhr.responseText);

                    // Show error in total field if it exists
                    if ($totalField.length) {
                        $totalField.find('.pfb-total-value').text('Error');
                    }

                    // Show error in subtotal fields if they exist
                    $subtotalFields.each(function() {
                        $(this).find('.pfb-subtotal-value').text('Error');
                    });
                },
                complete: function() {
                    // Remove loading state from total field if it exists
                    if ($totalField.length) {
                        $totalField.find('.pfb-total-value').removeClass('loading');
                    }

                    // Remove loading state from subtotal fields if they exist
                    $subtotalFields.each(function() {
                        $(this).find('.pfb-subtotal-value').removeClass('loading');
                    });

                    // Remove loading state from the calculate button
                    $form.closest('.pfb-form-container').find('.pfb-calculate-button').removeClass('loading').prop('disabled', false);
                }
            });
        } catch (e) {
            console.error('Exception in price calculation:', e);

            // Show error in total field if it exists
            if ($totalField.length) {
                $totalField.find('.pfb-total-value').text('Error');
                $totalField.find('.pfb-total-value').removeClass('loading');
            }

            // Show error in subtotal fields if they exist
            $subtotalFields.each(function() {
                $(this).find('.pfb-subtotal-value').text('Error');
                $(this).find('.pfb-subtotal-value').removeClass('loading');
            });

            // Remove loading state from the calculate button
            $form.closest('.pfb-form-container').find('.pfb-calculate-button').removeClass('loading').prop('disabled', false);
        }
    }

    /**
     * Initialize currency selector functionality.
     *
     * Note: Currency change no longer triggers automatic calculation.
     * User must click the calculate button to see the updated price.
     */
    function initCurrencySelector() {
        // No automatic calculation on currency change
        // The user will need to click the calculate button
    }

    /**
     * Format a price value with the appropriate currency symbol and formatting.
     *
     * @param {number} price - The price value to format
     * @param {object} currencyData - The currency data object
     * @return {string} - The formatted price string
     */
    function formatPrice(price, currencyData) {
        if (typeof price !== 'number') {
            price = parseFloat(price) || 0;
        }

        // Log currency data for debugging
        console.log('Currency data:', currencyData);

        // Default values if currency data is not provided
        const symbol = currencyData && currencyData.symbol ? currencyData.symbol : '$';
        const symbolPosition = currencyData && currencyData.symbol_position ? currencyData.symbol_position : 'before';
        const rtlSymbolPosition = currencyData && currencyData.rtl_symbol_position ? currencyData.rtl_symbol_position : symbolPosition;
        const decimalPlaces = currencyData && typeof currencyData.decimal_places !== 'undefined' ? parseInt(currencyData.decimal_places) : 2;
        const thousandSeparator = currencyData && currencyData.thousand_separator ? currencyData.thousand_separator : ',';
        const decimalSeparator = currencyData && currencyData.decimal_separator ? currencyData.decimal_separator : '.';
        const exchangeRate = currencyData && typeof currencyData.exchange_rate !== 'undefined' ? parseFloat(currencyData.exchange_rate) : 1;

        console.log('Symbol position:', symbolPosition);
        console.log('RTL symbol position:', rtlSymbolPosition);

        // Apply exchange rate to the price
        price = price * exchangeRate;

        console.log('Formatting price:', {
            originalPrice: price / exchangeRate,
            exchangeRate: exchangeRate,
            convertedPrice: price
        });

        // Format the price with the specified decimal places
        let formattedPrice = price.toFixed(decimalPlaces);

        // Split into whole and decimal parts
        let parts = formattedPrice.split('.');
        let wholePart = parts[0];
        let decimalPart = parts.length > 1 ? parts[1] : '';

        // Add thousand separators to the whole part
        if (thousandSeparator) {
            wholePart = wholePart.replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator);
        }

        // Combine whole and decimal parts with the decimal separator
        formattedPrice = decimalPart ? wholePart + decimalSeparator + decimalPart : wholePart;

        // Check if we're in RTL mode
        const isRtl = $('html').attr('dir') === 'rtl' || $('body').hasClass('rtl') || $('.pfb-form-container').hasClass('is-rtl');

        // Add the currency symbol in the correct position based on settings and RTL mode
        let finalSymbolPosition = symbolPosition;

        // If in RTL mode and RTL symbol position is set, use that instead
        if (isRtl && currencyData && currencyData.rtl_symbol_position) {
            finalSymbolPosition = currencyData.rtl_symbol_position;
            console.log('Using RTL symbol position:', finalSymbolPosition);
        }

        if (finalSymbolPosition === 'before') {
            return symbol + formattedPrice;
        } else {
            return formattedPrice + symbol;
        }
    }

    /**
     * Initialize form validation functionality.
     */
    function initFormValidation() {
        $('.pfb-form').on('submit', function(e) {
            const $form = $(this);
            let isValid = true;

            // Clear previous errors
            $form.find('.pfb-field-error').remove();
            $form.find('.error').removeClass('error');

            // Validate required fields
            $form.find('[required]').each(function() {
                const $input = $(this);
                let hasValue = true;

                if ($input.is(':checkbox') || $input.is(':radio')) {
                    const name = $input.attr('name');
                    hasValue = $form.find('[name="' + name + '"]:checked').length > 0;
                } else {
                    hasValue = $input.val().trim() !== '';
                }

                if (!hasValue) {
                    isValid = false;
                    $input.addClass('error');

                    const $field = $input.closest('.pfb-field');
                    $field.append('<div class="pfb-field-error">This field is required.</div>');
                }
            });

            // Validate number fields
            $form.find('input[type="number"]').each(function() {
                const $input = $(this);
                const value = $input.val().trim();

                if (value !== '' && isNaN(value)) {
                    isValid = false;
                    $input.addClass('error');

                    const $field = $input.closest('.pfb-field');
                    $field.append('<div class="pfb-field-error">Please enter a valid number.</div>');
                }
            });

            if (!isValid) {
                e.preventDefault();

                // Scroll to first error
                const $firstError = $form.find('.error').first();
                if ($firstError.length) {
                    $('html, body').animate({
                        scrollTop: $firstError.offset().top - 100
                    }, 500);
                }
            }
        });
    }

    // Initialize when document is ready
    $(document).ready(init);

})(jQuery);
