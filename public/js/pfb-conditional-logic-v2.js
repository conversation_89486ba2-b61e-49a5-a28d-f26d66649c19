/**
 * Conditional Logic Handler for Price Form Builder
 * 
 * Handles conditional logic for form fields on the front-end.
 * 
 * This is a complete rewrite of the conditional logic functionality.
 *
 * @since      1.0.0
 */
(function($) {
    'use strict';
    
    /**
     * Conditional Logic Handler
     */
    var PFB_ConditionalLogic = {
        
        /**
         * Initialize conditional logic
         */
        init: function(formId) {
            console.log('Initializing conditional logic for form ' + formId);
            
            // Get form element
            var $form = $('#pfb-form-' + formId);
            
            if ($form.length === 0) {
                console.error('Form not found: #pfb-form-' + formId);
                return;
            }
            
            // Get all fields with conditional logic
            var $conditionalFields = $form.find('[data-conditional-logic]');
            
            if ($conditionalFields.length === 0) {
                console.log('No conditional fields found in form ' + formId);
                return;
            }
            
            console.log('Found ' + $conditionalFields.length + ' conditional fields');
            
            // Process each field
            $conditionalFields.each(function() {
                var $field = $(this);
                var fieldId = $field.attr('id');
                
                // Parse conditional logic
                var conditionalLogic = PFB_ConditionalLogic.parseConditionalLogic($field);
                
                if (!conditionalLogic || !conditionalLogic.enabled) {
                    console.log('Conditional logic not enabled for field ' + fieldId);
                    return;
                }
                
                console.log('Processing conditional logic for field ' + fieldId, conditionalLogic);
                
                // Store conditional logic on field
                $field.data('conditionalLogic', conditionalLogic);
                
                // Find all fields that this field depends on
                var dependencyFields = PFB_ConditionalLogic.getDependencyFields(conditionalLogic, $form);
                
                // Add change event to dependency fields
                $.each(dependencyFields, function(i, dependencyField) {
                    $(dependencyField).on('change', function() {
                        PFB_ConditionalLogic.evaluateField($field, $form);
                    });
                });
                
                // Initial evaluation
                PFB_ConditionalLogic.evaluateField($field, $form);
            });
            
            // Add form change event to re-evaluate all conditional fields
            $form.on('change', 'input, select, textarea', function() {
                PFB_ConditionalLogic.evaluateAllFields($form);
            });
        },
        
        /**
         * Parse conditional logic from data attribute
         */
        parseConditionalLogic: function($field) {
            var conditionalLogicStr = $field.attr('data-conditional-logic');
            
            if (!conditionalLogicStr) {
                return null;
            }
            
            try {
                return JSON.parse(conditionalLogicStr);
            } catch (e) {
                console.error('Error parsing conditional logic:', e);
                console.error('Conditional logic string:', conditionalLogicStr);
                return null;
            }
        },
        
        /**
         * Get all fields that a conditional field depends on
         */
        getDependencyFields: function(conditionalLogic, $form) {
            var dependencyFields = [];
            
            if (!conditionalLogic || !conditionalLogic.rules || !conditionalLogic.rules.length) {
                return dependencyFields;
            }
            
            $.each(conditionalLogic.rules, function(i, rule) {
                var fieldId = rule.field;
                var $dependencyField = $form.find('#' + fieldId);
                
                if ($dependencyField.length > 0) {
                    dependencyFields.push($dependencyField[0]);
                } else {
                    console.warn('Dependency field not found:', fieldId);
                }
            });
            
            return dependencyFields;
        },
        
        /**
         * Evaluate a conditional field
         */
        evaluateField: function($field, $form) {
            var conditionalLogic = $field.data('conditionalLogic');
            
            if (!conditionalLogic || !conditionalLogic.enabled) {
                return;
            }
            
            var fieldValues = PFB_ConditionalLogic.getFieldValues($form);
            var shouldShow = PFB_ConditionalLogic.evaluateConditionalLogic(conditionalLogic, fieldValues);
            
            // Get the field container
            var $fieldContainer = $field.closest('.pfb-field-container');
            
            if (shouldShow) {
                $fieldContainer.show();
            } else {
                $fieldContainer.hide();
            }
        },
        
        /**
         * Evaluate all conditional fields in a form
         */
        evaluateAllFields: function($form) {
            var $conditionalFields = $form.find('[data-conditional-logic]');
            
            $conditionalFields.each(function() {
                PFB_ConditionalLogic.evaluateField($(this), $form);
            });
        },
        
        /**
         * Get all field values in a form
         */
        getFieldValues: function($form) {
            var fieldValues = {};
            
            $form.find('input, select, textarea').each(function() {
                var $input = $(this);
                var fieldId = $input.attr('id');
                
                if (!fieldId) {
                    return;
                }
                
                // Handle different input types
                if ($input.is(':checkbox')) {
                    fieldValues[fieldId] = $input.is(':checked') ? $input.val() : '';
                } else if ($input.is(':radio')) {
                    if ($input.is(':checked')) {
                        fieldValues[fieldId] = $input.val();
                    }
                } else {
                    fieldValues[fieldId] = $input.val();
                }
            });
            
            return fieldValues;
        },
        
        /**
         * Evaluate conditional logic
         */
        evaluateConditionalLogic: function(conditionalLogic, fieldValues) {
            if (!conditionalLogic.enabled) {
                return true;
            }
            
            if (!conditionalLogic.rules || conditionalLogic.rules.length === 0) {
                return true;
            }
            
            var results = [];
            
            $.each(conditionalLogic.rules, function(i, rule) {
                results.push(PFB_ConditionalLogic.evaluateRule(rule, fieldValues));
            });
            
            if (conditionalLogic.logic_type === 'all') {
                // All rules must be true
                return !results.includes(false);
            } else {
                // Any rule can be true
                return results.includes(true);
            }
        },
        
        /**
         * Evaluate a single rule
         */
        evaluateRule: function(rule, fieldValues) {
            var fieldId = rule.field;
            var operator = rule.operator;
            var value = rule.value;
            var fieldValue = fieldValues[fieldId] || '';
            
            switch (operator) {
                case 'is':
                    return fieldValue == value;
                    
                case 'is_not':
                    return fieldValue != value;
                    
                case 'greater_than':
                    return parseFloat(fieldValue) > parseFloat(value);
                    
                case 'less_than':
                    return parseFloat(fieldValue) < parseFloat(value);
                    
                case 'contains':
                    return fieldValue.indexOf(value) !== -1;
                    
                case 'starts_with':
                    return fieldValue.indexOf(value) === 0;
                    
                case 'ends_with':
                    return fieldValue.endsWith(value);
                    
                case 'is_empty':
                    return fieldValue === '';
                    
                case 'is_not_empty':
                    return fieldValue !== '';
                    
                default:
                    return false;
            }
        }
    };
    
    // Initialize on document ready
    $(document).ready(function() {
        // Find all forms
        $('.pfb-form').each(function() {
            var formId = $(this).data('form-id');
            if (formId) {
                PFB_ConditionalLogic.init(formId);
            }
        });
    });
    
})(jQuery);
