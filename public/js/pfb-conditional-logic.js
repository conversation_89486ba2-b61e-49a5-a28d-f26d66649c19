/**
 * JavaScript for handling conditional logic in the frontend form.
 *
 * @since      1.0.2
 */

(function($) {
    'use strict';

    // Store field dependencies for optimization
    const fieldDependencies = {};

    // Initialize conditional logic
    function initConditionalLogic() {
        console.log('PFB Conditional Logic: Initializing conditional logic on frontend');

        // Get all form fields with conditional logic
        $('.pfb-form-container').each(function() {
            const formContainer = $(this);
            const formId = formContainer.attr('id');
            console.log('PFB Conditional Logic: Processing form container:', formId);

            const formFields = formContainer.find('.pfb-field');
            console.log('PFB Conditional Logic: Found', formFields.length, 'fields in form');

            // Create a map of field IDs to field names for easier lookup
            const fieldMap = {};
            formFields.each(function() {
                const field = $(this);
                const fieldId = field.data('field-id');
                const fieldName = field.data('field-name');
                const fieldType = field.data('type');

                if (fieldId && fieldName) {
                    // Map by ID
                    fieldMap[fieldId] = {
                        name: fieldName,
                        type: fieldType
                    };

                    // Also map by name for redundancy
                    fieldMap[fieldName] = {
                        id: fieldId,
                        type: fieldType
                    };

                    console.log('PFB Conditional Logic: Mapped field ID', fieldId, 'to name', fieldName);
                }
            });

            // Process each field
            formFields.each(function() {
                const field = $(this);
                const fieldId = field.data('field-id');
                const fieldType = field.data('type');

                // Get conditional logic from data attribute
                let conditionalLogic = field.data('conditional-logic');

                // If the data attribute is a string (JSON), parse it
                if (typeof conditionalLogic === 'string' && conditionalLogic.trim() !== '') {
                    try {
                        conditionalLogic = JSON.parse(conditionalLogic);
                        console.log('Parsed conditional logic from string for field', fieldId, ':', conditionalLogic);

                        // Ensure the conditionalLogic has the enabled property
                        if (conditionalLogic && typeof conditionalLogic.enabled === 'undefined') {
                            conditionalLogic.enabled = true;
                        }
                    } catch (e) {
                        console.error('Error parsing conditional logic for field', fieldId, ':', e);
                    }
                }

                console.log('PFB Conditional Logic: Field', fieldId, 'type:', fieldType, 'has conditional logic:', conditionalLogic ? 'yes' : 'no');

                if (conditionalLogic) {
                    console.log('PFB Conditional Logic: Conditional logic for field', fieldId, ':', conditionalLogic);

                    // Store the conditional logic data on the field element
                    field.data('conditional-logic', conditionalLogic);

                    // Set initial visibility
                    evaluateConditionalLogic(field, conditionalLogic, formContainer);

                    // Add event listeners to fields that this field depends on
                    if (conditionalLogic.rules && conditionalLogic.rules.length) {
                        conditionalLogic.rules.forEach(function(rule) {
                            const dependentFieldId = rule.field;

                            // Try to find the field by ID first
                            let dependentField = formContainer.find('.pfb-field[data-field-id="' + dependentFieldId + '"]');

                            // If not found by ID, try to find by field name
                            if (!dependentField.length && rule.field_name) {
                                dependentField = formContainer.find('.pfb-field[data-field-name="' + rule.field_name + '"]');
                                console.log('PFB Conditional Logic: Found field by name:', rule.field_name);
                            }

                            // If still not found, try to use the field map by ID
                            if (!dependentField.length && fieldMap[dependentFieldId] && fieldMap[dependentFieldId].name) {
                                const fieldName = fieldMap[dependentFieldId].name;
                                dependentField = formContainer.find('.pfb-field[data-field-name="' + fieldName + '"]');
                                console.log('PFB Conditional Logic: Found field using map by ID:', fieldName);
                            }

                            // If still not found, try to use the field map by name
                            if (!dependentField.length && rule.field_name && fieldMap[rule.field_name] && fieldMap[rule.field_name].id) {
                                const fieldId = fieldMap[rule.field_name].id;
                                dependentField = formContainer.find('.pfb-field[data-field-id="' + fieldId + '"]');
                                console.log('PFB Conditional Logic: Found field using map by name:', rule.field_name);
                            }

                            if (!dependentField.length) {
                                console.error('PFB Conditional Logic: Could not find dependent field:', dependentFieldId, rule.field_name);
                                return;
                            }

                            const actualDependentFieldId = dependentField.data('field-id');

                            // Store dependency for optimization
                            if (!fieldDependencies[actualDependentFieldId]) {
                                fieldDependencies[actualDependentFieldId] = [];
                            }

                            if (!fieldDependencies[actualDependentFieldId].includes(fieldId)) {
                                fieldDependencies[actualDependentFieldId].push(fieldId);
                            }

                            // Add event listeners based on field type
                            const fieldType = dependentField.data('type');

                            if (fieldType === 'text' || fieldType === 'number') {
                                dependentField.find('input').on('input change', function() {
                                    evaluateConditionalLogic(field, conditionalLogic, formContainer);
                                });
                            } else if (fieldType === 'dropdown') {
                                dependentField.find('select').on('change', function() {
                                    evaluateConditionalLogic(field, conditionalLogic, formContainer);
                                });
                            } else if (fieldType === 'radio') {
                                dependentField.find('input[type="radio"]').on('change', function() {
                                    evaluateConditionalLogic(field, conditionalLogic, formContainer);
                                });
                            } else if (fieldType === 'checkbox') {
                                dependentField.find('input[type="checkbox"]').on('change', function() {
                                    evaluateConditionalLogic(field, conditionalLogic, formContainer);
                                });
                            } else if (fieldType === 'slider') {
                                dependentField.find('input[type="range"]').on('input change', function() {
                                    evaluateConditionalLogic(field, conditionalLogic, formContainer);
                                });
                            }
                        });
                    }
                }
            });
        });
    }

    // Evaluate conditional logic for a field
    function evaluateConditionalLogic(field, conditionalLogic, formContainer) {
        const fieldId = field.data('field-id');
        console.log('PFB Conditional Logic: Evaluating conditional logic for field:', fieldId);

        // If conditionalLogic is a string, try to parse it
        if (typeof conditionalLogic === 'string' && conditionalLogic.trim() !== '') {
            try {
                conditionalLogic = JSON.parse(conditionalLogic);
                console.log('PFB Conditional Logic: Parsed conditional logic from string for evaluation:', conditionalLogic);
            } catch (e) {
                console.error('PFB Conditional Logic: Error parsing conditional logic for evaluation:', e);
                return;
            }
        }

        // Check if conditional logic is valid and enabled
        if (!conditionalLogic) {
            console.log('PFB Conditional Logic: No conditional logic for field:', fieldId);
            return;
        }

        // If enabled flag is not set, check if there are rules
        if (typeof conditionalLogic.enabled === 'undefined') {
            conditionalLogic.enabled = !!(conditionalLogic.rules && conditionalLogic.rules.length);
            console.log('PFB Conditional Logic: Set enabled flag to:', conditionalLogic.enabled);
        }

        // If not enabled or no rules, return
        if (!conditionalLogic.enabled || !conditionalLogic.rules || !conditionalLogic.rules.length) {
            console.log('PFB Conditional Logic: Conditional logic disabled or no rules for field:', fieldId);
            return;
        }

        const logicType = conditionalLogic.logic_type || 'all';
        const rules = conditionalLogic.rules;
        console.log('PFB Conditional Logic: Logic type:', logicType, 'with', rules.length, 'rules');

        // Default to true for 'all', false for 'any'
        let shouldShow = logicType === 'all';

        // Evaluate each rule
        rules.forEach(function(rule, index) {
            console.log(`PFB Conditional Logic: Evaluating rule ${index + 1}:`, rule);

            const dependentFieldId = rule.field;
            const dependentFieldName = rule.field_name;
            const operator = rule.operator || 'is';
            const value = rule.value || '';

            // Try multiple strategies to find the dependent field
            let dependentField = null;

            // Strategy 1: Try to find by ID
            if (dependentFieldId) {
                dependentField = formContainer.find(`.pfb-field[data-field-id="${dependentFieldId}"]`);
                if (dependentField.length) {
                    console.log(`PFB Conditional Logic: Found field by ID: ${dependentFieldId}`);
                }
            }

            // Strategy 2: Try to find by field name
            if (!dependentField || !dependentField.length) {
                if (dependentFieldName) {
                    dependentField = formContainer.find(`.pfb-field[data-field-name="${dependentFieldName}"]`);
                    if (dependentField.length) {
                        console.log(`PFB Conditional Logic: Found field by name: ${dependentFieldName}`);
                    }
                }
            }

            // Strategy 3: Try to find by field name in any attribute
            if (!dependentField || !dependentField.length) {
                if (dependentFieldName) {
                    // Look for the field name in any attribute
                    formContainer.find('.pfb-field').each(function() {
                        const $field = $(this);
                        const fieldName = $field.data('field-name');
                        const fieldId = $field.data('field-id');

                        if (fieldName === dependentFieldName || fieldId === dependentFieldId) {
                            dependentField = $field;
                            console.log(`PFB Conditional Logic: Found field by searching all fields: ${fieldName}`);
                            return false; // Break the loop
                        }
                    });
                }
            }

            // If still not found, log error and skip this rule
            if (!dependentField || !dependentField.length) {
                console.log('PFB Conditional Logic: Dependent field not found:', dependentFieldId, dependentFieldName);
                return;
            }

            const fieldType = dependentField.data('type');

            // Get the field value based on field type
            let fieldValue = '';

            if (fieldType === 'text' || fieldType === 'number') {
                fieldValue = dependentField.find('input').val();
            } else if (fieldType === 'dropdown') {
                fieldValue = dependentField.find('select').val();
            } else if (fieldType === 'radio') {
                fieldValue = dependentField.find('input[type="radio"]:checked').val() || '';
            } else if (fieldType === 'checkbox') {
                // For checkboxes, we need to handle multiple values
                const checkedValues = [];
                dependentField.find('input[type="checkbox"]:checked').each(function() {
                    checkedValues.push($(this).val());
                });

                // Store as array for better comparison
                fieldValue = checkedValues;
            } else if (fieldType === 'slider') {
                fieldValue = dependentField.find('input[type="range"]').val();
            }

            console.log('PFB Conditional Logic: Evaluating rule - Field:', dependentFieldId, 'Type:', fieldType, 'Operator:', operator, 'Value:', value, 'Field Value:', fieldValue);

            // Evaluate the rule
            const ruleResult = evaluateRule(fieldValue, operator, value, fieldType);
            console.log('PFB Conditional Logic: Rule result:', ruleResult);

            // Update the result based on logic type
            if (logicType === 'all') {
                shouldShow = shouldShow && ruleResult;
            } else {
                shouldShow = shouldShow || ruleResult;
            }
        });

        console.log('PFB Conditional Logic: Field', fieldId, 'should be', shouldShow ? 'shown' : 'hidden');

        // Store the current required state before changing visibility
        const $inputs = field.find('input, select, textarea');
        const wasRequired = $inputs.filter('[required]').length > 0;
        field.data('was-required', wasRequired);

        // Show or hide the field based on the result
        if (shouldShow) {
            console.log('PFB Conditional Logic: Showing field', fieldId);
            field.slideDown(200);
            $inputs.prop('disabled', false);

            // Restore required state if it was required before
            if (wasRequired) {
                $inputs.prop('required', true);
            }

            // Trigger change event on the field's inputs to update dependent fields
            $inputs.trigger('change');
        } else {
            console.log('PFB Conditional Logic: Hiding field', fieldId);
            field.slideUp(200);
            $inputs.prop('disabled', true).prop('required', false);

            // Clear field values when hidden
            $inputs.each(function() {
                const $input = $(this);
                if ($input.is(':checkbox, :radio')) {
                    $input.prop('checked', false);
                } else {
                    $input.val('');
                }
            });

            // Trigger change event on the field's inputs to update dependent fields
            $inputs.trigger('change');
        }
    }

    // Evaluate a single rule
    function evaluateRule(fieldValue, operator, value, fieldType) {
        // Handle array values (checkboxes)
        if (Array.isArray(fieldValue)) {
            if (operator === 'is') {
                return fieldValue.includes(value);
            } else if (operator === 'is_not') {
                return !fieldValue.includes(value);
            } else if (operator === 'contains') {
                return fieldValue.some(val => val.toString().indexOf(value.toString()) !== -1);
            }
            // For other operators, convert to string for comparison
            fieldValue = fieldValue.join(',');
        }

        // Convert to appropriate types for comparison
        let numericFieldValue = parseFloat(fieldValue);
        let numericValue = parseFloat(value);

        // For number and slider fields, ensure we're using numeric comparison
        if (fieldType === 'number' || fieldType === 'slider') {
            if (!isNaN(numericFieldValue) && !isNaN(numericValue)) {
                switch (operator) {
                    case 'is':
                        return numericFieldValue === numericValue;
                    case 'is_not':
                        return numericFieldValue !== numericValue;
                    case 'greater_than':
                        return numericFieldValue > numericValue;
                    case 'less_than':
                        return numericFieldValue < numericValue;
                    case 'contains':
                        return fieldValue.toString().indexOf(value.toString()) !== -1;
                }
            }
        }

        // For other field types, use string comparison
        switch (operator) {
            case 'is':
                return fieldValue.toString() === value.toString();

            case 'is_not':
                return fieldValue.toString() !== value.toString();

            case 'greater_than':
                if (!isNaN(numericFieldValue) && !isNaN(numericValue)) {
                    return numericFieldValue > numericValue;
                }
                return false;

            case 'less_than':
                if (!isNaN(numericFieldValue) && !isNaN(numericValue)) {
                    return numericFieldValue < numericValue;
                }
                return false;

            case 'contains':
                return fieldValue.toString().indexOf(value.toString()) !== -1;

            default:
                return false;
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('PFB Conditional Logic: Initializing conditional logic on frontend');
        initConditionalLogic();
    });

    // Expose functions to global scope
    window.PFBConditionalLogic = {
        init: initConditionalLogic,
        evaluateConditionalLogic: evaluateConditionalLogic
    };

})(jQuery);
