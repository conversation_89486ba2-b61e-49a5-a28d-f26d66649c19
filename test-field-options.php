<?php
/**
 * Test Field Options
 * 
 * This file tests the field options functionality.
 */

// Include WordPress
require_once '../../../wp-config.php';

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied.');
}

echo "<h1>Field Options Test</h1>";

// Test 1: Create a form with dropdown field and options
echo "<h2>Test 1: Create Form with Dropdown Field</h2>";

try {
    global $wpdb;
    
    // Create form handler
    require_once 'includes/class-pfb-conditional-logic.php';
    require_once 'includes/class-pfb-form-field-handler.php';
    require_once 'includes/class-pfb-form-handler.php';
    
    $form_handler = new PFB_Form_Handler();
    
    // Create test form data
    $form_data = array(
        'title' => 'Test Form with Field Options',
        'description' => 'This is a test form to verify field options are working',
        'status' => 'draft',
        'settings' => array(
            'template' => 'default',
            'show_currency_selector' => '1',
            'submit_button_text' => 'Calculate Price'
        ),
        'fields' => array(
            // Dropdown field with options
            array(
                'type' => 'select',
                'label' => 'Choose Option',
                'name' => 'test_dropdown',
                'required' => true,
                'width' => '100',
                'options' => array(
                    'items' => array(
                        array(
                            'label' => 'Option A',
                            'value' => 'option_a',
                            'variable' => 'var_a'
                        ),
                        array(
                            'label' => 'Option B',
                            'value' => 'option_b',
                            'variable' => 'var_b'
                        ),
                        array(
                            'label' => 'Option C',
                            'value' => 'option_c',
                            'variable' => 'var_c'
                        )
                    )
                ),
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'rules' => array()
                )
            ),
            // Radio field with options
            array(
                'type' => 'radio',
                'label' => 'Select One',
                'name' => 'test_radio',
                'required' => false,
                'width' => '50',
                'options' => array(
                    'items' => array(
                        array(
                            'label' => 'Yes',
                            'value' => 'yes',
                            'variable' => ''
                        ),
                        array(
                            'label' => 'No',
                            'value' => 'no',
                            'variable' => ''
                        )
                    )
                ),
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'rules' => array()
                )
            ),
            // Text field
            array(
                'type' => 'text',
                'label' => 'Your Name',
                'name' => 'test_text',
                'required' => true,
                'width' => '50',
                'options' => array(
                    'placeholder' => 'Enter your name',
                    'default' => '',
                    'description' => 'Please enter your full name'
                ),
                'conditional_logic' => array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'rules' => array()
                )
            )
        )
    );
    
    // Save the form
    $result = $form_handler->save_form($form_data);
    
    if ($result['success']) {
        $form_id = $result['form_id'];
        echo "<p style='color: green;'>✓ Form created successfully with ID: {$form_id}</p>";
        
        // Test 2: Load the form back and verify options
        echo "<h2>Test 2: Load Form and Verify Options</h2>";
        
        $loaded_form = $form_handler->get_form($form_id);
        
        if ($loaded_form) {
            echo "<p style='color: green;'>✓ Form loaded successfully</p>";
            echo "<p><strong>Form Title:</strong> {$loaded_form['title']}</p>";
            echo "<p><strong>Number of Fields:</strong> " . count($loaded_form['fields']) . "</p>";
            
            // Check each field
            foreach ($loaded_form['fields'] as $index => $field) {
                echo "<h3>Field " . ($index + 1) . ": {$field['label']} ({$field['type']})</h3>";
                
                if (isset($field['options']) && is_array($field['options'])) {
                    echo "<p><strong>Field Options:</strong></p>";
                    echo "<pre>" . print_r($field['options'], true) . "</pre>";
                    
                    // Check if it's a choice field with items
                    if (in_array($field['type'], array('select', 'radio', 'checkbox')) && isset($field['options']['items'])) {
                        echo "<p><strong>Choice Options:</strong></p>";
                        echo "<ul>";
                        foreach ($field['options']['items'] as $item) {
                            echo "<li>{$item['label']} (value: {$item['value']})</li>";
                        }
                        echo "</ul>";
                        
                        if (count($field['options']['items']) > 0) {
                            echo "<p style='color: green;'>✓ Field options loaded correctly</p>";
                        } else {
                            echo "<p style='color: red;'>✗ No field options found</p>";
                        }
                    }
                } else {
                    echo "<p style='color: orange;'>⚠ No options found for this field</p>";
                }
                
                // Check conditional logic
                if (isset($field['conditional_logic'])) {
                    echo "<p><strong>Conditional Logic:</strong></p>";
                    echo "<pre>" . print_r($field['conditional_logic'], true) . "</pre>";
                }
            }
        } else {
            echo "<p style='color: red;'>✗ Failed to load form</p>";
        }
        
        // Test 3: Check database directly
        echo "<h2>Test 3: Check Database Directly</h2>";
        
        $fields_table = $wpdb->prefix . 'pfb_form_fields';
        $fields = $wpdb->get_results($wpdb->prepare("SELECT * FROM {$fields_table} WHERE form_id = %d ORDER BY field_order", $form_id), ARRAY_A);
        
        if ($fields) {
            echo "<p style='color: green;'>✓ Found " . count($fields) . " fields in database</p>";
            
            foreach ($fields as $field) {
                echo "<h4>Database Field: {$field['field_label']} ({$field['field_type']})</h4>";
                
                // Check raw field options
                echo "<p><strong>Raw field_options:</strong></p>";
                echo "<pre>" . htmlspecialchars($field['field_options']) . "</pre>";
                
                // Check unserialized field options
                $options = maybe_unserialize($field['field_options']);
                echo "<p><strong>Unserialized field_options:</strong></p>";
                echo "<pre>" . print_r($options, true) . "</pre>";
                
                // Check conditional logic
                if (!empty($field['conditional_logic'])) {
                    $conditional_logic = json_decode($field['conditional_logic'], true);
                    echo "<p><strong>Conditional Logic:</strong></p>";
                    echo "<pre>" . print_r($conditional_logic, true) . "</pre>";
                }
            }
        } else {
            echo "<p style='color: red;'>✗ No fields found in database</p>";
        }
        
        echo "<p><a href='admin.php?page=pfb-form-editor&form_id={$form_id}' target='_blank'>Edit this form in the admin</a></p>";
        
    } else {
        echo "<p style='color: red;'>✗ Failed to create form: {$result['message']}</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Exception: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Summary</h2>";
echo "<p>This test verifies that:</p>";
echo "<ul>";
echo "<li>Forms can be created with field options</li>";
echo "<li>Field options are properly saved to the database</li>";
echo "<li>Field options are properly loaded from the database</li>";
echo "<li>Choice fields (dropdown, radio, checkbox) have their items array</li>";
echo "<li>Conditional logic is properly handled</li>";
echo "</ul>";
?>
