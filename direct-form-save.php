<?php
/**
 * Direct Form Save Script
 * 
 * This script directly saves form data to the database, bypassing the complex form saver class.
 */

// Load WordPress
require_once('wp-load.php');

// Check if user is logged in and has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to access this page.');
}

// Get database connection
global $wpdb;
$forms_table = $wpdb->prefix . 'pfb_forms';
$fields_table = $wpdb->prefix . 'pfb_form_fields';

// Enable error display
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Function to save form directly
function save_form_directly($form_data) {
    global $wpdb, $forms_table, $fields_table;
    
    // Start transaction
    $wpdb->query('START TRANSACTION');
    
    try {
        // Prepare form data
        $form = array(
            'title' => isset($form_data['title']) ? sanitize_text_field($form_data['title']) : 'Untitled Form',
            'description' => isset($form_data['description']) ? sanitize_textarea_field($form_data['description']) : '',
            'status' => isset($form_data['status']) ? sanitize_text_field($form_data['status']) : 'publish',
            'settings' => isset($form_data['settings']) ? serialize($form_data['settings']) : serialize(array())
        );
        
        // Check if we're updating an existing form
        if (isset($form_data['id']) && !empty($form_data['id'])) {
            $form_id = intval($form_data['id']);
            
            // Update form
            $result = $wpdb->update(
                $forms_table,
                $form,
                array('id' => $form_id)
            );
            
            if ($result === false) {
                throw new Exception('Failed to update form: ' . $wpdb->last_error);
            }
            
            echo "<p>Updated form with ID: {$form_id}</p>";
        } else {
            // Insert new form
            $result = $wpdb->insert($forms_table, $form);
            
            if ($result === false) {
                throw new Exception('Failed to insert form: ' . $wpdb->last_error);
            }
            
            $form_id = $wpdb->insert_id;
            echo "<p>Created new form with ID: {$form_id}</p>";
        }
        
        // Delete existing fields
        $delete_result = $wpdb->delete($fields_table, array('form_id' => $form_id));
        
        if ($delete_result === false) {
            throw new Exception('Failed to delete existing fields: ' . $wpdb->last_error);
        }
        
        echo "<p>Deleted existing fields: {$delete_result}</p>";
        
        // Save fields
        if (isset($form_data['fields']) && is_array($form_data['fields'])) {
            foreach ($form_data['fields'] as $order => $field) {
                // Skip fields without type
                if (!isset($field['type']) || empty($field['type'])) {
                    echo "<p>Skipping field at index {$order}: No type specified</p>";
                    continue;
                }
                
                // Prepare field data
                $field_data = array(
                    'form_id' => $form_id,
                    'field_type' => sanitize_text_field($field['type']),
                    'field_label' => isset($field['label']) ? sanitize_text_field($field['label']) : 'Field ' . ($order + 1),
                    'field_name' => isset($field['name']) ? sanitize_key($field['name']) : 'field_' . ($order + 1),
                    'field_required' => isset($field['required']) && $field['required'] ? 1 : 0,
                    'field_width' => isset($field['width']) ? intval($field['width']) : 100,
                    'field_order' => $order
                );
                
                // Prepare field options
                $options = isset($field['options']) && is_array($field['options']) ? $field['options'] : array();
                
                // Special handling for choice fields
                if (in_array($field['type'], array('select', 'radio', 'checkbox'))) {
                    // Ensure items array exists
                    if (!isset($options['items']) || !is_array($options['items'])) {
                        $options['items'] = array(
                            array(
                                'label' => 'Option 1',
                                'value' => 'option_1',
                                'variable' => ''
                            )
                        );
                    }
                }
                
                // Serialize options
                $field_data['field_options'] = serialize($options);
                
                // Prepare conditional logic
                $conditional_logic = isset($field['conditional_logic']) && is_array($field['conditional_logic']) 
                    ? $field['conditional_logic'] 
                    : array('enabled' => false, 'logic_type' => 'all', 'rules' => array());
                
                // Encode conditional logic
                $field_data['conditional_logic'] = json_encode($conditional_logic);
                
                // Insert field
                $result = $wpdb->insert($fields_table, $field_data);
                
                if ($result === false) {
                    throw new Exception("Failed to insert field at index {$order}: " . $wpdb->last_error);
                }
                
                echo "<p>Inserted field: {$field_data['field_label']} ({$field_data['field_type']})</p>";
            }
        } else {
            echo "<p>No fields to save</p>";
        }
        
        // Commit transaction
        $wpdb->query('COMMIT');
        
        echo "<p style='color: green;'>Form saved successfully!</p>";
        echo "<p><a href='admin.php?page=pfb-form-editor&form_id={$form_id}'>Edit the form</a></p>";
        
        return $form_id;
    } catch (Exception $e) {
        // Rollback transaction
        $wpdb->query('ROLLBACK');
        
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'save_form') {
    $form_data = isset($_POST['form_data']) ? json_decode(stripslashes($_POST['form_data']), true) : null;
    
    if ($form_data) {
        echo "<h2>Saving Form</h2>";
        $form_id = save_form_directly($form_data);
    } else {
        echo "<p style='color: red;'>Error: Invalid form data</p>";
    }
}

// Main page
echo "<!DOCTYPE html>
<html>
<head>
    <title>Direct Form Save</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2, h3 { color: #333; }
        pre { background-color: #f5f5f5; padding: 10px; overflow: auto; }
        textarea { width: 100%; height: 300px; font-family: monospace; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Direct Form Save</h1>
    
    <form method='post'>
        <input type='hidden' name='action' value='save_form'>
        <p><label for='form_data'>Form Data (JSON):</label></p>
        <textarea id='form_data' name='form_data' required></textarea>
        <p><button type='submit'>Save Form</button></p>
    </form>
    
    <h2>Sample Form Data</h2>
    <pre>" . htmlspecialchars(json_encode(array(
        'title' => 'Sample Form',
        'description' => 'This is a sample form',
        'status' => 'publish',
        'settings' => array(
            'template' => 'default',
            'show_currency_selector' => '0',
            'submit_button_text' => 'Submit'
        ),
        'fields' => array(
            array(
                'type' => 'text',
                'label' => 'Text Field',
                'name' => 'text_field',
                'required' => false,
                'width' => 100,
                'options' => array(
                    'placeholder' => 'Enter text here',
                    'default' => '',
                    'description' => 'This is a text field'
                )
            ),
            array(
                'type' => 'select',
                'label' => 'Select Field',
                'name' => 'select_field',
                'required' => false,
                'width' => 100,
                'options' => array(
                    'items' => array(
                        array(
                            'label' => 'Option 1',
                            'value' => 'option_1',
                            'variable' => ''
                        ),
                        array(
                            'label' => 'Option 2',
                            'value' => 'option_2',
                            'variable' => ''
                        )
                    ),
                    'description' => 'This is a select field'
                )
            )
        )
    ), JSON_PRETTY_PRINT)) . "</pre>
</body>
</html>";
