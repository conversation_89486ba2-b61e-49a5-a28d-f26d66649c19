<?php
/**
 * Fired during plugin activation.
 *
 * This class defines all code necessary to run during the plugin's activation.
 *
 * @since      1.0.0
 */
class PFB_Activator {

    /**
     * Create necessary database tables on plugin activation.
     *
     * @since    1.0.0
     */
    public static function activate() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        // Forms table
        $table_name = $wpdb->prefix . 'pfb_forms';
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            title varchar(255) NOT NULL,
            description text,
            status varchar(20) NOT NULL DEFAULT 'draft',
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id)
        ) $charset_collate;";

        // Form fields table
        $table_name = $wpdb->prefix . 'pfb_form_fields';
        $sql .= "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            form_id mediumint(9) NOT NULL,
            field_type varchar(50) NOT NULL,
            field_label varchar(255) NOT NULL,
            field_name varchar(255) NOT NULL,
            field_options longtext,
            field_required tinyint(1) DEFAULT 0,
            field_order int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id),
            KEY form_id (form_id)
        ) $charset_collate;";

        // Price categories table
        $table_name = $wpdb->prefix . 'pfb_price_categories';
        $sql .= "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id)
        ) $charset_collate;";

        // Price variables table
        $table_name = $wpdb->prefix . 'pfb_price_variables';
        $sql .= "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            category_id mediumint(9) NOT NULL,
            name varchar(255) NOT NULL,
            variable_key varchar(255) NOT NULL,
            price_type varchar(20) NOT NULL DEFAULT 'fixed',
            price_value decimal(15,2) DEFAULT 0,
            price_ranges longtext,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id),
            KEY category_id (category_id)
        ) $charset_collate;";

        // Variables table (alias for price_variables for backward compatibility)
        $table_name = $wpdb->prefix . 'pfb_variables';
        $sql .= "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            category_id mediumint(9) NOT NULL,
            name varchar(255) NOT NULL,
            variable_key varchar(255) NOT NULL,
            type varchar(20) NOT NULL DEFAULT 'fixed',
            value decimal(15,2) DEFAULT 0,
            price decimal(15,2) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id),
            KEY category_id (category_id)
        ) $charset_collate;";

        // Currencies table
        $table_name = $wpdb->prefix . 'pfb_currencies';
        $sql .= "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL,
            code varchar(10) NOT NULL,
            symbol varchar(10) NOT NULL,
            exchange_rate decimal(15,6) DEFAULT 1,
            is_default tinyint(1) DEFAULT 0,
            symbol_position varchar(20) DEFAULT 'before',
            thousand_separator varchar(10) DEFAULT ',',
            decimal_separator varchar(10) DEFAULT '.',
            decimal_places int(2) DEFAULT 2,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id)
        ) $charset_collate;";

        // Form translations table
        $table_name = $wpdb->prefix . 'pfb_translations';
        $sql .= "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            object_type varchar(50) NOT NULL,
            object_id mediumint(9) NOT NULL,
            language_code varchar(10) NOT NULL,
            field_name varchar(100) NOT NULL,
            translated_value text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id),
            KEY object_type_id (object_type, object_id),
            KEY language_code (language_code)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);

        // Insert default currency
        $table_name = $wpdb->prefix . 'pfb_currencies';
        $default_currency = array(
            'name' => 'US Dollar',
            'code' => 'USD',
            'symbol' => '$',
            'exchange_rate' => 1,
            'is_default' => 1,
            'symbol_position' => 'before',
            'thousand_separator' => ',',
            'decimal_separator' => '.',
            'decimal_places' => 2
        );

        $wpdb->insert($table_name, $default_currency);
    }
}
