<?php
/**
 * Form Field Handler
 *
 * @since      1.0.0
 * @package    PFB
 * @subpackage PFB/includes
 */

/**
 * Form Field Handler class.
 *
 * This class handles saving and loading form fields.
 *
 * @since      1.0.0
 * @package    PFB
 * @subpackage PFB/includes
 */
class PFB_Form_Field_Handler {

    /**
     * Database table
     */
    private $fields_table;
    
    /**
     * Conditional logic handler
     */
    private $conditional_logic;

    /**
     * Initialize the class
     */
    public function __construct() {
        global $wpdb;
        $this->fields_table = $wpdb->prefix . 'pfb_form_fields';
        $this->conditional_logic = new PFB_Conditional_Logic();
    }

    /**
     * Save form fields
     *
     * @param int $form_id Form ID
     * @param array $fields Form fields
     * @return bool Success status
     */
    public function save_fields($form_id, $fields) {
        global $wpdb;
        
        error_log('PFB Form Field Handler: Saving fields for form ID ' . $form_id);
        
        try {
            // Ensure form ID is valid
            $form_id = intval($form_id);
            if ($form_id <= 0) {
                error_log('PFB Form Field Handler: Invalid form ID: ' . $form_id);
                return false;
            }
            
            // Delete existing fields
            error_log('PFB Form Field Handler: Deleting existing fields for form ID ' . $form_id);
            $wpdb->delete($this->fields_table, array('form_id' => $form_id));
            
            // If no fields, we're done
            if (empty($fields) || !is_array($fields)) {
                error_log('PFB Form Field Handler: No fields to save');
                return true;
            }
            
            // Insert new fields
            foreach ($fields as $order => $field) {
                // Skip fields without type
                if (!isset($field['type']) || empty($field['type'])) {
                    error_log('PFB Form Field Handler: Skipping field without type');
                    continue;
                }
                
                error_log('PFB Form Field Handler: Processing field: ' . print_r($field, true));
                
                // Prepare field data
                $field_data = $this->prepare_field_data($field, $form_id, $order);
                
                // Insert field
                $result = $wpdb->insert($this->fields_table, $field_data);
                
                if ($result === false) {
                    error_log('PFB Form Field Handler: Failed to insert field: ' . $wpdb->last_error);
                    error_log('PFB Form Field Handler: Field data: ' . print_r($field_data, true));
                } else {
                    error_log('PFB Form Field Handler: Field inserted successfully with ID: ' . $wpdb->insert_id);
                }
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log('PFB Form Field Handler: Exception saving fields: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Prepare field data for database
     *
     * @param array $field Field data
     * @param int $form_id Form ID
     * @param int $order Field order
     * @return array Prepared field data
     */
    private function prepare_field_data($field, $form_id, $order) {
        // Basic field data
        $field_data = array(
            'form_id'        => $form_id,
            'field_type'     => sanitize_text_field($field['type']),
            'field_label'    => isset($field['label']) ? sanitize_text_field($field['label']) : 'Field ' . ($order + 1),
            'field_name'     => isset($field['name']) ? sanitize_key($field['name']) : 'field_' . ($order + 1),
            'field_required' => isset($field['required']) && $field['required'] ? 1 : 0,
            'field_order'    => $order
        );
        
        // Field width
        $field_data['field_width'] = isset($field['width']) ? $this->sanitize_width($field['width']) : 100;
        
        // Field options
        $field_data['field_options'] = $this->prepare_field_options($field);
        
        // Conditional logic
        $field_data['conditional_logic'] = $this->prepare_conditional_logic($field);
        
        return $field_data;
    }
    
    /**
     * Sanitize field width
     *
     * @param mixed $width Field width
     * @return int Sanitized width
     */
    private function sanitize_width($width) {
        // Remove % if present
        $width = str_replace('%', '', $width);
        
        // Ensure it's a number
        if (!is_numeric($width)) {
            return 100;
        }
        
        // Ensure it's between 1 and 100
        $width = max(1, min(100, intval($width)));
        
        return $width;
    }
    
    /**
     * Prepare field options
     *
     * @param array $field Field data
     * @return string Serialized field options
     */
    private function prepare_field_options($field) {
        $options = isset($field['options']) && is_array($field['options']) ? $field['options'] : array();
        
        // Process based on field type
        switch ($field['type']) {
            case 'select':
            case 'radio':
            case 'checkbox':
                $options = $this->prepare_choice_field_options($options);
                break;
                
            case 'number':
                $options = $this->prepare_number_field_options($options);
                break;
                
            case 'slider':
                $options = $this->prepare_slider_field_options($options);
                break;
                
            case 'price':
            case 'total':
                $options = $this->prepare_price_field_options($options);
                break;
                
            case 'subtotal':
                $options = $this->prepare_subtotal_field_options($options);
                break;
                
            case 'text':
                $options = $this->prepare_text_field_options($field, $options);
                break;
        }
        
        // Serialize options
        return serialize($options);
    }
    
    /**
     * Prepare choice field options
     *
     * @param array $options Field options
     * @return array Processed options
     */
    private function prepare_choice_field_options($options) {
        // Ensure items array exists
        if (!isset($options['items']) || !is_array($options['items'])) {
            $options['items'] = array(
                array(
                    'label' => 'Option 1',
                    'value' => 'option_1',
                    'variable' => ''
                )
            );
        } else {
            // Process each item
            foreach ($options['items'] as $key => $item) {
                $options['items'][$key] = array(
                    'label' => isset($item['label']) ? sanitize_text_field($item['label']) : 'Option ' . ($key + 1),
                    'value' => isset($item['value']) ? sanitize_text_field($item['value']) : 'option_' . ($key + 1),
                    'variable' => isset($item['variable']) ? sanitize_text_field($item['variable']) : ''
                );
            }
        }
        
        return $options;
    }
    
    /**
     * Prepare number field options
     *
     * @param array $options Field options
     * @return array Processed options
     */
    private function prepare_number_field_options($options) {
        // Set min, max, and step
        $options['min'] = isset($options['min']) ? sanitize_text_field($options['min']) : '0';
        $options['max'] = isset($options['max']) ? sanitize_text_field($options['max']) : '100';
        $options['step'] = isset($options['step']) ? sanitize_text_field($options['step']) : '1';
        
        return $options;
    }
    
    /**
     * Prepare slider field options
     *
     * @param array $options Field options
     * @return array Processed options
     */
    private function prepare_slider_field_options($options) {
        // Set min, max, step, and default
        $options['min'] = isset($options['min']) ? intval($options['min']) : 0;
        $options['max'] = isset($options['max']) ? intval($options['max']) : 100;
        $options['step'] = isset($options['step']) ? intval($options['step']) : 1;
        $options['default'] = isset($options['default']) ? intval($options['default']) : 50;
        
        return $options;
    }
    
    /**
     * Prepare price field options
     *
     * @param array $options Field options
     * @return array Processed options
     */
    private function prepare_price_field_options($options) {
        // Set formula
        $options['formula'] = isset($options['formula']) ? $options['formula'] : '';
        
        return $options;
    }
    
    /**
     * Prepare subtotal field options
     *
     * @param array $options Field options
     * @return array Processed options
     */
    private function prepare_subtotal_field_options($options) {
        // Ensure lines array exists
        if (!isset($options['lines']) || !is_array($options['lines'])) {
            $options['lines'] = array(
                array(
                    'label' => 'Line 1',
                    'formula' => ''
                )
            );
        } else {
            // Process each line
            foreach ($options['lines'] as $key => $line) {
                $options['lines'][$key] = array(
                    'label' => isset($line['label']) ? sanitize_text_field($line['label']) : 'Line ' . ($key + 1),
                    'formula' => isset($line['formula']) ? $line['formula'] : ''
                );
            }
        }
        
        // Set empty_value
        $options['empty_value'] = isset($options['empty_value']) ? sanitize_text_field($options['empty_value']) : '---';
        
        return $options;
    }
    
    /**
     * Prepare text field options
     *
     * @param array $field Field data
     * @param array $options Field options
     * @return array Processed options
     */
    private function prepare_text_field_options($field, $options) {
        // Handle hidden field properties
        if (isset($field['hidden']) && $field['hidden']) {
            $options['is_hidden'] = true;
            
            if (isset($field['variable'])) {
                $options['variable'] = sanitize_text_field($field['variable']);
            }
            
            if (isset($field['default_value'])) {
                $options['default_value'] = sanitize_text_field($field['default_value']);
            }
        }
        
        return $options;
    }
    
    /**
     * Prepare conditional logic
     *
     * @param array $field Field data
     * @return string JSON encoded conditional logic
     */
    private function prepare_conditional_logic($field) {
        // Get conditional logic from field
        $conditional_logic = isset($field['conditional_logic']) ? $field['conditional_logic'] : array();
        
        // Use conditional logic handler to prepare for DB
        return $this->conditional_logic->prepare_for_db($conditional_logic);
    }
}
