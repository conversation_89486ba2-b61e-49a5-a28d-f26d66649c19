<?php
/**
 * Form Save Handler
 *
 * @since      1.0.0
 * @package    PFB
 * @subpackage PFB/includes
 */

/**
 * Form Save Handler class.
 *
 * This class handles saving forms to the database.
 *
 * @since      1.0.0
 * @package    PFB
 * @subpackage PFB/includes
 */
class PFB_Form_Save_Handler {

    /**
     * Database tables
     */
    private $forms_table;
    private $fields_table;

    /**
     * Initialize the class
     */
    public function __construct() {
        global $wpdb;
        $this->forms_table = $wpdb->prefix . 'pfb_forms';
        $this->fields_table = $wpdb->prefix . 'pfb_form_fields';

        // Add AJAX handlers
        add_action('wp_ajax_pfb_save_form_v2', array($this, 'ajax_save_form'));
    }

    /**
     * AJAX handler for saving forms
     */
    public function ajax_save_form() {
        try {
            error_log('PFB Form Save Handler: AJAX save form request received');

            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
                error_log('PFB Form Save Handler: Nonce verification failed');
                wp_send_json_error(array('message' => 'Security check failed.'));
                return;
            }

            // Check if user has permission
            if (!current_user_can('manage_options')) {
                wp_send_json_error(array('message' => 'You do not have permission to save forms.'));
                return;
            }

            // Get form data
            $form_data = isset($_POST['form_data']) ? $_POST['form_data'] : null;

            if (!$form_data) {
                wp_send_json_error(array('message' => 'No form data provided.'));
                return;
            }

            // Save form
            $result = $this->save_form($form_data);

            if ($result['success']) {
                wp_send_json_success(array(
                    'message' => 'Form saved successfully.',
                    'form_id' => $result['form_id']
                ));
            } else {
                wp_send_json_error(array('message' => $result['message']));
            }
        } catch (Exception $e) {
            wp_send_json_error(array('message' => 'Error: ' . $e->getMessage()));
        }
    }

    /**
     * Save form to database
     *
     * @param array $form_data Form data
     * @return array Result with success status, message, and form ID
     */
    public function save_form($form_data) {
        global $wpdb;

        error_log('PFB Form Save Handler: Starting form save process');
        error_log('PFB Form Save Handler: Form data: ' . print_r($form_data, true));

        // Start transaction
        $wpdb->query('START TRANSACTION');
        error_log('PFB Form Save Handler: Transaction started');

        try {
            // Validate form data
            if (!isset($form_data['title']) || empty($form_data['title'])) {
                throw new Exception('Form title is required.');
            }

            // Prepare form data
            $form = array(
                'title'       => sanitize_text_field($form_data['title']),
                'description' => isset($form_data['description']) ? sanitize_textarea_field($form_data['description']) : '',
                'status'      => isset($form_data['status']) ? sanitize_text_field($form_data['status']) : 'publish'
            );

            // Prepare form settings separately (will be stored in options table)
            $form_settings = $this->prepare_form_settings(isset($form_data['settings']) ? $form_data['settings'] : array());

            // Check if we're updating an existing form
            if (isset($form_data['id']) && !empty($form_data['id'])) {
                $form_id = intval($form_data['id']);
                error_log('PFB Form Save Handler: Updating existing form with ID: ' . $form_id);

                // Check if form exists
                $existing_form = $wpdb->get_var($wpdb->prepare("SELECT id FROM {$this->forms_table} WHERE id = %d", $form_id));
                error_log('PFB Form Save Handler: Form exists check: ' . ($existing_form ? 'YES' : 'NO'));

                if (!$existing_form) {
                    throw new Exception("Form with ID {$form_id} does not exist.");
                }

                // Log the form data we're about to save
                error_log('PFB Form Save Handler: Form data to update: ' . print_r($form, true));

                // Update form
                $result = $wpdb->update(
                    $this->forms_table,
                    $form,
                    array('id' => $form_id)
                );

                if ($result === false) {
                    error_log('PFB Form Save Handler: Update failed: ' . $wpdb->last_error);
                    throw new Exception('Failed to update form: ' . $wpdb->last_error);
                }

                error_log('PFB Form Save Handler: Form updated successfully');
            } else {
                error_log('PFB Form Save Handler: Creating new form');

                // Log the form data we're about to save
                error_log('PFB Form Save Handler: Form data to insert: ' . print_r($form, true));

                // Insert new form
                $result = $wpdb->insert($this->forms_table, $form);

                if ($result === false) {
                    error_log('PFB Form Save Handler: Insert failed: ' . $wpdb->last_error);
                    throw new Exception('Failed to insert form: ' . $wpdb->last_error);
                }

                $form_id = $wpdb->insert_id;
                error_log('PFB Form Save Handler: New form created with ID: ' . $form_id);
            }

            // Save form settings in WordPress options table
            error_log('PFB Form Save Handler: Saving form settings for form ID ' . $form_id . ': ' . print_r($form_settings, true));
            $settings_saved = update_option('pfb_form_settings_' . $form_id, $form_settings);
            error_log('PFB Form Save Handler: Settings saved: ' . ($settings_saved ? 'YES' : 'NO'));

            // Save form fields
            if (isset($form_data['fields']) && is_array($form_data['fields'])) {
                $this->save_form_fields($form_id, $form_data['fields']);
            }

            // Commit transaction
            $wpdb->query('COMMIT');

            return array(
                'success' => true,
                'message' => 'Form saved successfully.',
                'form_id' => $form_id
            );
        } catch (Exception $e) {
            // Rollback transaction on error
            $wpdb->query('ROLLBACK');

            return array(
                'success' => false,
                'message' => $e->getMessage()
            );
        }
    }

    /**
     * Prepare form settings
     *
     * @param array $settings Form settings
     * @return array Sanitized settings array
     */
    private function prepare_form_settings($settings) {
        // Ensure settings is an array
        if (!is_array($settings)) {
            $settings = array();
        }

        // Sanitize settings
        $sanitized_settings = array();

        // Template
        if (isset($settings['template'])) {
            $sanitized_settings['template'] = sanitize_text_field($settings['template']);
        }

        // Show currency selector
        if (isset($settings['show_currency_selector'])) {
            $sanitized_settings['show_currency_selector'] = sanitize_text_field($settings['show_currency_selector']);
        }

        // Submit button text
        if (isset($settings['submit_button_text'])) {
            $sanitized_settings['submit_button_text'] = sanitize_text_field($settings['submit_button_text']);
        }

        return $sanitized_settings;
    }

    /**
     * Save form fields
     *
     * @param int $form_id Form ID
     * @param array $fields Form fields
     * @throws Exception If field saving fails
     */
    private function save_form_fields($form_id, $fields) {
        global $wpdb;

        error_log('PFB Form Save Handler: Saving ' . count($fields) . ' fields for form ID: ' . $form_id);

        // Delete existing fields
        error_log('PFB Form Save Handler: Deleting existing fields for form ID: ' . $form_id);
        $delete_result = $wpdb->delete($this->fields_table, array('form_id' => $form_id));
        error_log('PFB Form Save Handler: Delete result: ' . ($delete_result !== false ? $delete_result : 'false'));

        // Insert new fields
        foreach ($fields as $order => $field) {
            // Skip fields without type
            if (!isset($field['type']) || empty($field['type'])) {
                continue;
            }

            // Prepare field data
            $field_data = array(
                'form_id'        => $form_id,
                'field_type'     => sanitize_text_field($field['type']),
                'field_label'    => isset($field['label']) ? sanitize_text_field($field['label']) : 'Field ' . ($order + 1),
                'field_name'     => isset($field['name']) ? sanitize_key($field['name']) : 'field_' . ($order + 1),
                'field_required' => isset($field['required']) && $field['required'] ? 1 : 0,
                'field_width'    => isset($field['width']) ? intval($field['width']) : 100,
                'field_order'    => $order
            );

            // Process field options
            $field_data['field_options'] = $this->prepare_field_options($field);

            // Process conditional logic
            $field_data['conditional_logic'] = $this->prepare_conditional_logic($field);

            // Log field data before insertion
            error_log('PFB Form Save Handler: Inserting field: ' . $field['type'] . ' - ' . (isset($field['label']) ? $field['label'] : 'Unnamed'));

            // Insert field
            $result = $wpdb->insert($this->fields_table, $field_data);

            if ($result === false) {
                error_log('PFB Form Save Handler: Field insertion failed: ' . $wpdb->last_error);
                error_log('PFB Form Save Handler: Field data: ' . print_r($field_data, true));
                throw new Exception('Failed to insert field: ' . $wpdb->last_error);
            }

            error_log('PFB Form Save Handler: Field inserted successfully with ID: ' . $wpdb->insert_id);
        }
    }

    /**
     * Prepare field options
     *
     * @param array $field Field data
     * @return string Serialized field options
     */
    private function prepare_field_options($field) {
        $options = isset($field['options']) && is_array($field['options']) ? $field['options'] : array();

        // Process field options based on field type
        switch ($field['type']) {
            case 'select':
            case 'radio':
            case 'checkbox':
                $options = $this->prepare_choice_field_options($options);
                break;

            case 'number':
                $options = $this->prepare_number_field_options($options);
                break;

            case 'slider':
                $options = $this->prepare_slider_field_options($options);
                break;

            case 'price':
            case 'total':
                $options = $this->prepare_price_field_options($options, $field['type']);
                break;

            case 'subtotal':
                $options = $this->prepare_subtotal_field_options($options);
                break;

            case 'text':
                $options = $this->prepare_text_field_options($options, $field);
                break;
        }

        // Serialize options
        return serialize($options);
    }

    /**
     * Prepare choice field options
     *
     * @param array $options Field options
     * @return array Processed options
     */
    private function prepare_choice_field_options($options) {
        // Ensure items array exists
        if (!isset($options['items']) || !is_array($options['items'])) {
            $options['items'] = array(
                array(
                    'label' => 'Option 1',
                    'value' => 'option_1',
                    'variable' => ''
                )
            );
        } else {
            // Process each item
            foreach ($options['items'] as $key => $item) {
                $options['items'][$key] = array(
                    'label' => isset($item['label']) ? sanitize_text_field($item['label']) : 'Option ' . ($key + 1),
                    'value' => isset($item['value']) ? sanitize_text_field($item['value']) : 'option_' . ($key + 1),
                    'variable' => isset($item['variable']) ? sanitize_text_field($item['variable']) : ''
                );
            }
        }

        return $options;
    }

    /**
     * Prepare number field options
     *
     * @param array $options Field options
     * @return array Processed options
     */
    private function prepare_number_field_options($options) {
        // Set min, max, and step
        $options['min'] = isset($options['min']) ? sanitize_text_field($options['min']) : '0';
        $options['max'] = isset($options['max']) ? sanitize_text_field($options['max']) : '100';
        $options['step'] = isset($options['step']) ? sanitize_text_field($options['step']) : '1';

        return $options;
    }

    /**
     * Prepare slider field options
     *
     * @param array $options Field options
     * @return array Processed options
     */
    private function prepare_slider_field_options($options) {
        // Set min, max, step, and default
        $options['min'] = isset($options['min']) ? intval($options['min']) : 0;
        $options['max'] = isset($options['max']) ? intval($options['max']) : 100;
        $options['step'] = isset($options['step']) ? intval($options['step']) : 1;
        $options['default'] = isset($options['default']) ? intval($options['default']) : 50;

        return $options;
    }

    /**
     * Prepare price/total field options
     *
     * @param array $options Field options
     * @param string $field_type Field type
     * @return array Processed options
     */
    private function prepare_price_field_options($options, $field_type) {
        // Set formula
        $options['formula'] = isset($options['formula']) ? $options['formula'] : '';

        // For price fields, set price
        if ($field_type === 'price') {
            $options['price'] = isset($options['price']) ? sanitize_text_field($options['price']) : '0';
        }

        return $options;
    }

    /**
     * Prepare subtotal field options
     *
     * @param array $options Field options
     * @return array Processed options
     */
    private function prepare_subtotal_field_options($options) {
        // Ensure lines array exists
        if (!isset($options['lines']) || !is_array($options['lines'])) {
            $options['lines'] = array(
                array(
                    'label' => 'Line 1',
                    'formula' => ''
                )
            );
        } else {
            // Process each line
            foreach ($options['lines'] as $key => $line) {
                $options['lines'][$key] = array(
                    'label' => isset($line['label']) ? sanitize_text_field($line['label']) : 'Line ' . ($key + 1),
                    'formula' => isset($line['formula']) ? $line['formula'] : ''
                );
            }
        }

        // Set empty_value
        $options['empty_value'] = isset($options['empty_value']) ? sanitize_text_field($options['empty_value']) : '---';

        return $options;
    }

    /**
     * Prepare text field options
     *
     * @param array $options Field options
     * @param array $field Field data
     * @return array Processed options
     */
    private function prepare_text_field_options($options, $field) {
        // Handle hidden field properties
        if (isset($field['hidden']) && $field['hidden']) {
            $options['is_hidden'] = true;

            if (isset($field['variable'])) {
                $options['variable'] = sanitize_text_field($field['variable']);
            }

            if (isset($field['default_value'])) {
                $options['default_value'] = sanitize_text_field($field['default_value']);
            }
        }

        return $options;
    }

    /**
     * Prepare conditional logic
     *
     * @param array $field Field data
     * @return string JSON encoded conditional logic
     */
    private function prepare_conditional_logic($field) {
        // Default conditional logic
        $conditional_logic = array(
            'enabled' => false,
            'logic_type' => 'all',
            'rules' => array()
        );

        // Check if conditional logic exists
        if (isset($field['conditional_logic']) && is_array($field['conditional_logic'])) {
            // Set enabled
            $conditional_logic['enabled'] = isset($field['conditional_logic']['enabled']) ?
                (bool) $field['conditional_logic']['enabled'] : false;

            // Set logic type
            $conditional_logic['logic_type'] = isset($field['conditional_logic']['logic_type']) ?
                sanitize_text_field($field['conditional_logic']['logic_type']) : 'all';

            // Process rules
            if (isset($field['conditional_logic']['rules']) && is_array($field['conditional_logic']['rules'])) {
                foreach ($field['conditional_logic']['rules'] as $rule) {
                    // Skip rules without field or operator
                    if (!isset($rule['field']) || empty($rule['field']) || !isset($rule['operator']) || empty($rule['operator'])) {
                        continue;
                    }

                    $conditional_logic['rules'][] = array(
                        'field' => sanitize_text_field($rule['field']),
                        'field_name' => isset($rule['field_name']) ? sanitize_text_field($rule['field_name']) : '',
                        'operator' => sanitize_text_field($rule['operator']),
                        'value' => isset($rule['value']) ? sanitize_text_field($rule['value']) : ''
                    );
                }
            }
        }

        // Encode conditional logic
        return json_encode($conditional_logic);
    }
}
