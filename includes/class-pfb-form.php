<?php
/**
 * Form operations for the plugin.
 *
 * @since      1.0.0
 */
class PFB_Form {

    /**
     * Form data.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $form    Form data.
     */
    private $form;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    array    $form    Form data.
     */
    public function __construct($form = null) {
        if ($form) {
            $this->form = $form;
        }
    }

    /**
     * Load form by ID.
     *
     * @since    1.0.0
     * @param    int      $id    Form ID.
     * @return   bool            True if form was loaded, false otherwise.
     */
    public function load($id) {
        error_log('PFB_Form: Loading form with ID: ' . $id);

        try {
            $form = PFB_DB::get_form($id);

            if (!$form) {
                error_log('PFB_Form: Form not found with ID: ' . $id);
                return false;
            }

            error_log('PFB_Form: Form loaded successfully with ID: ' . $id);
            error_log('PFB_Form: Form has ' . (isset($form['fields']) && is_array($form['fields']) ? count($form['fields']) : 0) . ' fields');

            // Add form settings if not already present
            if (!isset($form['settings'])) {
                $form['settings'] = get_option('pfb_form_settings_' . $id, array());
                error_log('PFB_Form: Added form settings: ' . print_r($form['settings'], true));
            }

            $this->form = $form;
            return true;

        } catch (Exception $e) {
            error_log('PFB_Form: Exception loading form: ' . $e->getMessage());
            error_log('PFB_Form: Exception trace: ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Get form data.
     *
     * @since    1.0.0
     * @return   array    Form data.
     */
    public function get_data() {
        $form_data = $this->form;

        // Add form settings if available
        if (isset($form_data['id'])) {
            $form_data['settings'] = get_option('pfb_form_settings_' . $form_data['id'], array());
        }

        return $form_data;
    }

    /**
     * Get form ID.
     *
     * @since    1.0.0
     * @return   int    Form ID.
     */
    public function get_id() {
        return isset($this->form['id']) ? $this->form['id'] : 0;
    }

    /**
     * Get form title.
     *
     * @since    1.0.0
     * @return   string    Form title.
     */
    public function get_title() {
        return isset($this->form['title']) ? $this->form['title'] : '';
    }

    /**
     * Get form description.
     *
     * @since    1.0.0
     * @return   string    Form description.
     */
    public function get_description() {
        return isset($this->form['description']) ? $this->form['description'] : '';
    }

    /**
     * Get form fields.
     *
     * @since    1.0.0
     * @return   array    Form fields.
     */
    public function get_fields() {
        return isset($this->form['fields']) ? $this->form['fields'] : array();
    }

    /**
     * Save form.
     *
     * @since    1.0.0
     * @return   int       Form ID.
     */
    public function save() {
        // Debug log for form data before saving
        error_log('PFB Form: Saving form with data: ' . print_r($this->form, true));

        // Create a field map for easier reference
        $field_map = array();
        if (isset($this->form['fields']) && is_array($this->form['fields'])) {
            foreach ($this->form['fields'] as $field) {
                if (isset($field['field_name'])) {
                    // Map by field name
                    $field_map[$field['field_name']] = array(
                        'id' => isset($field['id']) ? $field['id'] : '',
                        'label' => isset($field['field_label']) ? $field['field_label'] : '',
                        'type' => isset($field['field_type']) ? $field['field_type'] : ''
                    );

                    // Also map by ID if available
                    if (isset($field['id'])) {
                        $field_map[$field['id']] = array(
                            'name' => $field['field_name'],
                            'label' => isset($field['field_label']) ? $field['field_label'] : '',
                            'type' => isset($field['field_type']) ? $field['field_type'] : ''
                        );
                    }
                }
            }
        }

        error_log('PFB Form: Created field map: ' . print_r($field_map, true));

        // Process conditional logic for each field
        if (isset($this->form['fields']) && is_array($this->form['fields'])) {
            error_log('PFB Form: Processing conditional logic for ' . count($this->form['fields']) . ' fields');

            foreach ($this->form['fields'] as $key => $field) {
                $field_id = isset($field['id']) ? $field['id'] : '';
                $field_name = isset($field['field_name']) ? $field['field_name'] : '';

                error_log("PFB Form: Processing field {$field_id} ({$field_name})");

                // Ensure conditional_logic is properly formatted
                if (isset($field['conditional_logic'])) {
                    // If it's a string, try to decode it
                    if (is_string($field['conditional_logic'])) {
                        $decoded = json_decode($field['conditional_logic'], true);
                        if ($decoded !== null) {
                            $field['conditional_logic'] = $decoded;
                            error_log('PFB Form: Successfully decoded conditional logic JSON');
                        } else {
                            error_log('PFB Form: Failed to decode conditional logic JSON: ' . $field['conditional_logic']);
                            // Create a default structure
                            $field['conditional_logic'] = [
                                'enabled' => false,
                                'logic_type' => 'all',
                                'rules' => []
                            ];
                        }
                    }

                    // Now we should have an array
                    if (is_array($field['conditional_logic'])) {
                        // Make sure enabled flag is set
                        if (!isset($field['conditional_logic']['enabled'])) {
                            $has_rules = isset($field['conditional_logic']['rules']) &&
                                        is_array($field['conditional_logic']['rules']) &&
                                        count($field['conditional_logic']['rules']) > 0;

                            $field['conditional_logic']['enabled'] = $has_rules;
                            error_log('PFB Form: Set enabled flag to: ' . ($has_rules ? 'true' : 'false'));
                        }

                        // Make sure logic_type is set
                        if (!isset($field['conditional_logic']['logic_type'])) {
                            $field['conditional_logic']['logic_type'] = 'all';
                            error_log('PFB Form: Set default logic_type to: all');
                        }

                        // Process rules to ensure they have all necessary data
                        if (isset($field['conditional_logic']['rules']) && is_array($field['conditional_logic']['rules'])) {
                            error_log('PFB Form: Processing ' . count($field['conditional_logic']['rules']) . ' rules');

                            foreach ($field['conditional_logic']['rules'] as $rule_key => $rule) {
                                // Make sure the rule has a field reference
                                if (isset($rule['field'])) {
                                    // If we have the field in our map by ID, add its name and label
                                    if (isset($field_map[$rule['field']])) {
                                        $field['conditional_logic']['rules'][$rule_key]['field_name'] = $field_map[$rule['field']]['name'];
                                        $field['conditional_logic']['rules'][$rule_key]['field_label'] = $field_map[$rule['field']]['label'];
                                        error_log('PFB Form: Updated rule with field data from ID: ' . $rule['field']);
                                    }
                                    // If we have field_name but no mapping by ID, try to find by name
                                    else if (isset($rule['field_name']) && isset($field_map[$rule['field_name']])) {
                                        // Update the field ID to match the current field ID
                                        $field['conditional_logic']['rules'][$rule_key]['field'] = $field_map[$rule['field_name']]['id'];
                                        error_log('PFB Form: Updated rule field ID based on field_name: ' . $rule['field_name']);
                                    }
                                    // If we still can't find it, search all fields by name
                                    else if (isset($rule['field_name'])) {
                                        foreach ($this->form['fields'] as $search_field) {
                                            if (isset($search_field['field_name']) && $search_field['field_name'] === $rule['field_name']) {
                                                $field['conditional_logic']['rules'][$rule_key]['field'] = isset($search_field['id']) ? $search_field['id'] : '';
                                                error_log('PFB Form: Updated rule field ID from search: ' . $rule['field_name']);
                                                break;
                                            }
                                        }
                                    }
                                }
                                // If we only have field_name, try to find the field ID
                                else if (isset($rule['field_name']) && !empty($rule['field_name'])) {
                                    if (isset($field_map[$rule['field_name']]) && isset($field_map[$rule['field_name']]['id'])) {
                                        $field['conditional_logic']['rules'][$rule_key]['field'] = $field_map[$rule['field_name']]['id'];
                                        error_log('PFB Form: Set field ID based on field_name: ' . $rule['field_name']);
                                    }
                                }

                                // Make sure operator is set
                                if (!isset($rule['operator']) || empty($rule['operator'])) {
                                    $field['conditional_logic']['rules'][$rule_key]['operator'] = 'is';
                                    error_log('PFB Form: Set default operator to: is');
                                }

                                // Make sure value is set
                                if (!isset($rule['value'])) {
                                    $field['conditional_logic']['rules'][$rule_key]['value'] = '';
                                    error_log('PFB Form: Set default value to empty string');
                                }
                            }
                        } else {
                            // Initialize rules array if it doesn't exist
                            $field['conditional_logic']['rules'] = [];
                            error_log('PFB Form: Initialized empty rules array');
                        }

                        // Update the field's conditional logic
                        $this->form['fields'][$key]['conditional_logic'] = $field['conditional_logic'];
                    }

                    // Convert to JSON string for database storage
                    $this->form['fields'][$key]['conditional_logic'] = json_encode($this->form['fields'][$key]['conditional_logic']);
                    error_log('PFB Form: Encoded conditional logic for field ' . $field_name);
                } else {
                    // No conditional logic, set to empty object
                    $this->form['fields'][$key]['conditional_logic'] = json_encode([
                        'enabled' => false,
                        'logic_type' => 'all',
                        'rules' => []
                    ]);
                    error_log('PFB Form: Created empty conditional logic for field ' . $field_name);
                }
            }
        }

        $form_id = PFB_DB::save_form($this->form);

        if ($form_id) {
            // Save form settings if available
            if (isset($this->form['settings']) && is_array($this->form['settings'])) {
                update_option('pfb_form_settings_' . $form_id, $this->form['settings']);
            }

            error_log('PFB Form: Form saved successfully with ID: ' . $form_id);
        } else {
            error_log('PFB Form: Failed to save form');
        }

        return $form_id;
    }

    /**
     * Delete form.
     *
     * @since    1.0.0
     * @return   bool      True on success, false on failure.
     */
    public function delete() {
        if (!isset($this->form['id'])) {
            return false;
        }

        return PFB_DB::delete_form($this->form['id']);
    }

    /**
     * Render form.
     *
     * @since    1.0.0
     * @param    array    $atts    Shortcode attributes.
     * @return   string            Form HTML.
     */
    public function render($atts = array()) {
        if (!isset($this->form['id'])) {
            return '';
        }

        $defaults = array(
            'id'       => $this->form['id'],
            'title'    => true,
            'currency' => '',
            'language' => ''
        );

        $atts = wp_parse_args($atts, $defaults);

        // Get current language if not specified
        if (empty($atts['language'])) {
            $atts['language'] = get_locale();
        }

        // Get translations if needed
        $translations = array();
        if ($atts['language'] !== 'en_US') {
            $translations = PFB_DB::get_translations('form', $this->form['id'], $atts['language']);
        }

        // Get currency
        $currency = null;
        if (!empty($atts['currency'])) {
            global $wpdb;
            $table_name = $wpdb->prefix . 'pfb_currencies';
            $currency = $wpdb->get_row(
                $wpdb->prepare("SELECT * FROM $table_name WHERE code = %s", $atts['currency']),
                ARRAY_A
            );
        }

        if (!$currency) {
            $currency = PFB_DB::get_default_currency();
        }

        // Start output buffering
        ob_start();

        // Get form settings
        $form_settings = get_option('pfb_form_settings_' . $this->form['id'], array());

        // Debug log for form settings
        error_log('PFB: Form settings for form ' . $this->form['id'] . ': ' . print_r($form_settings, true));

        // If no specific settings for this form, use global settings
        if (empty($form_settings)) {
            $form_settings = array(
                'template' => get_option('pfb_form_template', 'default'),
                'show_currency_selector' => get_option('pfb_show_currency_selector', '1')
            );
            error_log('PFB: Using global settings for form ' . $this->form['id'] . ': ' . print_r($form_settings, true));
        }

        // Make $this available in the template
        $form_object = $this;

        // Include template
        include PFB_PLUGIN_DIR . 'public/partials/pfb-form-display.php';

        // Get the buffered content
        $output = ob_get_clean();

        return $output;
    }

    /**
     * Calculate price based on form data.
     *
     * @since    1.0.0
     * @param    array    $form_data    Form data.
     * @return   float                  Calculated price.
     */
    public function calculate_price($form_data) {
        $calculator = new PFB_Calculator();
        return $calculator->calculate($this->form, $form_data);
    }

    /**
     * Calculate price with subtotals based on form data.
     *
     * @since    1.0.0
     * @param    array    $form_data    Form data.
     * @return   array                  Array with total and subtotals.
     */
    public function calculate_price_with_subtotals($form_data) {
        $calculator = new PFB_Calculator();

        // Get the raw calculation result which includes subtotals
        $result = $calculator->calculate($this->form, $form_data);

        // If the result is a simple number, convert it to our expected format
        if (!is_array($result)) {
            $result = array(
                'total' => $result,
                'subtotals' => array()
            );
        }

        // If we don't have subtotals in the result, try to get them from form_data
        if (!isset($result['subtotals']) || empty($result['subtotals'])) {
            $result['subtotals'] = array();

            foreach ($this->form['fields'] as $field) {
                if ($field['field_type'] === 'subtotal') {
                    $field_name = $field['field_name'];
                    $options = maybe_unserialize($field['field_options']);

                    // Check if this is a multi-line subtotal
                    if (isset($options['lines']) && is_array($options['lines']) && !empty($options['lines'])) {
                        // Create a structured subtotal with lines
                        $lines = array();
                        $subtotal_value = 0;

                        foreach ($options['lines'] as $line) {
                            if (isset($line['formula']) && !empty($line['formula'])) {
                                $line_label = isset($line['label']) ? $line['label'] : '';
                                $line_value = 0; // Default to 0 if we can't calculate

                                // Try to calculate the line value
                                try {
                                    $line_value = $calculator->calculate_single_formula($line['formula'], $this->form, $form_data);
                                    $subtotal_value += $line_value;
                                } catch (Exception $e) {
                                    error_log('Error calculating line value: ' . $e->getMessage());
                                }

                                $lines[] = array(
                                    'label' => $line_label,
                                    'value' => $line_value,
                                    'formula' => $line['formula'],
                                    'has_required_fields' => false // We don't know, so assume false
                                );
                            }
                        }

                        $result['subtotals'][$field_name] = array(
                            'total' => $subtotal_value,
                            'lines' => $lines,
                            'empty_value' => isset($options['empty_value']) ? $options['empty_value'] : '---'
                        );
                    }
                    // Legacy single-value subtotal
                    else if (isset($form_data[$field_name])) {
                        $result['subtotals'][$field_name] = floatval($form_data[$field_name]);
                    }
                }
            }
        }

        return $result;
    }

    /**
     * Get available field types.
     *
     * @since    1.0.0
     * @return   array    Field types.
     */
    public static function get_field_types() {
        return array(
            'text'     => __('Text Input', 'price-form-builder'),
            'number'   => __('Number Input', 'price-form-builder'),
            'slider'   => __('Slider', 'price-form-builder'),
            'dropdown' => __('Dropdown', 'price-form-builder'),
            'radio'    => __('Radio Buttons', 'price-form-builder'),
            'checkbox' => __('Checkboxes', 'price-form-builder'),
            'subtotal' => __('Subtotal', 'price-form-builder'),
            'total'    => __('Total', 'price-form-builder')
        );
    }

    /**
     * Get all price variables for use in the form.
     *
     * @since    1.0.0
     * @return   array    Price variables.
     */
    public function get_variables() {
        global $wpdb;
        $variables = array();

        // Get all variables from the database
        $table_name = $wpdb->prefix . 'pfb_variables';
        $results = $wpdb->get_results("SELECT * FROM $table_name ORDER BY category_id, name", ARRAY_A);

        if ($results) {
            foreach ($results as $result) {
                // Handle range-based variables
                if ($result['type'] === 'range') {
                    $ranges_table = $wpdb->prefix . 'pfb_variable_ranges';
                    $ranges = $wpdb->get_results(
                        $wpdb->prepare("SELECT * FROM $ranges_table WHERE variable_id = %d ORDER BY min_value", $result['id']),
                        ARRAY_A
                    );

                    $result['ranges'] = $ranges;
                    $result['value'] = isset($ranges[0]['price']) ? $ranges[0]['price'] : 0;
                }

                $variables[] = array(
                    'variable_key' => $result['variable_key'],
                    'name' => $result['name'],
                    'value' => isset($result['value']) ? $result['value'] : (isset($result['price']) ? $result['price'] : 0),
                    'type' => $result['type']
                );
            }
        }

        error_log('Retrieved ' . count($variables) . ' variables for form ' . $this->form['id']);
        return $variables;
    }
}
