<?php
/**
 * Conditional formula parser for the Price Form Builder plugin.
 *
 * @since      1.0.2
 */
class PFB_Conditional_Formula {

    /**
     * Variables for calculation.
     *
     * @since    1.0.2
     * @access   private
     * @var      array    $variables    Variables for calculation.
     */
    private $variables = array();

    /**
     * Field values from the form.
     *
     * @since    1.0.2
     * @access   private
     * @var      array    $field_values    Field values from the form.
     */
    private $field_values = array();

    /**
     * Debug mode.
     *
     * @since    1.0.2
     * @access   private
     * @var      boolean    $debug    Whether to output debug information.
     */
    private $debug = true;

    /**
     * Initialize the class.
     *
     * @since    1.0.2
     * @param    array    $variables     Price variables.
     * @param    array    $field_values  Field values from the form.
     * @param    boolean  $debug         Whether to output debug information.
     */
    public function __construct($variables = array(), $field_values = array(), $debug = true) {
        $this->variables = $variables;
        $this->field_values = $field_values;
        $this->debug = $debug;
    }

    /**
     * Log debug information.
     *
     * @since    1.0.2
     * @param    string    $message    Debug message.
     */
    private function log($message) {
        if ($this->debug) {
            error_log('[PFB Conditional Formula] ' . $message);
        }
    }

    /**
     * Evaluate a conditional formula.
     *
     * @since    1.0.2
     * @param    array     $formula    Conditional formula structure.
     * @return   float                 Result.
     */
    public function evaluate($formula) {
        $this->log('Evaluating conditional formula: ' . print_r($formula, true));

        if (!is_array($formula) || !isset($formula['condition']) || !isset($formula['then_formula']) || !isset($formula['else_formula'])) {
            $this->log('Invalid conditional formula structure');
            return 0;
        }

        // Evaluate the condition
        $condition_result = $this->evaluate_condition($formula['condition']);
        $this->log('Condition result: ' . ($condition_result ? 'true' : 'false'));

        // Based on the condition result, evaluate either the then or else formula
        if ($condition_result) {
            $this->log('Evaluating THEN formula: ' . $formula['then_formula']);
            return $this->evaluate_formula($formula['then_formula']);
        } else {
            $this->log('Evaluating ELSE formula: ' . $formula['else_formula']);
            return $this->evaluate_formula($formula['else_formula']);
        }
    }

    /**
     * Evaluate a condition.
     *
     * @since    1.0.2
     * @param    array     $condition    Condition structure.
     * @return   boolean                 Result.
     */
    private function evaluate_condition($condition) {
        $this->log('Evaluating condition: ' . print_r($condition, true));

        if (!is_array($condition)) {
            $this->log('Invalid condition structure');
            return false;
        }

        // If it's a complex condition with logic type and rules
        if (isset($condition['logic_type']) && isset($condition['rules']) && is_array($condition['rules'])) {
            return $this->evaluate_complex_condition($condition);
        }
        
        // If it's a simple condition with field, operator, and value
        if (isset($condition['field']) && isset($condition['operator']) && isset($condition['value'])) {
            return $this->evaluate_simple_condition($condition);
        }

        $this->log('Unrecognized condition structure');
        return false;
    }

    /**
     * Evaluate a complex condition with multiple rules.
     *
     * @since    1.0.2
     * @param    array     $condition    Complex condition structure.
     * @return   boolean                 Result.
     */
    private function evaluate_complex_condition($condition) {
        $logic_type = $condition['logic_type'];
        $rules = $condition['rules'];
        $result = ($logic_type === 'all'); // Default to true for 'all', false for 'any'

        foreach ($rules as $rule) {
            $rule_result = $this->evaluate_simple_condition($rule);
            
            if ($logic_type === 'all') {
                $result = $result && $rule_result;
                if (!$result) break; // Short-circuit for AND logic
            } else {
                $result = $result || $rule_result;
                if ($result) break; // Short-circuit for OR logic
            }
        }

        $this->log('Complex condition result: ' . ($result ? 'true' : 'false'));
        return $result;
    }

    /**
     * Evaluate a simple condition.
     *
     * @since    1.0.2
     * @param    array     $condition    Simple condition structure.
     * @return   boolean                 Result.
     */
    private function evaluate_simple_condition($condition) {
        $field = $condition['field'];
        $operator = $condition['operator'];
        $value = $condition['value'];

        // Get the field value
        $field_value = '';
        if (isset($this->field_values[$field])) {
            $field_value = $this->field_values[$field];
        } else {
            $this->log("Field {$field} not found in field values");
            return false;
        }

        $this->log("Comparing field {$field} value '{$field_value}' {$operator} '{$value}'");

        // Evaluate based on operator
        switch ($operator) {
            case 'is':
                return (string)$field_value === (string)$value;
                
            case 'is_not':
                return (string)$field_value !== (string)$value;
                
            case 'greater_than':
                return floatval($field_value) > floatval($value);
                
            case 'less_than':
                return floatval($field_value) < floatval($value);
                
            case 'contains':
                return strpos((string)$field_value, (string)$value) !== false;
                
            default:
                $this->log("Unknown operator: {$operator}");
                return false;
        }
    }

    /**
     * Evaluate a formula using the existing formula parser.
     *
     * @since    1.0.2
     * @param    string    $formula    Formula to evaluate.
     * @return   float                 Result.
     */
    private function evaluate_formula($formula) {
        // Use the existing formula parser
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-pfb-simple-calculator.php';
        $calculator = new PFB_Simple_Calculator($this->variables, $this->debug);
        
        try {
            $result = $calculator->calculate($formula, $this->field_values);
            $this->log("Formula '{$formula}' calculated result: {$result}");
            return $result;
        } catch (Exception $e) {
            $this->log('Formula calculation error: ' . $e->getMessage());
            return 0;
        }
    }
}
