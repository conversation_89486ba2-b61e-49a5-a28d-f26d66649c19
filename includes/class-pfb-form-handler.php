<?php
/**
 * Form Handler
 *
 * @since      1.0.0
 * @package    PFB
 * @subpackage PFB/includes
 */

/**
 * Form Handler class.
 *
 * This class handles saving and loading forms.
 *
 * @since      1.0.0
 * @package    PFB
 * @subpackage PFB/includes
 */
class PFB_Form_Handler {

    /**
     * Database tables
     */
    private $forms_table;
    
    /**
     * Field handler
     */
    private $field_handler;

    /**
     * Initialize the class
     */
    public function __construct() {
        global $wpdb;
        $this->forms_table = $wpdb->prefix . 'pfb_forms';
        $this->field_handler = new PFB_Form_Field_Handler();
        
        // Add AJAX handlers
        add_action('wp_ajax_pfb_save_form_v3', array($this, 'ajax_save_form'));
        add_action('wp_ajax_pfb_get_form_v3', array($this, 'ajax_get_form'));
    }

    /**
     * AJAX handler for saving forms
     */
    public function ajax_save_form() {
        try {
            error_log('PFB Form Handler: AJAX save form request received');
            
            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
                error_log('PFB Form Handler: Nonce verification failed');
                wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
                return;
            }
            
            // Check permissions
            if (!current_user_can('manage_options')) {
                error_log('PFB Form Handler: Permission check failed');
                wp_send_json_error(array('message' => __('You do not have permission to save forms.', 'price-form-builder')));
                return;
            }
            
            // Get form data
            $form_data = isset($_POST['form_data']) ? $_POST['form_data'] : null;
            
            if (!$form_data) {
                error_log('PFB Form Handler: No form data provided');
                wp_send_json_error(array('message' => __('No form data provided.', 'price-form-builder')));
                return;
            }
            
            // Save form
            $result = $this->save_form($form_data);
            
            if ($result['success']) {
                wp_send_json_success(array(
                    'message' => __('Form saved successfully.', 'price-form-builder'),
                    'form_id' => $result['form_id']
                ));
            } else {
                wp_send_json_error(array('message' => $result['message']));
            }
        } catch (Exception $e) {
            error_log('PFB Form Handler: Exception in ajax_save_form: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Error: ' . $e->getMessage()));
        }
    }
    
    /**
     * AJAX handler for getting forms
     */
    public function ajax_get_form() {
        try {
            error_log('PFB Form Handler: AJAX get form request received');
            
            // Check nonce
            if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'pfb_nonce')) {
                error_log('PFB Form Handler: Nonce verification failed');
                wp_send_json_error(array('message' => __('Security check failed.', 'price-form-builder')));
                return;
            }
            
            // Check permissions
            if (!current_user_can('manage_options')) {
                error_log('PFB Form Handler: Permission check failed');
                wp_send_json_error(array('message' => __('You do not have permission to get forms.', 'price-form-builder')));
                return;
            }
            
            // Get form ID
            $form_id = isset($_GET['form_id']) ? intval($_GET['form_id']) : 0;
            
            if (!$form_id) {
                error_log('PFB Form Handler: No form ID provided');
                wp_send_json_error(array('message' => __('No form ID provided.', 'price-form-builder')));
                return;
            }
            
            // Get form
            $form = $this->get_form($form_id);
            
            if ($form) {
                wp_send_json_success(array('form' => $form));
            } else {
                wp_send_json_error(array('message' => __('Form not found.', 'price-form-builder')));
            }
        } catch (Exception $e) {
            error_log('PFB Form Handler: Exception in ajax_get_form: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Error: ' . $e->getMessage()));
        }
    }
    
    /**
     * Save form to database
     *
     * @param array $form_data Form data
     * @return array Result with success status, message, and form ID
     */
    public function save_form($form_data) {
        global $wpdb;
        
        error_log('PFB Form Handler: Starting form save process');
        
        // Start transaction
        $wpdb->query('START TRANSACTION');
        
        try {
            // Validate form data
            if (!isset($form_data['title']) || empty($form_data['title'])) {
                throw new Exception(__('Form title is required.', 'price-form-builder'));
            }
            
            // Prepare form data
            $form = array(
                'title'       => sanitize_text_field($form_data['title']),
                'description' => isset($form_data['description']) ? sanitize_textarea_field($form_data['description']) : '',
                'status'      => isset($form_data['status']) ? sanitize_text_field($form_data['status']) : 'publish'
            );
            
            // Check if we're updating an existing form
            if (isset($form_data['id']) && !empty($form_data['id'])) {
                $form_id = intval($form_data['id']);
                error_log('PFB Form Handler: Updating existing form with ID: ' . $form_id);
                
                // Check if form exists
                $existing_form = $wpdb->get_var($wpdb->prepare("SELECT id FROM {$this->forms_table} WHERE id = %d", $form_id));
                
                if (!$existing_form) {
                    throw new Exception(__('Form with ID ' . $form_id . ' does not exist.', 'price-form-builder'));
                }
                
                // Update form
                $result = $wpdb->update(
                    $this->forms_table,
                    $form,
                    array('id' => $form_id)
                );
                
                if ($result === false) {
                    throw new Exception(__('Failed to update form: ', 'price-form-builder') . $wpdb->last_error);
                }
            } else {
                error_log('PFB Form Handler: Creating new form');
                
                // Insert new form
                $result = $wpdb->insert($this->forms_table, $form);
                
                if ($result === false) {
                    throw new Exception(__('Failed to insert form: ', 'price-form-builder') . $wpdb->last_error);
                }
                
                $form_id = $wpdb->insert_id;
                error_log('PFB Form Handler: New form created with ID: ' . $form_id);
            }
            
            // Save form settings
            if (isset($form_data['settings']) && is_array($form_data['settings'])) {
                error_log('PFB Form Handler: Saving form settings');
                update_option('pfb_form_settings_' . $form_id, $this->sanitize_settings($form_data['settings']));
            }
            
            // Save form fields
            if (isset($form_data['fields']) && is_array($form_data['fields'])) {
                error_log('PFB Form Handler: Saving form fields');
                $this->field_handler->save_fields($form_id, $form_data['fields']);
            }
            
            // Commit transaction
            $wpdb->query('COMMIT');
            
            return array(
                'success' => true,
                'message' => __('Form saved successfully.', 'price-form-builder'),
                'form_id' => $form_id
            );
        } catch (Exception $e) {
            // Rollback transaction
            $wpdb->query('ROLLBACK');
            
            error_log('PFB Form Handler: Exception saving form: ' . $e->getMessage());
            
            return array(
                'success' => false,
                'message' => $e->getMessage()
            );
        }
    }
    
    /**
     * Get form from database
     *
     * @param int $form_id Form ID
     * @return array|false Form data or false if not found
     */
    public function get_form($form_id) {
        global $wpdb;
        
        error_log('PFB Form Handler: Getting form with ID: ' . $form_id);
        
        try {
            // Get form
            $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$this->forms_table} WHERE id = %d", $form_id), ARRAY_A);
            
            if (!$form) {
                error_log('PFB Form Handler: Form not found');
                return false;
            }
            
            // Get form settings
            $settings = get_option('pfb_form_settings_' . $form_id, array());
            $form['settings'] = $settings;
            
            // Get form fields
            $fields_table = $wpdb->prefix . 'pfb_form_fields';
            $fields = $wpdb->get_results($wpdb->prepare("
                SELECT * FROM {$fields_table} 
                WHERE form_id = %d 
                ORDER BY field_order ASC
            ", $form_id), ARRAY_A);
            
            // Process fields
            $processed_fields = array();
            
            if ($fields) {
                foreach ($fields as $field) {
                    $processed_fields[] = $this->process_field($field);
                }
            }
            
            $form['fields'] = $processed_fields;
            
            return $form;
            
        } catch (Exception $e) {
            error_log('PFB Form Handler: Exception getting form: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Process field from database
     *
     * @param array $field Field data from database
     * @return array Processed field
     */
    private function process_field($field) {
        // Create base field
        $processed_field = array(
            'id' => $field['id'],
            'type' => $field['field_type'],
            'label' => $field['field_label'],
            'name' => $field['field_name'],
            'required' => (bool)$field['field_required'],
            'width' => $field['field_width']
        );
        
        // Process options
        if (!empty($field['field_options'])) {
            $options = maybe_unserialize($field['field_options']);
            if (is_array($options)) {
                $processed_field['options'] = $options;
                
                // Handle hidden field properties
                if (isset($options['is_hidden']) && $options['is_hidden']) {
                    $processed_field['hidden'] = true;
                    
                    if (isset($options['variable'])) {
                        $processed_field['variable'] = $options['variable'];
                    }
                    
                    if (isset($options['default_value'])) {
                        $processed_field['default_value'] = $options['default_value'];
                    }
                }
            }
        }
        
        // Process conditional logic
        if (!empty($field['conditional_logic'])) {
            $conditional_logic = new PFB_Conditional_Logic();
            $processed_field['conditional_logic'] = $conditional_logic->parse_from_db($field['conditional_logic']);
        }
        
        return $processed_field;
    }
    
    /**
     * Sanitize form settings
     *
     * @param array $settings Form settings
     * @return array Sanitized settings
     */
    private function sanitize_settings($settings) {
        $sanitized = array();
        
        if (isset($settings['template'])) {
            $sanitized['template'] = sanitize_text_field($settings['template']);
        }
        
        if (isset($settings['show_currency_selector'])) {
            $sanitized['show_currency_selector'] = sanitize_text_field($settings['show_currency_selector']);
        }
        
        if (isset($settings['submit_button_text'])) {
            $sanitized['submit_button_text'] = sanitize_text_field($settings['submit_button_text']);
        }
        
        return $sanitized;
    }
}
