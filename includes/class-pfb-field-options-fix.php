<?php
/**
 * Field Options Fix
 *
 * This file contains functions to fix field options saving issues.
 *
 * @since      1.0.0
 * @package    PFB
 * @subpackage PFB/includes
 */

/**
 * Fix field options saving issues.
 */
class PFB_Field_Options_Fix {

    /**
     * Initialize the fix.
     */
    public static function init() {
        // Add filter to modify field options before saving
        add_filter('pfb_before_save_form', array(__CLASS__, 'fix_field_options'), 10, 1);
        
        // Add debug endpoint
        add_action('wp_ajax_pfb_debug_field_options', array(__CLASS__, 'debug_field_options'));
    }
    
    /**
     * Fix field options before saving.
     *
     * @param array $form_data Form data.
     * @return array Modified form data.
     */
    public static function fix_field_options($form_data) {
        // Check if we have fields
        if (!isset($form_data['fields']) || !is_array($form_data['fields'])) {
            return $form_data;
        }
        
        // Log the form data before fixing
        error_log('PFB Field Options Fix: Form data before fixing: ' . print_r($form_data, true));
        
        // Process each field
        foreach ($form_data['fields'] as $key => $field) {
            // Make sure options is an array
            if (!isset($field['options']) || !is_array($field['options'])) {
                $form_data['fields'][$key]['options'] = array();
            }
            
            // Process field based on type
            switch ($field['type']) {
                case 'select':
                case 'radio':
                case 'checkbox':
                    $form_data['fields'][$key] = self::fix_choice_field($field);
                    break;
                    
                case 'number':
                    $form_data['fields'][$key] = self::fix_number_field($field);
                    break;
                    
                case 'slider':
                    $form_data['fields'][$key] = self::fix_slider_field($field);
                    break;
                    
                case 'price':
                case 'total':
                    $form_data['fields'][$key] = self::fix_price_field($field);
                    break;
                    
                case 'subtotal':
                    $form_data['fields'][$key] = self::fix_subtotal_field($field);
                    break;
                    
                case 'text':
                    $form_data['fields'][$key] = self::fix_text_field($field);
                    break;
            }
            
            // Fix common options
            $form_data['fields'][$key] = self::fix_common_options($form_data['fields'][$key]);
            
            // Fix conditional logic
            if (isset($field['conditional_logic'])) {
                $form_data['fields'][$key]['conditional_logic'] = self::fix_conditional_logic($field['conditional_logic']);
            }
            
            // Log the fixed field
            error_log('PFB Field Options Fix: Fixed field: ' . print_r($form_data['fields'][$key], true));
        }
        
        // Log the form data after fixing
        error_log('PFB Field Options Fix: Form data after fixing: ' . print_r($form_data, true));
        
        return $form_data;
    }
    
    /**
     * Fix choice field options (select, radio, checkbox).
     *
     * @param array $field Field data.
     * @return array Modified field data.
     */
    public static function fix_choice_field($field) {
        // Make sure options is an array
        if (!isset($field['options'])) {
            $field['options'] = array();
        }
        
        // Make sure items is an array
        if (!isset($field['options']['items']) || !is_array($field['options']['items'])) {
            // Try to get items from choices
            if (isset($field['options']['choices']) && is_array($field['options']['choices'])) {
                $field['options']['items'] = $field['options']['choices'];
                unset($field['options']['choices']);
            } else {
                // Create default items
                $field['options']['items'] = array(
                    array(
                        'label' => 'Option 1',
                        'value' => 'option_1',
                        'variable' => ''
                    )
                );
            }
        }
        
        // Make sure each item has label, value, and variable
        foreach ($field['options']['items'] as $key => $item) {
            if (!isset($item['label']) || empty($item['label'])) {
                $field['options']['items'][$key]['label'] = 'Option ' . ($key + 1);
            }
            
            if (!isset($item['value']) || empty($item['value'])) {
                $field['options']['items'][$key]['value'] = 'option_' . ($key + 1);
            }
            
            if (!isset($item['variable'])) {
                $field['options']['items'][$key]['variable'] = '';
            }
        }
        
        return $field;
    }
    
    /**
     * Fix number field options.
     *
     * @param array $field Field data.
     * @return array Modified field data.
     */
    public static function fix_number_field($field) {
        // Make sure min, max, and step are set
        if (!isset($field['options']['min']) || !is_numeric($field['options']['min'])) {
            $field['options']['min'] = '0';
        }
        
        if (!isset($field['options']['max']) || !is_numeric($field['options']['max'])) {
            $field['options']['max'] = '100';
        }
        
        if (!isset($field['options']['step']) || !is_numeric($field['options']['step'])) {
            $field['options']['step'] = '1';
        }
        
        return $field;
    }
    
    /**
     * Fix slider field options.
     *
     * @param array $field Field data.
     * @return array Modified field data.
     */
    public static function fix_slider_field($field) {
        // Make sure min, max, step, and default are set
        if (!isset($field['options']['min']) || !is_numeric($field['options']['min'])) {
            $field['options']['min'] = 0;
        }
        
        if (!isset($field['options']['max']) || !is_numeric($field['options']['max'])) {
            $field['options']['max'] = 100;
        }
        
        if (!isset($field['options']['step']) || !is_numeric($field['options']['step'])) {
            $field['options']['step'] = 1;
        }
        
        if (!isset($field['options']['default']) || !is_numeric($field['options']['default'])) {
            $field['options']['default'] = 50;
        }
        
        return $field;
    }
    
    /**
     * Fix price/total field options.
     *
     * @param array $field Field data.
     * @return array Modified field data.
     */
    public static function fix_price_field($field) {
        // Make sure formula is set
        if (!isset($field['options']['formula'])) {
            $field['options']['formula'] = '';
        }
        
        // For price fields, make sure price is set
        if ($field['type'] === 'price' && !isset($field['options']['price'])) {
            $field['options']['price'] = '0';
        }
        
        return $field;
    }
    
    /**
     * Fix subtotal field options.
     *
     * @param array $field Field data.
     * @return array Modified field data.
     */
    public static function fix_subtotal_field($field) {
        // Make sure lines is an array
        if (!isset($field['options']['lines']) || !is_array($field['options']['lines'])) {
            $field['options']['lines'] = array(
                array(
                    'label' => 'Line 1',
                    'formula' => ''
                )
            );
        }
        
        // Make sure each line has label and formula
        foreach ($field['options']['lines'] as $key => $line) {
            if (!isset($line['label']) || empty($line['label'])) {
                $field['options']['lines'][$key]['label'] = 'Line ' . ($key + 1);
            }
            
            if (!isset($line['formula'])) {
                $field['options']['lines'][$key]['formula'] = '';
            }
        }
        
        // Make sure empty_value is set
        if (!isset($field['options']['empty_value'])) {
            $field['options']['empty_value'] = '---';
        }
        
        return $field;
    }
    
    /**
     * Fix text field options.
     *
     * @param array $field Field data.
     * @return array Modified field data.
     */
    public static function fix_text_field($field) {
        // Handle hidden field properties
        if (isset($field['hidden']) && $field['hidden']) {
            $field['options']['is_hidden'] = true;
            
            // Copy variable and default_value to options
            if (isset($field['variable'])) {
                $field['options']['variable'] = $field['variable'];
            }
            
            if (isset($field['default_value'])) {
                $field['options']['default_value'] = $field['default_value'];
            }
        }
        
        return $field;
    }
    
    /**
     * Fix common field options.
     *
     * @param array $field Field data.
     * @return array Modified field data.
     */
    public static function fix_common_options($field) {
        // Make sure placeholder, default, and description are set
        if (isset($field['placeholder']) && !isset($field['options']['placeholder'])) {
            $field['options']['placeholder'] = $field['placeholder'];
        }
        
        if (isset($field['default']) && !isset($field['options']['default'])) {
            $field['options']['default'] = $field['default'];
        }
        
        if (isset($field['description']) && !isset($field['options']['description'])) {
            $field['options']['description'] = $field['description'];
        }
        
        return $field;
    }
    
    /**
     * Fix conditional logic.
     *
     * @param array $conditional_logic Conditional logic data.
     * @return array Modified conditional logic data.
     */
    public static function fix_conditional_logic($conditional_logic) {
        // Make sure conditional_logic is an array
        if (!is_array($conditional_logic)) {
            return array(
                'enabled' => false,
                'logic_type' => 'all',
                'rules' => array()
            );
        }
        
        // Make sure enabled is set
        if (!isset($conditional_logic['enabled'])) {
            $conditional_logic['enabled'] = 
                isset($conditional_logic['rules']) && 
                is_array($conditional_logic['rules']) && 
                count($conditional_logic['rules']) > 0;
        }
        
        // Make sure logic_type is set
        if (!isset($conditional_logic['logic_type']) || empty($conditional_logic['logic_type'])) {
            $conditional_logic['logic_type'] = 'all';
        }
        
        // Make sure rules is an array
        if (!isset($conditional_logic['rules']) || !is_array($conditional_logic['rules'])) {
            $conditional_logic['rules'] = array();
        }
        
        // Make sure each rule has field, operator, and value
        foreach ($conditional_logic['rules'] as $key => $rule) {
            if (!isset($rule['field']) || empty($rule['field']) || $rule['field'] === '0' || $rule['field'] === 0) {
                // If we have field_name but no valid field ID, try to generate one
                if (isset($rule['field_name']) && !empty($rule['field_name'])) {
                    $conditional_logic['rules'][$key]['field'] = 'field_' . sanitize_key($rule['field_name']);
                } else {
                    // Remove rule with invalid field
                    unset($conditional_logic['rules'][$key]);
                    continue;
                }
            }
            
            if (!isset($rule['operator']) || empty($rule['operator'])) {
                $conditional_logic['rules'][$key]['operator'] = 'is';
            }
            
            if (!isset($rule['value'])) {
                $conditional_logic['rules'][$key]['value'] = '';
            }
            
            // Make sure field_name is set
            if (!isset($rule['field_name']) || empty($rule['field_name'])) {
                $conditional_logic['rules'][$key]['field_name'] = 'field_' . substr(sanitize_key($rule['field']), 0, 10);
            }
        }
        
        // Reindex rules array
        $conditional_logic['rules'] = array_values($conditional_logic['rules']);
        
        return $conditional_logic;
    }
    
    /**
     * Debug field options.
     */
    public static function debug_field_options() {
        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }
        
        // Get form ID
        $form_id = isset($_GET['form_id']) ? intval($_GET['form_id']) : 0;
        
        if (!$form_id) {
            wp_send_json_error('Form ID is required');
        }
        
        // Get fields from database
        global $wpdb;
        $fields_table = $wpdb->prefix . 'pfb_form_fields';
        $fields = $wpdb->get_results($wpdb->prepare("SELECT * FROM {$fields_table} WHERE form_id = %d", $form_id), ARRAY_A);
        
        if (empty($fields)) {
            wp_send_json_error('No fields found for this form');
        }
        
        // Process fields
        $processed_fields = array();
        foreach ($fields as $field) {
            $options = maybe_unserialize($field['field_options']);
            $conditional_logic = isset($field['conditional_logic']) ? json_decode($field['conditional_logic'], true) : null;
            
            $processed_fields[] = array(
                'id' => $field['id'],
                'type' => $field['field_type'],
                'label' => $field['field_label'],
                'name' => $field['field_name'],
                'width' => $field['field_width'],
                'options' => $options,
                'conditional_logic' => $conditional_logic
            );
        }
        
        wp_send_json_success($processed_fields);
    }
}

// Initialize the fix
PFB_Field_Options_Fix::init();
