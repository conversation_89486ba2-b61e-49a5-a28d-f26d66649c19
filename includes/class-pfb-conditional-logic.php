<?php
/**
 * Conditional Logic Handler
 *
 * @since      1.0.0
 * @package    PFB
 * @subpackage PFB/includes
 */

/**
 * Conditional Logic Handler class.
 *
 * This class handles all conditional logic operations for form fields.
 *
 * @since      1.0.0
 * @package    PFB
 * @subpackage PFB/includes
 */
class PFB_Conditional_Logic {

    /**
     * Initialize the class
     */
    public function __construct() {
        // Nothing to initialize
    }

    /**
     * Prepare conditional logic for saving to database
     *
     * @param array $conditional_logic Conditional logic data from form submission
     * @return string JSON encoded conditional logic
     */
    public function prepare_for_db($conditional_logic) {
        // Start with default structure
        $prepared_logic = array(
            'enabled' => false,
            'logic_type' => 'all', // 'all' or 'any'
            'rules' => array()
        );
        
        // If no conditional logic provided, return default structure
        if (empty($conditional_logic) || !is_array($conditional_logic)) {
            error_log('PFB Conditional Logic: Empty or invalid conditional logic provided');
            return json_encode($prepared_logic);
        }
        
        error_log('PFB Conditional Logic: Processing conditional logic: ' . print_r($conditional_logic, true));
        
        // Set enabled status
        $prepared_logic['enabled'] = isset($conditional_logic['enabled']) ? 
            (bool)$conditional_logic['enabled'] : false;
            
        // Set logic type (all/any)
        if (isset($conditional_logic['logic_type']) && 
            in_array($conditional_logic['logic_type'], array('all', 'any'))) {
            $prepared_logic['logic_type'] = $conditional_logic['logic_type'];
        }
        
        // Process rules
        if (isset($conditional_logic['rules']) && is_array($conditional_logic['rules'])) {
            foreach ($conditional_logic['rules'] as $rule) {
                // Skip invalid rules
                if (!isset($rule['field']) || empty($rule['field']) || 
                    !isset($rule['operator']) || empty($rule['operator'])) {
                    continue;
                }
                
                // Prepare rule
                $prepared_rule = array(
                    'field' => sanitize_text_field($rule['field']),
                    'operator' => sanitize_text_field($rule['operator']),
                    'value' => isset($rule['value']) ? sanitize_text_field($rule['value']) : ''
                );
                
                // Add field name if available
                if (isset($rule['field_name']) && !empty($rule['field_name'])) {
                    $prepared_rule['field_name'] = sanitize_text_field($rule['field_name']);
                }
                
                $prepared_logic['rules'][] = $prepared_rule;
            }
        }
        
        // If enabled but no rules, disable it
        if ($prepared_logic['enabled'] && empty($prepared_logic['rules'])) {
            $prepared_logic['enabled'] = false;
            error_log('PFB Conditional Logic: Disabled conditional logic because no valid rules were found');
        }
        
        error_log('PFB Conditional Logic: Prepared logic: ' . print_r($prepared_logic, true));
        
        // Encode as JSON
        return json_encode($prepared_logic);
    }
    
    /**
     * Parse conditional logic from database
     *
     * @param string $db_conditional_logic JSON encoded conditional logic from database
     * @return array Parsed conditional logic
     */
    public function parse_from_db($db_conditional_logic) {
        // Default structure
        $parsed_logic = array(
            'enabled' => false,
            'logic_type' => 'all',
            'rules' => array()
        );
        
        // If empty, return default
        if (empty($db_conditional_logic)) {
            return $parsed_logic;
        }
        
        try {
            // Decode JSON
            $decoded = json_decode($db_conditional_logic, true);
            
            // Check if decode was successful
            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log('PFB Conditional Logic: Failed to decode JSON: ' . json_last_error_msg());
                error_log('PFB Conditional Logic: Original data: ' . $db_conditional_logic);
                return $parsed_logic;
            }
            
            // Validate structure
            if (!is_array($decoded)) {
                error_log('PFB Conditional Logic: Decoded data is not an array');
                return $parsed_logic;
            }
            
            // Set enabled status
            $parsed_logic['enabled'] = isset($decoded['enabled']) ? (bool)$decoded['enabled'] : false;
            
            // Set logic type
            if (isset($decoded['logic_type']) && in_array($decoded['logic_type'], array('all', 'any'))) {
                $parsed_logic['logic_type'] = $decoded['logic_type'];
            }
            
            // Process rules
            if (isset($decoded['rules']) && is_array($decoded['rules'])) {
                foreach ($decoded['rules'] as $rule) {
                    // Skip invalid rules
                    if (!isset($rule['field']) || empty($rule['field']) || 
                        !isset($rule['operator']) || empty($rule['operator'])) {
                        continue;
                    }
                    
                    // Add rule
                    $parsed_rule = array(
                        'field' => $rule['field'],
                        'operator' => $rule['operator'],
                        'value' => isset($rule['value']) ? $rule['value'] : ''
                    );
                    
                    // Add field name if available
                    if (isset($rule['field_name']) && !empty($rule['field_name'])) {
                        $parsed_rule['field_name'] = $rule['field_name'];
                    }
                    
                    $parsed_logic['rules'][] = $parsed_rule;
                }
            }
            
            // If enabled but no rules, disable it
            if ($parsed_logic['enabled'] && empty($parsed_logic['rules'])) {
                $parsed_logic['enabled'] = false;
            }
            
            return $parsed_logic;
            
        } catch (Exception $e) {
            error_log('PFB Conditional Logic: Exception parsing conditional logic: ' . $e->getMessage());
            return $parsed_logic;
        }
    }
    
    /**
     * Evaluate conditional logic for a field
     *
     * @param array $conditional_logic Conditional logic to evaluate
     * @param array $field_values Current field values
     * @return bool Whether the field should be shown
     */
    public function evaluate($conditional_logic, $field_values) {
        // If not enabled, always show the field
        if (!isset($conditional_logic['enabled']) || !$conditional_logic['enabled']) {
            return true;
        }
        
        // If no rules, always show the field
        if (!isset($conditional_logic['rules']) || empty($conditional_logic['rules'])) {
            return true;
        }
        
        // Get logic type
        $logic_type = isset($conditional_logic['logic_type']) ? $conditional_logic['logic_type'] : 'all';
        
        // Evaluate each rule
        $results = array();
        
        foreach ($conditional_logic['rules'] as $rule) {
            // Skip invalid rules
            if (!isset($rule['field']) || !isset($rule['operator'])) {
                continue;
            }
            
            // Get field value
            $field_id = $rule['field'];
            $field_value = isset($field_values[$field_id]) ? $field_values[$field_id] : '';
            
            // Evaluate rule
            $results[] = $this->evaluate_rule($rule, $field_value);
        }
        
        // If no valid rules were evaluated, show the field
        if (empty($results)) {
            return true;
        }
        
        // Determine final result based on logic type
        if ($logic_type === 'all') {
            // All rules must be true
            return !in_array(false, $results);
        } else {
            // Any rule can be true
            return in_array(true, $results);
        }
    }
    
    /**
     * Evaluate a single conditional logic rule
     *
     * @param array $rule Rule to evaluate
     * @param mixed $field_value Current field value
     * @return bool Whether the rule is satisfied
     */
    private function evaluate_rule($rule, $field_value) {
        $operator = $rule['operator'];
        $value = isset($rule['value']) ? $rule['value'] : '';
        
        switch ($operator) {
            case 'is':
                return $field_value == $value;
                
            case 'is_not':
                return $field_value != $value;
                
            case 'greater_than':
                return floatval($field_value) > floatval($value);
                
            case 'less_than':
                return floatval($field_value) < floatval($value);
                
            case 'contains':
                return strpos($field_value, $value) !== false;
                
            case 'starts_with':
                return strpos($field_value, $value) === 0;
                
            case 'ends_with':
                return substr($field_value, -strlen($value)) === $value;
                
            case 'is_empty':
                return empty($field_value);
                
            case 'is_not_empty':
                return !empty($field_value);
                
            default:
                return false;
        }
    }
}
