<?php
/**
 * Database operations for the plugin.
 *
 * @since      1.0.0
 */
class PFB_DB {

    /**
     * Get forms from the database.
     *
     * @since    1.0.0
     * @param    array    $args    Query arguments.
     * @return   array              Array of forms.
     */
    public static function get_forms($args = array()) {
        global $wpdb;

        $defaults = array(
            'number'     => 20,
            'offset'     => 0,
            'orderby'    => 'id',
            'order'      => 'DESC',
            'status'     => 'publish'
        );

        $args = wp_parse_args($args, $defaults);

        $table_name = $wpdb->prefix . 'pfb_forms';

        $sql = "SELECT * FROM $table_name";

        if (!empty($args['status'])) {
            $sql .= $wpdb->prepare(" WHERE status = %s", $args['status']);
        }

        $sql .= " ORDER BY " . sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']);

        $sql .= $wpdb->prepare(" LIMIT %d, %d", $args['offset'], $args['number']);

        $forms = $wpdb->get_results($sql, ARRAY_A);

        return $forms;
    }

    /**
     * Get a single form from the database.
     *
     * @since    1.0.0
     * @param    int      $id      Form ID.
     * @return   array             Form data.
     */
    public static function get_form($id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_forms';

        $form = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM $table_name WHERE id = %d", $id),
            ARRAY_A
        );

        if (!$form) {
            return null;
        }

        // Get form fields
        $form['fields'] = self::get_form_fields($id);

        return $form;
    }

    /**
     * Get form fields from the database.
     *
     * @since    1.0.0
     * @param    int      $form_id    Form ID.
     * @return   array                Array of form fields.
     */
    public static function get_form_fields($form_id) {
        global $wpdb;

        error_log('PFB_DB: Getting form fields for form ID: ' . $form_id);

        $table_name = $wpdb->prefix . 'pfb_form_fields';

        try {
            // Get fields
            $query = $wpdb->prepare("SELECT * FROM $table_name WHERE form_id = %d ORDER BY field_order ASC", $form_id);
            error_log('PFB_DB: Fields query: ' . $query);

            $fields = $wpdb->get_results($query, ARRAY_A);

            error_log('PFB_DB: Found ' . count($fields) . ' fields for form ID: ' . $form_id);

            // Check if field_width column exists
            $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'field_width'");
            error_log('PFB_DB: field_width column exists: ' . (empty($column_exists) ? 'NO' : 'YES'));

            // Check if we have any fields
            if (!empty($fields) && is_array($fields)) {
                foreach ($fields as &$field) {
                    error_log('PFB_DB: Processing field ID: ' . $field['id'] . ', Type: ' . $field['field_type']);

                    // Ensure field_width is set
                    if (!isset($field['field_width']) || empty($field['field_width'])) {
                        // Default to 100% if not set
                        $field['field_width'] = '100';
                        error_log('PFB_DB: Set default field_width to 100 for field ID: ' . $field['id']);
                    }

                    if (!empty($field['field_options'])) {
                        // Try to unserialize the field options
                        $original_options = $field['field_options'];
                        $field['field_options'] = maybe_unserialize($field['field_options']);

                        // Check if unserialization failed
                        if (is_serialized($original_options) && !is_array($field['field_options']) && !is_object($field['field_options'])) {
                            error_log('PFB_DB: Failed to unserialize field options for field ID: ' . $field['id']);
                            error_log('PFB_DB: Original serialized data: ' . $original_options);
                            // Set default empty array to prevent errors
                            $field['field_options'] = array();
                        }

                        error_log('PFB_DB: Unserialized field options for field ' . $field['id'] . ' (' . $field['field_type'] . '): ' . print_r($field['field_options'], true));

                        // Check if width is in field_options and field_width is not set
                        if (isset($field['field_options']['width']) && $field['field_width'] === '100') {
                            $field['field_width'] = $field['field_options']['width'];
                            error_log('PFB_DB: Using width from field_options for field ' . $field['id'] . ': ' . $field['field_width']);

                            // Remove width from options to avoid duplication
                            unset($field['field_options']['width']);
                        }

                        // For subtotal fields, ensure the lines array exists
                        if ($field['field_type'] === 'subtotal') {
                            if (!isset($field['field_options']['lines']) || !is_array($field['field_options']['lines'])) {
                                $field['field_options']['lines'] = array(
                                    array(
                                        'label' => 'Line 1',
                                        'formula' => ''
                                    )
                                );
                                error_log('PFB_DB: Added default lines to subtotal field options');
                            }

                            // Make sure empty_value is set
                            if (!isset($field['field_options']['empty_value'])) {
                                $field['field_options']['empty_value'] = '---';
                            }
                        }
                    } else {
                        // Ensure field_options exists
                        $field['field_options'] = array();
                        error_log('PFB_DB: Created empty field_options for field ID: ' . $field['id']);
                    }

                    // Handle conditional logic
                    if (!empty($field['conditional_logic'])) {
                        error_log('PFB_DB: Processing conditional logic for field ID: ' . $field['id']);

                        // Try to unserialize the conditional logic
                        $original_logic = $field['conditional_logic'];
                        $field['conditional_logic'] = maybe_unserialize($field['conditional_logic']);

                        // If it's a JSON string, try to decode it
                        if (is_string($field['conditional_logic']) && strlen($field['conditional_logic']) > 0) {
                            try {
                                $decoded = json_decode($field['conditional_logic'], true);
                                if (json_last_error() === JSON_ERROR_NONE) {
                                    $field['conditional_logic'] = $decoded;
                                    error_log('PFB_DB: Decoded JSON conditional logic for field ' . $field['id']);
                                } else {
                                    error_log('PFB_DB: Failed to decode JSON conditional logic: ' . json_last_error_msg());
                                    error_log('PFB_DB: Original JSON data: ' . $field['conditional_logic']);
                                    // Set default conditional logic
                                    $field['conditional_logic'] = array(
                                        'enabled' => false,
                                        'logic_type' => 'all',
                                        'rules' => array()
                                    );
                                }
                            } catch (Exception $e) {
                                error_log('PFB_DB: Exception decoding conditional logic: ' . $e->getMessage());
                                // Set default conditional logic
                                $field['conditional_logic'] = array(
                                    'enabled' => false,
                                    'logic_type' => 'all',
                                    'rules' => array()
                                );
                            }
                        }

                        // Ensure conditional logic is an array
                        if (!is_array($field['conditional_logic'])) {
                            error_log('PFB_DB: Conditional logic is not an array after processing for field ID: ' . $field['id']);
                            $field['conditional_logic'] = array(
                                'enabled' => false,
                                'logic_type' => 'all',
                                'rules' => array()
                            );
                        }

                        // Ensure required properties exist
                        if (!isset($field['conditional_logic']['enabled'])) {
                            $field['conditional_logic']['enabled'] = false;
                        }

                        if (!isset($field['conditional_logic']['logic_type'])) {
                            $field['conditional_logic']['logic_type'] = 'all';
                        }

                        if (!isset($field['conditional_logic']['rules']) || !is_array($field['conditional_logic']['rules'])) {
                            $field['conditional_logic']['rules'] = array();
                        }

                        error_log('PFB_DB: Processed conditional logic for field ' . $field['id'] . ': ' . print_r($field['conditional_logic'], true));
                    } else {
                        // Create empty conditional logic structure
                        $field['conditional_logic'] = array(
                            'enabled' => false,
                            'logic_type' => 'all',
                            'rules' => array()
                        );
                        error_log('PFB_DB: Created empty conditional logic for field ' . $field['id']);
                    }
                }
            } else {
                error_log('PFB_DB: No fields found for form ID: ' . $form_id);
                $fields = array();
            }

            error_log('PFB_DB: Successfully processed all fields for form ID: ' . $form_id);
            return $fields;

        } catch (Exception $e) {
            error_log('PFB_DB: Exception getting form fields: ' . $e->getMessage());
            error_log('PFB_DB: Exception trace: ' . $e->getTraceAsString());
            return array();
        }
    }

    /**
     * Save a form to the database.
     *
     * @since    1.0.0
     * @param    array    $form_data    Form data.
     * @return   int                     Form ID.
     */
    public static function save_form($form_data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_forms';

        $form = array(
            'title'       => sanitize_text_field($form_data['title']),
            'description' => sanitize_textarea_field($form_data['description']),
            'status'      => sanitize_text_field($form_data['status'])
        );

        if (isset($form_data['id']) && !empty($form_data['id'])) {
            // Update existing form
            $wpdb->update(
                $table_name,
                $form,
                array('id' => $form_data['id'])
            );

            $form_id = $form_data['id'];
        } else {
            // Insert new form
            $wpdb->insert($table_name, $form);
            $form_id = $wpdb->insert_id;
        }

        // Save form fields
        if (isset($form_data['fields']) && is_array($form_data['fields'])) {
            self::save_form_fields($form_id, $form_data['fields']);
        }

        return $form_id;
    }

    /**
     * Save form fields to the database.
     *
     * @since    1.0.0
     * @param    int      $form_id    Form ID.
     * @param    array    $fields     Form fields.
     */
    public static function save_form_fields($form_id, $fields) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_form_fields';

        // Debug: Log the fields being saved
        error_log('PFB: Saving fields for form ' . $form_id . ': ' . print_r($fields, true));

        // Debug: Check if field_width column exists
        $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'field_width'");
        error_log('PFB: field_width column exists: ' . (empty($column_exists) ? 'NO' : 'YES'));

        // Delete existing fields - add debug log
        error_log('PFB: Deleting existing fields for form ' . $form_id);
        $delete_result = $wpdb->delete($table_name, array('form_id' => $form_id));
        error_log('PFB: Delete result: ' . ($delete_result !== false ? $delete_result : 'false'));

        // Insert new fields
        foreach ($fields as $order => $field) {
            // Debug: Log each field's options before serialization
            if (isset($field['options']) && !empty($field['options'])) {
                error_log('Field options before serialization for field type ' . $field['type'] . ': ' . print_r($field['options'], true));
            }

            // If this is a text field and it's hidden, add that to the options
            if ($field['type'] === 'text' && isset($field['hidden']) && $field['hidden']) {
                if (!isset($field['options'])) {
                    $field['options'] = array();
                }
                $field['options']['is_hidden'] = true;

                // Debug log
                error_log('Setting hidden field option for ' . $field['name'] . ': ' . print_r($field['options'], true));
            }

            // Make sure options is an array
            if (!isset($field['options'])) {
                $field['options'] = array();
            }

            // Store width separately (will be saved in field_width column)
            $field_width = isset($field['width']) ? $field['width'] : '100';

            // Make sure the field width is properly formatted (remove % if present)
            $field_width = str_replace('%', '', $field_width);

            // Ensure the field width is a valid numeric value
            if (!is_numeric($field_width)) {
                $field_width = '100';
            }

            // For subtotal fields, make sure lines is an array
            if ($field['type'] === 'subtotal') {
                if (isset($field['options']['lines']) && is_array($field['options']['lines'])) {
                    error_log('Subtotal field lines before serialization: ' . print_r($field['options']['lines'], true));
                } else {
                    // If lines is not set or not an array, initialize it
                    $field['options']['lines'] = array(
                        array(
                            'label' => 'Line 1',
                            'formula' => ''
                        )
                    );
                    error_log('Created default lines for subtotal field');
                }

                // Make sure empty_value is set
                if (!isset($field['options']['empty_value'])) {
                    $field['options']['empty_value'] = '---';
                }
            }

            // Prepare field options for serialization
            $options_to_save = $field['options'];

            // For subtotal fields, ensure the lines array is properly formatted
            if ($field['type'] === 'subtotal' && isset($options_to_save['lines'])) {
                // Filter out empty lines and make sure each line has a label and formula
                $filtered_lines = array();
                foreach ($options_to_save['lines'] as $line) {
                    // Only keep lines that have either a label or a formula
                    if ((isset($line['label']) && !empty($line['label'])) ||
                        (isset($line['formula']) && !empty($line['formula']))) {

                        // Make sure label and formula are set
                        if (!isset($line['label']) || empty($line['label'])) {
                            $line['label'] = 'Line';
                        }
                        if (!isset($line['formula'])) {
                            $line['formula'] = '';
                        }

                        $filtered_lines[] = $line;
                    }
                }

                // Make sure we have at least one line
                if (empty($filtered_lines)) {
                    $filtered_lines[] = array(
                        'label' => 'Line 1',
                        'formula' => ''
                    );
                }

                // Replace the lines with the filtered ones
                $options_to_save['lines'] = $filtered_lines;

                // Make sure empty_value is set
                if (!isset($options_to_save['empty_value'])) {
                    $options_to_save['empty_value'] = '---';
                }

                error_log('Subtotal field options before serialization: ' . print_r($options_to_save, true));
            }

            // Serialize options using WordPress serialization
            $serialized_options = maybe_serialize($options_to_save);

            // Log the serialized options for debugging
            error_log('Options to save: ' . print_r($options_to_save, true));
            error_log('Serialized options: ' . $serialized_options);

            $field_data = array(
                'form_id'        => $form_id,
                'field_type'     => sanitize_text_field($field['type']),
                'field_label'    => sanitize_text_field($field['label']),
                'field_name'     => sanitize_key($field['name']),
                'field_required' => isset($field['required']) ? 1 : 0,
                'field_width'    => $field_width,
                'field_order'    => $order,
                'field_options'  => $serialized_options
            );

            // Handle conditional logic
            if (isset($field['conditional_logic']) && !empty($field['conditional_logic'])) {
                error_log('PFB: Processing conditional logic for field ' . $field['name']);

                // Make sure it's an array
                if (is_string($field['conditional_logic'])) {
                    // Try to decode JSON string
                    $decoded = json_decode($field['conditional_logic'], true);
                    if ($decoded !== null) {
                        $field['conditional_logic'] = $decoded;
                        error_log('PFB: Successfully decoded JSON conditional logic');
                    } else {
                        error_log('PFB: Failed to decode JSON conditional logic: ' . $field['conditional_logic']);
                    }
                }

                // Create a field map to store field names for reference
                $field_map = array();

                // First, create a map of field names to field data
                // This will be used to update field IDs in conditional logic rules
                foreach ($fields as $map_field) {
                    if (isset($map_field['name'])) {
                        // Map by name
                        $field_map[$map_field['name']] = array(
                            'id' => isset($map_field['id']) ? $map_field['id'] : '',
                            'label' => isset($map_field['label']) ? $map_field['label'] : '',
                            'type' => isset($map_field['type']) ? $map_field['type'] : ''
                        );

                        error_log('PFB: Added field to map by name: ' . $map_field['name']);

                        // Also map by ID if available
                        if (isset($map_field['id']) && !empty($map_field['id'])) {
                            $field_map[$map_field['id']] = array(
                                'name' => $map_field['name'],
                                'label' => isset($map_field['label']) ? $map_field['label'] : '',
                                'type' => isset($map_field['type']) ? $map_field['type'] : ''
                            );

                            error_log('PFB: Added field to map by ID: ' . $map_field['id']);
                        }
                    }
                }

                error_log('PFB: Created field map with ' . count($field_map) . ' entries');

                // Process rules to ensure they have field_name and correct field ID
                if (isset($field['conditional_logic']['rules']) && is_array($field['conditional_logic']['rules'])) {
                    error_log('PFB: Processing ' . count($field['conditional_logic']['rules']) . ' rules for field ' . $field['name']);

                    foreach ($field['conditional_logic']['rules'] as $rule_key => $rule) {
                        error_log('PFB: Processing rule ' . ($rule_key + 1) . ': ' . print_r($rule, true));

                        // Check if the field ID is valid (not 0 or empty)
                        $field_id_valid = isset($rule['field']) && !empty($rule['field']) && $rule['field'] !== '0' && $rule['field'] !== 0;

                        if (!$field_id_valid) {
                            error_log('PFB: Invalid field ID in rule: ' . (isset($rule['field']) ? $rule['field'] : 'not set'));

                            // Strategy 1: If we have field_name, use it to find the correct field ID
                            if (isset($rule['field_name']) && !empty($rule['field_name'])) {
                                if (isset($field_map[$rule['field_name']])) {
                                    // Update the field ID to match the current field ID in the map
                                    $field['conditional_logic']['rules'][$rule_key]['field'] = $field_map[$rule['field_name']]['id'];
                                    $field['conditional_logic']['rules'][$rule_key]['field_label'] = $field_map[$rule['field_name']]['label'];
                                    error_log('PFB: Updated rule field ID based on field_name: ' . $rule['field_name'] . ' -> ' . $field_map[$rule['field_name']]['id']);
                                } else {
                                    error_log('PFB: Could not find field ID for name: ' . $rule['field_name']);

                                    // Try to find a field with a similar name (case insensitive)
                                    foreach ($field_map as $key => $map_data) {
                                        if (isset($map_data['name']) && strcasecmp($map_data['name'], $rule['field_name']) === 0) {
                                            $field['conditional_logic']['rules'][$rule_key]['field'] = $map_data['id'];
                                            $field['conditional_logic']['rules'][$rule_key]['field_name'] = $map_data['name']; // Use the correct case
                                            $field['conditional_logic']['rules'][$rule_key]['field_label'] = $map_data['label'];
                                            error_log('PFB: Found field with similar name: ' . $map_data['name'] . ' -> ' . $map_data['id']);
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        // Strategy 2: If we have field ID but no field_name, try to find the field name
                        else if ($field_id_valid) {
                            if (isset($field_map[$rule['field']])) {
                                // Add the field name and label
                                $field['conditional_logic']['rules'][$rule_key]['field_name'] = $field_map[$rule['field']]['name'];
                                $field['conditional_logic']['rules'][$rule_key]['field_label'] = $field_map[$rule['field']]['label'];
                                error_log('PFB: Added field name for ID: ' . $rule['field'] . ' -> ' . $field_map[$rule['field']]['name']);
                            } else {
                                error_log('PFB: Could not find field name for ID: ' . $rule['field']);

                                // Try to find a field with this ID in the current fields array
                                foreach ($fields as $search_field) {
                                    if (isset($search_field['id']) && $search_field['id'] === $rule['field']) {
                                        $field['conditional_logic']['rules'][$rule_key]['field_name'] = $search_field['name'];
                                        $field['conditional_logic']['rules'][$rule_key]['field_label'] = isset($search_field['label']) ? $search_field['label'] : '';
                                        error_log('PFB: Found field name in fields array: ' . $search_field['name']);
                                        break;
                                    }
                                }
                            }
                        }

                        // Final check: Make sure we have both field and field_name
                        if ((!isset($rule['field']) || empty($rule['field']) || $rule['field'] === '0' || $rule['field'] === 0) &&
                            isset($rule['field_name']) && !empty($rule['field_name'])) {
                            // Generate a temporary field ID if we couldn't find one
                            $field['conditional_logic']['rules'][$rule_key]['field'] = 'field_' . sanitize_key($rule['field_name']);
                            error_log('PFB: Generated temporary field ID: ' . $field['conditional_logic']['rules'][$rule_key]['field']);
                        }

                        if ((!isset($rule['field_name']) || empty($rule['field_name'])) &&
                            isset($rule['field']) && !empty($rule['field']) && $rule['field'] !== '0' && $rule['field'] !== 0) {
                            // Generate a field name from the ID if we couldn't find one
                            $field['conditional_logic']['rules'][$rule_key]['field_name'] = 'field_' . substr(sanitize_key($rule['field']), 0, 10);
                            error_log('PFB: Generated field name: ' . $field['conditional_logic']['rules'][$rule_key]['field_name']);
                        }

                        // Final validation: If field is still 0 or empty, remove this rule
                        if (!isset($field['conditional_logic']['rules'][$rule_key]['field']) ||
                            empty($field['conditional_logic']['rules'][$rule_key]['field']) ||
                            $field['conditional_logic']['rules'][$rule_key]['field'] === '0' ||
                            $field['conditional_logic']['rules'][$rule_key]['field'] === 0) {
                            error_log('PFB: Removing rule with invalid field ID');
                            unset($field['conditional_logic']['rules'][$rule_key]);
                        }
                    }

                    // Reindex the rules array after possible removals
                    $field['conditional_logic']['rules'] = array_values($field['conditional_logic']['rules']);
                }

                // Make sure enabled flag is set
                if (!isset($field['conditional_logic']['enabled'])) {
                    $field['conditional_logic']['enabled'] =
                        (isset($field['conditional_logic']['rules']) &&
                         is_array($field['conditional_logic']['rules']) &&
                         count($field['conditional_logic']['rules']) > 0);
                    error_log('PFB: Set enabled flag to: ' . ($field['conditional_logic']['enabled'] ? 'true' : 'false'));
                }

                // Serialize conditional logic
                $field_data['conditional_logic'] = json_encode($field['conditional_logic']);
                error_log('PFB: Saving conditional logic for field ' . $field['name'] . ': ' . print_r($field['conditional_logic'], true));
            } else {
                error_log('PFB: No conditional logic to save for field ' . $field['name']);

                // Create empty conditional logic structure
                $field_data['conditional_logic'] = json_encode(array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'rules' => array()
                ));
            }

            $wpdb->insert($table_name, $field_data);
        }
    }

    /**
     * Delete a form from the database.
     *
     * @since    1.0.0
     * @param    int      $id    Form ID.
     * @return   bool            True on success, false on failure.
     */
    public static function delete_form($id) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_forms';

        // Delete form fields first
        $fields_table = $wpdb->prefix . 'pfb_form_fields';
        $wpdb->delete($fields_table, array('form_id' => $id));

        // Delete form
        $result = $wpdb->delete($table_name, array('id' => $id));

        return $result !== false;
    }

    /**
     * Get price categories from the database.
     *
     * @since    1.0.0
     * @param    array    $args    Query arguments.
     * @return   array              Array of price categories.
     */
    public static function get_price_categories($args = array()) {
        global $wpdb;

        $defaults = array(
            'number'     => 20,
            'offset'     => 0,
            'orderby'    => 'id',
            'order'      => 'DESC'
        );

        $args = wp_parse_args($args, $defaults);

        $table_name = $wpdb->prefix . 'pfb_price_categories';

        $sql = "SELECT * FROM $table_name";

        $sql .= " ORDER BY " . sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']);

        $sql .= $wpdb->prepare(" LIMIT %d, %d", $args['offset'], $args['number']);

        $categories = $wpdb->get_results($sql, ARRAY_A);

        return $categories;
    }

    /**
     * Get price variables from the database.
     *
     * @since    1.0.0
     * @param    array    $args    Query arguments.
     * @return   array              Array of price variables.
     */
    public static function get_price_variables($args = array()) {
        global $wpdb;

        $defaults = array(
            'number'      => 20,
            'offset'      => 0,
            'orderby'     => 'id',
            'order'       => 'DESC',
            'category_id' => 0
        );

        $args = wp_parse_args($args, $defaults);

        $table_name = $wpdb->prefix . 'pfb_price_variables';

        $sql = "SELECT * FROM $table_name";

        if (!empty($args['category_id'])) {
            $sql .= $wpdb->prepare(" WHERE category_id = %d", $args['category_id']);
        }

        $sql .= " ORDER BY " . sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']);

        $sql .= $wpdb->prepare(" LIMIT %d, %d", $args['offset'], $args['number']);

        $variables = $wpdb->get_results($sql, ARRAY_A);

        // Check if we have any variables
        if (!empty($variables) && is_array($variables)) {
            foreach ($variables as &$variable) {
                if (!empty($variable['price_ranges'])) {
                    $variable['price_ranges'] = maybe_unserialize($variable['price_ranges']);
                }
            }
        }

        return $variables;
    }

    /**
     * Save a price category to the database.
     *
     * @since    1.0.0
     * @param    array    $category_data    Category data.
     * @return   int                         Category ID.
     */
    public static function save_price_category($category_data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_price_categories';

        $category = array(
            'name'        => sanitize_text_field($category_data['name']),
            'description' => sanitize_textarea_field($category_data['description'])
        );

        if (isset($category_data['id']) && !empty($category_data['id'])) {
            // Update existing category
            $wpdb->update(
                $table_name,
                $category,
                array('id' => $category_data['id'])
            );

            $category_id = $category_data['id'];
        } else {
            // Insert new category
            $wpdb->insert($table_name, $category);
            $category_id = $wpdb->insert_id;
        }

        return $category_id;
    }

    /**
     * Save a price variable to the database.
     *
     * @since    1.0.0
     * @param    array    $variable_data    Variable data.
     * @return   int                         Variable ID.
     */
    public static function save_price_variable($variable_data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_price_variables';

        $variable = array(
            'category_id' => intval($variable_data['category_id']),
            'name'        => sanitize_text_field($variable_data['name']),
            'variable_key' => sanitize_key($variable_data['variable_key']),
            'price_type'  => sanitize_text_field($variable_data['price_type']),
            'price_value' => floatval($variable_data['price_value'])
        );

        if (isset($variable_data['price_ranges']) && is_array($variable_data['price_ranges'])) {
            $variable['price_ranges'] = maybe_serialize($variable_data['price_ranges']);
        }

        if (isset($variable_data['id']) && !empty($variable_data['id'])) {
            // Update existing variable
            $wpdb->update(
                $table_name,
                $variable,
                array('id' => $variable_data['id'])
            );

            $variable_id = $variable_data['id'];
        } else {
            // Insert new variable
            $wpdb->insert($table_name, $variable);
            $variable_id = $wpdb->insert_id;
        }

        return $variable_id;
    }

    /**
     * Get currencies from the database.
     *
     * @since    1.0.0
     * @param    array    $args    Query arguments.
     * @return   array              Array of currencies.
     */
    public static function get_currencies($args = array()) {
        global $wpdb;

        $defaults = array(
            'number'     => 20,
            'offset'     => 0,
            'orderby'    => 'id',
            'order'      => 'ASC'
        );

        $args = wp_parse_args($args, $defaults);

        $table_name = $wpdb->prefix . 'pfb_currencies';

        $sql = "SELECT * FROM $table_name";

        $sql .= " ORDER BY " . sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']);

        $sql .= $wpdb->prepare(" LIMIT %d, %d", $args['offset'], $args['number']);

        $currencies = $wpdb->get_results($sql, ARRAY_A);

        return $currencies;
    }

    /**
     * Get the default currency.
     *
     * @since    1.0.0
     * @return   array    Default currency.
     */
    public static function get_default_currency() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_currencies';

        $currency = $wpdb->get_row(
            "SELECT * FROM $table_name WHERE is_default = 1",
            ARRAY_A
        );

        return $currency;
    }

    /**
     * Save a currency to the database.
     *
     * @since    1.0.0
     * @param    array    $currency_data    Currency data.
     * @return   int                         Currency ID.
     */
    public static function save_currency($currency_data) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_currencies';

        $currency = array(
            'name'               => sanitize_text_field($currency_data['name']),
            'code'               => sanitize_text_field($currency_data['code']),
            'symbol'             => sanitize_text_field($currency_data['symbol']),
            'exchange_rate'      => floatval($currency_data['exchange_rate']),
            'is_default'         => isset($currency_data['is_default']) ? 1 : 0,
            'symbol_position'    => sanitize_text_field($currency_data['symbol_position']),
            'rtl_symbol_position'=> isset($currency_data['rtl_symbol_position']) ? sanitize_text_field($currency_data['rtl_symbol_position']) : null,
            'thousand_separator' => sanitize_text_field($currency_data['thousand_separator']),
            'decimal_separator'  => sanitize_text_field($currency_data['decimal_separator']),
            'decimal_places'     => intval($currency_data['decimal_places'])
        );

        // If this is the default currency, unset all other default currencies
        if ($currency['is_default']) {
            $wpdb->update(
                $table_name,
                array('is_default' => 0),
                array('is_default' => 1)
            );
        }

        if (isset($currency_data['id']) && !empty($currency_data['id'])) {
            // Update existing currency
            $wpdb->update(
                $table_name,
                $currency,
                array('id' => $currency_data['id'])
            );

            $currency_id = $currency_data['id'];
        } else {
            // Insert new currency
            $wpdb->insert($table_name, $currency);
            $currency_id = $wpdb->insert_id;
        }

        return $currency_id;
    }

    /**
     * Get translations from the database.
     *
     * @since    1.0.0
     * @param    string   $object_type     Object type.
     * @param    int      $object_id       Object ID.
     * @param    string   $language_code   Language code.
     * @return   array                     Array of translations.
     */
    public static function get_translations($object_type, $object_id, $language_code) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_translations';

        $translations = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM $table_name WHERE object_type = %s AND object_id = %d AND language_code = %s",
                $object_type,
                $object_id,
                $language_code
            ),
            ARRAY_A
        );

        $result = array();

        foreach ($translations as $translation) {
            $result[$translation['field_name']] = $translation['translated_value'];
        }

        return $result;
    }

    /**
     * Save translations to the database.
     *
     * @since    1.0.0
     * @param    string   $object_type     Object type.
     * @param    int      $object_id       Object ID.
     * @param    string   $language_code   Language code.
     * @param    array    $translations    Translations.
     */
    public static function save_translations($object_type, $object_id, $language_code, $translations) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_translations';

        // Delete existing translations
        $wpdb->delete(
            $table_name,
            array(
                'object_type'   => $object_type,
                'object_id'     => $object_id,
                'language_code' => $language_code
            )
        );

        // Insert new translations
        foreach ($translations as $field_name => $translated_value) {
            $wpdb->insert(
                $table_name,
                array(
                    'object_type'      => $object_type,
                    'object_id'        => $object_id,
                    'language_code'    => $language_code,
                    'field_name'       => $field_name,
                    'translated_value' => $translated_value
                )
            );
        }
    }
}
