<?php
/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * @since      1.0.0
 */
class PFB {

    /**
     * The loader that's responsible for maintaining and registering all hooks that power
     * the plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      PFB_Loader    $loader    Maintains and registers all hooks for the plugin.
     */
    protected $loader;

    /**
     * The unique identifier of this plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $plugin_name    The string used to uniquely identify this plugin.
     */
    protected $plugin_name;

    /**
     * The current version of the plugin.
     *
     * @since    1.0.0
     * @access   protected
     * @var      string    $version    The current version of the plugin.
     */
    protected $version;

    /**
     * Define the core functionality of the plugin.
     *
     * @since    1.0.0
     */
    public function __construct() {
        $this->version = PFB_VERSION;
        $this->plugin_name = 'price-form-builder';

        $this->load_dependencies();
        $this->set_locale();
        $this->define_admin_hooks();
        $this->define_public_hooks();
    }

    /**
     * Load the required dependencies for this plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function load_dependencies() {
        /**
         * The class responsible for orchestrating the actions and filters of the
         * core plugin.
         */
        require_once PFB_PLUGIN_DIR . 'includes/class-pfb-loader.php';

        /**
         * The class responsible for defining internationalization functionality
         * of the plugin.
         */
        require_once PFB_PLUGIN_DIR . 'includes/class-pfb-i18n.php';

        /**
         * The class responsible for defining all actions that occur in the admin area.
         */
        require_once PFB_PLUGIN_DIR . 'admin/class-pfb-admin.php';

        /**
         * The class responsible for defining all actions that occur in the public-facing
         * side of the site.
         */
        require_once PFB_PLUGIN_DIR . 'public/class-pfb-public.php';

        /**
         * The class responsible for database operations.
         */
        require_once PFB_PLUGIN_DIR . 'includes/class-pfb-db.php';

        /**
         * The class responsible for form operations.
         */
        require_once PFB_PLUGIN_DIR . 'includes/class-pfb-form.php';

        /**
         * The class responsible for price calculations.
         */
        require_once PFB_PLUGIN_DIR . 'includes/class-pfb-calculator.php';

        /**
         * The class responsible for currency operations.
         */
        require_once PFB_PLUGIN_DIR . 'includes/class-pfb-currency.php';

        /**
         * The class responsible for handling conditional logic.
         */
        require_once PFB_PLUGIN_DIR . 'includes/class-pfb-conditional-logic.php';

        /**
         * The class responsible for handling form fields.
         */
        require_once PFB_PLUGIN_DIR . 'includes/class-pfb-form-field-handler.php';

        /**
         * The class responsible for handling forms.
         */
        require_once PFB_PLUGIN_DIR . 'includes/class-pfb-form-handler.php';

        $this->loader = new PFB_Loader();
    }

    /**
     * Define the locale for this plugin for internationalization.
     *
     * @since    1.0.0
     * @access   private
     */
    private function set_locale() {
        $plugin_i18n = new PFB_i18n();
        $plugin_i18n->set_domain($this->get_plugin_name());

        $this->loader->add_action('plugins_loaded', $plugin_i18n, 'load_plugin_textdomain');
    }

    /**
     * Register all of the hooks related to the admin area functionality
     * of the plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_admin_hooks() {
        $plugin_admin = new PFB_Admin($this->get_plugin_name(), $this->get_version());

        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_styles');
        $this->loader->add_action('admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts');
        $this->loader->add_action('admin_menu', $plugin_admin, 'add_plugin_admin_menu');

        // AJAX handlers
        $this->loader->add_action('wp_ajax_pfb_save_form', $plugin_admin, 'ajax_save_form');
        $this->loader->add_action('wp_ajax_pfb_get_form', $plugin_admin, 'ajax_get_form');
        $this->loader->add_action('wp_ajax_pfb_delete_form', $plugin_admin, 'ajax_delete_form');
        $this->loader->add_action('wp_ajax_pfb_save_price_variable', $plugin_admin, 'ajax_save_price_variable');
        $this->loader->add_action('wp_ajax_pfb_get_price_variables', $plugin_admin, 'ajax_get_price_variables');
        $this->loader->add_action('wp_ajax_pfb_delete_price_variable', $plugin_admin, 'ajax_delete_price_variable');
        $this->loader->add_action('wp_ajax_pfb_save_price_sheet', $plugin_admin, 'ajax_save_price_sheet');
        $this->loader->add_action('wp_ajax_pfb_save_price_category', $plugin_admin, 'ajax_save_price_category');
        $this->loader->add_action('wp_ajax_pfb_get_price_category', $plugin_admin, 'ajax_get_price_category');
        $this->loader->add_action('wp_ajax_pfb_delete_price_category', $plugin_admin, 'ajax_delete_price_category');
        $this->loader->add_action('wp_ajax_pfb_save_currency', $plugin_admin, 'ajax_save_currency');
        $this->loader->add_action('wp_ajax_pfb_get_currency', $plugin_admin, 'ajax_get_currency');
        $this->loader->add_action('wp_ajax_pfb_get_currencies', $plugin_admin, 'ajax_get_currencies');
        $this->loader->add_action('wp_ajax_pfb_delete_currency', $plugin_admin, 'ajax_delete_currency');
        $this->loader->add_action('wp_ajax_pfb_set_default_currency', $plugin_admin, 'ajax_set_default_currency');
        $this->loader->add_action('wp_ajax_pfb_save_settings', $plugin_admin, 'ajax_save_settings');
        $this->loader->add_action('wp_ajax_pfb_test_formula', $plugin_admin, 'ajax_test_formula');
        $this->loader->add_action('wp_ajax_pfb_check_database', $plugin_admin, 'ajax_check_database');

        // Initialize form handler
        $form_handler = new PFB_Form_Handler();
    }

    /**
     * Register all of the hooks related to the public-facing functionality
     * of the plugin.
     *
     * @since    1.0.0
     * @access   private
     */
    private function define_public_hooks() {
        $plugin_public = new PFB_Public($this->get_plugin_name(), $this->get_version());

        $this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_styles');
        $this->loader->add_action('wp_enqueue_scripts', $plugin_public, 'enqueue_scripts');

        // Register shortcode
        $this->loader->add_shortcode('price_form', $plugin_public, 'render_price_form');

        // AJAX handlers for frontend
        $this->loader->add_action('wp_ajax_pfb_calculate_price', $plugin_public, 'ajax_calculate_price');
        $this->loader->add_action('wp_ajax_nopriv_pfb_calculate_price', $plugin_public, 'ajax_calculate_price');
    }

    /**
     * Run the loader to execute all of the hooks with WordPress.
     *
     * @since    1.0.0
     */
    public function run() {
        $this->loader->run();
    }

    /**
     * The name of the plugin used to uniquely identify it within the context of
     * WordPress and to define internationalization functionality.
     *
     * @since     1.0.0
     * @return    string    The name of the plugin.
     */
    public function get_plugin_name() {
        return $this->plugin_name;
    }

    /**
     * The reference to the class that orchestrates the hooks with the plugin.
     *
     * @since     1.0.0
     * @return    PFB_Loader    Orchestrates the hooks of the plugin.
     */
    public function get_loader() {
        return $this->loader;
    }

    /**
     * Retrieve the version number of the plugin.
     *
     * @since     1.0.0
     * @return    string    The version number of the plugin.
     */
    public function get_version() {
        return $this->version;
    }
}
