<?php
/**
 * Price calculator for the plugin.
 *
 * @since      1.0.0
 */
class PFB_Calculator {

    /**
     * Variables for calculation.
     *
     * @since    1.0.0
     * @access   private
     * @var      array    $variables    Variables for calculation.
     */
    private $variables = array();

    /**
     * Initialize the class.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // Load all price variables
        $this->load_variables();
    }

    /**
     * Load price variables from the database.
     *
     * @since    1.0.0
     */
    private function load_variables() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'pfb_price_variables';

        $variables = $wpdb->get_results("SELECT * FROM $table_name", ARRAY_A);

        foreach ($variables as $variable) {
            $key = $variable['variable_key'];

            if ($variable['price_type'] === 'fixed') {
                $this->variables[$key] = floatval($variable['price_value']);
            } else {
                // For range-based pricing, we'll handle it during calculation
                $this->variables[$key] = array(
                    'type'   => 'range',
                    'ranges' => maybe_unserialize($variable['price_ranges'])
                );
            }
        }
    }

    /**
     * Calculate price based on form data.
     *
     * @since    1.0.0
     * @param    array    $form       Form data.
     * @param    array    $form_data  Submitted form data.
     * @return   float                Calculated price.
     */
    public function calculate($form, $form_data) {
        $total = 0;
        $subtotals = array();
        $main_formula = '';

        // Debug log the input data
        error_log('Calculator input - form: ' . print_r($form, true));
        error_log('Calculator input - form_data: ' . print_r($form_data, true));

        // First, calculate all subtotal fields
        foreach ($form['fields'] as $field) {
            if ($field['field_type'] === 'subtotal') {
                $options = maybe_unserialize($field['field_options']);
                $field_name = $field['field_name'];
                $subtotal_value = 0;
                $subtotal_lines = array();

                // Check if this field has a conditional formula
                if (isset($options['conditional_formula'])) {
                    error_log("Found conditional formula for subtotal field: " . print_r($options['conditional_formula'], true));

                    // Load the conditional formula class
                    require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-pfb-conditional-formula.php';
                    $conditional_formula = new PFB_Conditional_Formula($this->variables, $form_data);

                    // Evaluate the conditional formula
                    $subtotal_value = $conditional_formula->evaluate($options['conditional_formula']);

                    // Store the subtotal value for use in the total formula
                    $subtotals[$field_name] = $subtotal_value;

                    error_log("Conditional subtotal {$field_name} calculated: {$subtotal_value}");
                }
                // Check if this is a multi-line subtotal field
                else if (isset($options['lines']) && is_array($options['lines']) && !empty($options['lines'])) {
                    error_log("Processing multi-line subtotal field: {$field_name} with " . count($options['lines']) . " lines");
                    error_log("Options: " . print_r($options, true));

                    // Calculate each line
                    foreach ($options['lines'] as $index => $line) {
                        error_log("Processing line {$index}: " . print_r($line, true));

                        if (isset($line['formula']) && !empty($line['formula'])) {
                            $line_formula = $line['formula'];
                            $line_label = isset($line['label']) ? $line['label'] : '';

                            error_log("Processing subtotal line: {$line_label} with formula: {$line_formula}");

                            try {
                                // Calculate the line value
                                $line_value = $this->calculate_single_formula($line_formula, $form, $form_data);

                                // Add to the subtotal
                                $subtotal_value += $line_value;

                                // Check if formula has required fields
                                $has_required_fields = $this->formula_has_required_fields($line_formula, $form, $form_data);

                                // Store the line value for display
                                $subtotal_lines[] = array(
                                    'label' => $line_label,
                                    'value' => $line_value,
                                    'formula' => $line_formula,
                                    'has_required_fields' => $has_required_fields
                                );

                                error_log("Subtotal line {$line_label} calculated: {$line_value}, has required fields: " . ($has_required_fields ? 'yes' : 'no'));
                            } catch (Exception $e) {
                                error_log("Error calculating line {$line_label}: " . $e->getMessage());

                                // Add a placeholder for the failed calculation
                                $subtotal_lines[] = array(
                                    'label' => $line_label,
                                    'value' => 0,
                                    'formula' => $line_formula,
                                    'has_required_fields' => true,
                                    'error' => $e->getMessage()
                                );
                            }
                        } else {
                            error_log("Line {$index} has no formula or empty formula");
                        }
                    }

                    // Store the subtotal lines for display
                    $subtotals[$field_name] = array(
                        'total' => $subtotal_value,
                        'lines' => $subtotal_lines,
                        'empty_value' => isset($options['empty_value']) ? $options['empty_value'] : '---'
                    );
                }
                // Backward compatibility for single formula subtotal fields
                else if (isset($options['formula'])) {
                    $formula = $options['formula'];

                    error_log("Processing legacy subtotal field: {$field_name} with formula: {$formula}");

                    // Calculate the subtotal
                    $subtotal_value = $this->calculate_single_formula($formula, $form, $form_data);

                    // Store the subtotal value for use in the total formula
                    $subtotals[$field_name] = $subtotal_value;

                    error_log("Legacy subtotal {$field_name} calculated: {$subtotal_value}");
                }

                // Add the subtotal to the form data so it can be referenced in other formulas
                $form_data[$field_name] = $subtotal_value;
            }
        }

        // Then find the total field and its formula
        foreach ($form['fields'] as $field) {
            if ($field['field_type'] === 'total') {
                $options = maybe_unserialize($field['field_options']);

                // Check if this field has a conditional formula
                if (isset($options['conditional_formula'])) {
                    error_log("Found conditional formula for total field: " . print_r($options['conditional_formula'], true));

                    // Load the conditional formula class
                    require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-pfb-conditional-formula.php';
                    $conditional_formula = new PFB_Conditional_Formula($this->variables, $form_data);

                    // Evaluate the conditional formula
                    $total = $conditional_formula->evaluate($options['conditional_formula']);

                    // Return the total, subtotals, and variables
                    return array(
                        'total' => $total,
                        'subtotals' => $subtotals,
                        'variables' => $this->get_variable_values()
                    );
                }
                // Regular formula
                else if (isset($options['formula'])) {
                    $main_formula = $options['formula'];
                    error_log("Found total formula: {$main_formula}");
                }
                break;
            }
        }

        if (empty($main_formula)) {
            // If no total formula is found, sum up all subtotals
            if (!empty($subtotals)) {
                // For multi-line subtotals, we need to extract the total value
                $subtotal_values = array();
                foreach ($subtotals as $field_name => $subtotal) {
                    if (is_array($subtotal) && isset($subtotal['total'])) {
                        $subtotal_values[$field_name] = $subtotal['total'];
                    } else {
                        $subtotal_values[$field_name] = floatval($subtotal);
                    }
                }

                $total = array_sum($subtotal_values);
                error_log("No total formula found, summing subtotals: {$total}");
            }

            // Return both the total and subtotals
            return array(
                'total' => $total,
                'subtotals' => $subtotals
            );
        }

        // Calculate the total using the main formula
        $total = $this->calculate_single_formula($main_formula, $form, $form_data);
        error_log("Final total calculated: {$total}");

        // Return the total, subtotals, and variables
        return array(
            'total' => $total,
            'subtotals' => $subtotals,
            'variables' => $this->get_variable_values()
        );
    }

    /**
     * Calculate a single formula.
     *
     * @since    1.0.0
     * @param    string   $formula     Formula to calculate.
     * @param    array    $form        Form data.
     * @param    array    $form_data   Submitted form data.
     * @return   float                 Calculated value.
     */
    public function calculate_single_formula($formula, $form, $form_data) {
        if (empty($formula)) {
            error_log('Empty formula, returning 0');
            return 0;
        }

        error_log('Processing formula: ' . $formula);
        error_log('Form data: ' . print_r($form_data, true));
        error_log('Variables: ' . print_r($this->variables, true));

        // Process form data to get field values
        $field_values = array();

        foreach ($form['fields'] as $field) {
            $field_name = $field['field_name'];

            if (isset($form_data[$field_name])) {
                $value = $form_data[$field_name];

                // Handle different field types
                switch ($field['field_type']) {
                    case 'checkbox':
                        // For checkboxes, we need to sum up all selected values
                        if (is_array($value)) {
                            $checkbox_sum = 0;
                            $options = maybe_unserialize($field['field_options']);

                            if (isset($options['items']) && is_array($options['items'])) {
                                foreach ($value as $checkbox_value) {
                                    // Find the selected option
                                    foreach ($options['items'] as $item) {
                                        if ($item['value'] == $checkbox_value) {
                                            // Check if this option has a variable
                                            if (!empty($item['variable']) && isset($this->variables[$item['variable']])) {
                                                $variable_value = $this->variables[$item['variable']];
                                                if (is_array($variable_value) && $variable_value['type'] === 'range') {
                                                    // For range-based variables, we need a quantity
                                                    $checkbox_sum += 0; // Skip for now
                                                } else {
                                                    $checkbox_sum += floatval($variable_value);
                                                }
                                            } else {
                                                $checkbox_sum += floatval($checkbox_value);
                                            }
                                            break;
                                        }
                                    }
                                }
                            } else {
                                // Fallback to simple sum
                                foreach ($value as $checkbox_value) {
                                    $checkbox_sum += floatval($checkbox_value);
                                }
                            }

                            $field_values[$field_name] = $checkbox_sum;
                        } else {
                            $field_values[$field_name] = floatval($value);
                        }
                        break;

                    case 'dropdown':
                    case 'radio':
                        // For dropdown and radio, check if the selected option has a variable
                        $options = maybe_unserialize($field['field_options']);
                        $field_value = floatval($value); // Default value

                        if (isset($options['items']) && is_array($options['items'])) {
                            foreach ($options['items'] as $item) {
                                if ($item['value'] == $value) {
                                    // Check if this option has a variable
                                    if (!empty($item['variable']) && isset($this->variables[$item['variable']])) {
                                        $variable_value = $this->variables[$item['variable']];
                                        if (is_array($variable_value) && $variable_value['type'] === 'range') {
                                            // For range-based variables, we need a quantity
                                            // We'll use 1 as default quantity
                                            $field_value = $this->get_range_price($variable_value['ranges'], 1);
                                        } else {
                                            $field_value = floatval($variable_value);
                                        }
                                    } else {
                                        $field_value = floatval($value);
                                    }
                                    break;
                                }
                            }
                        }

                        $field_values[$field_name] = $field_value;
                        break;

                    case 'number':
                    case 'slider':
                    case 'subtotal':
                    case 'total':
                        // For number fields, slider fields, and calculated fields, just use the value
                        $field_values[$field_name] = floatval($value);
                        break;

                    default:
                        $field_values[$field_name] = floatval($value);
                        break;
                }
            } else {
                // If field is not in form data, set to 0
                $field_values[$field_name] = 0;
            }
        }

        // Process any hidden variable fields (pfb_var_*)
        foreach ($form_data as $key => $value) {
            if (strpos($key, 'pfb_var_') === 0) {
                $var_name = substr($key, 8); // Remove 'pfb_var_' prefix
                $field_values[$var_name] = floatval($value);
                error_log("Found hidden variable field: {$key} with value: {$value}");
            }
        }

        // Log the field values and variables
        error_log('Field values for formula: ' . print_r($field_values, true));

        // Use the simple calculator to evaluate the formula
        require_once plugin_dir_path(dirname(__FILE__)) . 'includes/class-pfb-simple-calculator.php';
        $calculator = new PFB_Simple_Calculator($this->variables);

        try {
            $result = $calculator->calculate($formula, $field_values);
            error_log("Formula '{$formula}' calculated result: {$result}");
            return $result;
        } catch (Exception $e) {
            error_log('Formula calculation error: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Check if a formula has required fields that are not filled.
     *
     * @since    1.0.0
     * @param    string   $formula     Formula to check.
     * @param    array    $form        Form data.
     * @param    array    $form_data   Submitted form data.
     * @return   boolean               True if formula has required fields that are not filled.
     */
    private function formula_has_required_fields($formula, $form, $form_data) {
        error_log("Checking if formula has required fields: {$formula}");

        // Extract field names from the formula
        preg_match_all('/\{([^{}]+)\}/', $formula, $matches);

        if (empty($matches[1])) {
            error_log("No field references found in formula");
            return false;
        }

        $field_names = $matches[1];
        error_log("Field names in formula: " . implode(', ', $field_names));

        // Check if any of the fields are required and not filled
        foreach ($field_names as $field_name) {
            // Skip if it's a variable or already in form_data
            if (isset($this->variables[$field_name])) {
                error_log("Field {$field_name} is a variable, skipping");
                continue;
            }

            if (isset($form_data[$field_name])) {
                error_log("Field {$field_name} is in form_data with value: " . print_r($form_data[$field_name], true));
                continue;
            }

            error_log("Field {$field_name} is not in form_data");

            // Find the field in the form
            $field_found = false;
            foreach ($form['fields'] as $field) {
                if ($field['field_name'] === $field_name) {
                    $field_found = true;
                    // Check if the field is required
                    if (isset($field['field_required']) && $field['field_required']) {
                        error_log("Field {$field_name} is required but not filled");
                        return true;
                    }
                    break;
                }
            }

            if (!$field_found) {
                error_log("Field {$field_name} not found in form definition");
            }
        }

        error_log("Formula does not have any required fields that are not filled");
        return false;
    }

    /**
     * Get all variable values for use in labels.
     *
     * @since    1.0.0
     * @return   array    Array of variable values.
     */
    private function get_variable_values() {
        $variable_values = array();

        foreach ($this->variables as $key => $value) {
            // For simple variables, just use the value
            if (!is_array($value)) {
                $variable_values[$key] = $value;
            }
            // For range-based variables, use the first range's price as default
            else if (isset($value['type']) && $value['type'] === 'range' && isset($value['ranges']) && !empty($value['ranges'])) {
                $first_range = reset($value['ranges']);
                $variable_values[$key] = isset($first_range['price']) ? floatval($first_range['price']) : 0;
            }
        }

        return $variable_values;
    }

    /**
     * Get price from a range based on quantity.
     *
     * @since    1.0.0
     * @param    array    $ranges     Price ranges.
     * @param    float    $quantity   Quantity.
     * @return   float                Price.
     */
    private function get_range_price($ranges, $quantity) {
        if (empty($ranges) || !is_array($ranges)) {
            return 0;
        }

        // Sort ranges by min value
        usort($ranges, function($a, $b) {
            return $a['min'] - $b['min'];
        });

        // Find the appropriate range
        foreach ($ranges as $range) {
            $min = floatval($range['min']);
            $max = isset($range['max']) ? floatval($range['max']) : PHP_FLOAT_MAX;

            if ($quantity >= $min && $quantity <= $max) {
                return floatval($range['price']);
            }
        }

        // If no range matches, return the price of the last range
        $last_range = end($ranges);
        return floatval($last_range['price']);
    }

    /**
     * Evaluate a mathematical formula.
     *
     * @since    1.0.0
     * @param    string   $formula    Formula to evaluate.
     * @return   float                Result.
     */
    private function evaluate_formula($formula) {
        // Debug the original formula
        error_log('Original formula: ' . $formula);

        // Check for any remaining field or variable references that weren't replaced
        if (preg_match_all('/\{([^{}]+)\}/', $formula, $matches)) {
            error_log('WARNING: Unreplaced variables in formula: ' . print_r($matches[1], true));

            // Replace any unreplaced variables with 0 to avoid errors
            foreach ($matches[1] as $unreplaced) {
                $formula = str_replace('{' . $unreplaced . '}', '0', $formula);
                error_log('Replaced unreplaced variable {' . $unreplaced . '} with 0');
            }
        }

        // Make the formula safe to evaluate
        $formula = $this->sanitize_formula($formula);

        // Debug the sanitized formula
        error_log('Sanitized formula: ' . $formula);

        // Process function calls recursively
        $formula = $this->process_functions($formula);

        // Debug the processed formula
        error_log('Processed formula: ' . $formula);

        // Evaluate the final formula
        $result = 0;
        try {
            // Replace any remaining placeholders with 0 to avoid errors
            $formula = preg_replace('/___FUNC_[a-z]+_\d+___/i', '0', $formula);

            // Final check for any syntax issues
            if (strpos($formula, '()') !== false) {
                error_log('WARNING: Empty parentheses in formula: ' . $formula);
                $formula = str_replace('()', '(0)', $formula);
            }

            // Check for consecutive operators
            if (preg_match('/[\+\-\*\/]{2,}/', $formula)) {
                error_log('WARNING: Consecutive operators in formula: ' . $formula);
                $formula = preg_replace('/[\+\-\*\/]{2,}/', '+', $formula);
            }

            // Check for operators at the beginning or end
            if (preg_match('/^[\+\-\*\/]/', trim($formula))) {
                error_log('WARNING: Operator at beginning of formula: ' . $formula);
                $formula = '0' . $formula;
            }

            if (preg_match('/[\+\-\*\/]$/', trim($formula))) {
                error_log('WARNING: Operator at end of formula: ' . $formula);
                $formula .= '0';
            }

            $code = 'return ' . $formula . ';';
            error_log('Final eval code: ' . $code);
            $result = eval($code);

            if ($result === false && error_get_last()) {
                error_log('Final formula evaluation error: ' . error_get_last()['message']);
                throw new Exception('Final formula evaluation error');
            }

            error_log('Formula evaluation result: ' . $result);
        } catch (ParseError $e) {
            error_log('Final formula parse error: ' . $e->getMessage());
            return 0;
        } catch (Exception $e) {
            error_log('Final formula exception: ' . $e->getMessage());
            return 0;
        }

        return floatval($result);
    }

    /**
     * Process function calls in a formula recursively.
     *
     * @since    1.0.0
     * @param    string   $formula    Formula to process.
     * @return   string               Processed formula.
     */
    private function process_functions($formula) {
        // Check for any remaining field or variable references that weren't replaced
        if (preg_match_all('/\{([^{}]+)\}/', $formula, $matches)) {
            error_log('WARNING: Unreplaced variables in function arguments: ' . print_r($matches[1], true));

            // Replace any unreplaced variables with 0 to avoid errors
            foreach ($matches[1] as $unreplaced) {
                $formula = str_replace('{' . $unreplaced . '}', '0', $formula);
                error_log('Replaced unreplaced variable {' . $unreplaced . '} with 0 in function arguments');
            }
        }

        // Define supported functions
        $functions = array('ceil', 'floor', 'round', 'min', 'max');

        // Process each function
        foreach ($functions as $function) {
            // Match function calls with proper nested parentheses handling
            $pattern = '/\b' . $function . '\s*\(/i';
            $matches = array();
            $offset = 0;

            while (preg_match($pattern, $formula, $matches, PREG_OFFSET_CAPTURE, $offset)) {
                $func_pos = $matches[0][1];
                $open_paren_pos = $func_pos + strlen($matches[0][0]) - 1;

                // Find the matching closing parenthesis
                $level = 1;
                $close_paren_pos = -1;

                for ($i = $open_paren_pos + 1; $i < strlen($formula); $i++) {
                    if ($formula[$i] === '(') {
                        $level++;
                    } elseif ($formula[$i] === ')') {
                        $level--;
                        if ($level === 0) {
                            $close_paren_pos = $i;
                            break;
                        }
                    }
                }

                if ($close_paren_pos === -1) {
                    // No matching closing parenthesis found
                    error_log("WARNING: No matching closing parenthesis for {$function} at position {$func_pos}");
                    // Add a closing parenthesis at the end
                    $formula .= ')';
                    $close_paren_pos = strlen($formula) - 1;
                    error_log("Added missing closing parenthesis: {$formula}");
                }

                // Extract the function arguments
                $args_str = substr($formula, $open_paren_pos + 1, $close_paren_pos - $open_paren_pos - 1);
                error_log("Function {$function} arguments: {$args_str}");

                // Check if arguments are empty
                if (trim($args_str) === '') {
                    error_log("WARNING: Empty arguments for {$function}");
                    // Replace the function call with 0
                    $full_func = substr($formula, $func_pos, $close_paren_pos - $func_pos + 1);
                    $formula = str_replace($full_func, '0', $formula);
                    error_log("Replaced empty function call with 0: {$formula}");
                    $offset = 0;
                    continue;
                }

                // Process any nested functions in the arguments
                $processed_args = $this->process_functions($args_str);
                error_log("Processed {$function} arguments: {$processed_args}");

                // Check for division by zero
                if (preg_match('/\/\s*0(\s|$)/', $processed_args)) {
                    error_log("WARNING: Division by zero in {$function} arguments: {$processed_args}");
                    // Replace the division by zero with division by 1
                    $processed_args = preg_replace('/\/\s*0(\s|$)/', '/1$1', $processed_args);
                    error_log("Fixed division by zero: {$processed_args}");
                }

                // Evaluate the arguments
                try {
                    // Check for syntax errors before evaluation
                    if (preg_match('/[\+\-\*\/]{2,}/', $processed_args)) {
                        error_log("WARNING: Consecutive operators in {$function} arguments: {$processed_args}");
                        $processed_args = preg_replace('/[\+\-\*\/]{2,}/', '+', $processed_args);
                        error_log("Fixed consecutive operators: {$processed_args}");
                    }

                    if (preg_match('/^[\+\-\*\/]/', trim($processed_args))) {
                        error_log("WARNING: Operator at beginning of {$function} arguments: {$processed_args}");
                        $processed_args = '0' . $processed_args;
                        error_log("Fixed operator at beginning: {$processed_args}");
                    }

                    if (preg_match('/[\+\-\*\/]$/', trim($processed_args))) {
                        error_log("WARNING: Operator at end of {$function} arguments: {$processed_args}");
                        $processed_args .= '0';
                        error_log("Fixed operator at end: {$processed_args}");
                    }

                    $args_code = 'return ' . $processed_args . ';';
                    error_log("Evaluating {$function} arguments with code: {$args_code}");
                    $args_value = eval($args_code);

                    if ($args_value === false && error_get_last()) {
                        error_log('Args evaluation error: ' . error_get_last()['message']);
                        throw new Exception('Args evaluation error');
                    }

                    error_log("Arguments evaluation result: " . print_r($args_value, true));

                    // Apply the function
                    $result = 0;
                    switch ($function) {
                        case 'ceil':
                            $result = ceil(floatval($args_value));
                            break;
                        case 'floor':
                            $result = floor(floatval($args_value));
                            break;
                        case 'round':
                            $result = round(floatval($args_value));
                            break;
                        case 'min':
                            if (is_array($args_value)) {
                                $result = min($args_value);
                            } else {
                                $args_array = explode(',', $processed_args);
                                $args_values = array();
                                foreach ($args_array as $arg) {
                                    $arg = trim($arg);
                                    if (empty($arg)) continue;

                                    try {
                                        $arg_code = 'return ' . $arg . ';';
                                        $arg_value = eval($arg_code);
                                        if ($arg_value !== false) {
                                            $args_values[] = floatval($arg_value);
                                        }
                                    } catch (Exception $e) {
                                        error_log("Error evaluating min argument: {$arg}");
                                    }
                                }
                                $result = !empty($args_values) ? min($args_values) : 0;
                            }
                            break;
                        case 'max':
                            if (is_array($args_value)) {
                                $result = max($args_value);
                            } else {
                                $args_array = explode(',', $processed_args);
                                $args_values = array();
                                foreach ($args_array as $arg) {
                                    $arg = trim($arg);
                                    if (empty($arg)) continue;

                                    try {
                                        $arg_code = 'return ' . $arg . ';';
                                        $arg_value = eval($arg_code);
                                        if ($arg_value !== false) {
                                            $args_values[] = floatval($arg_value);
                                        }
                                    } catch (Exception $e) {
                                        error_log("Error evaluating max argument: {$arg}");
                                    }
                                }
                                $result = !empty($args_values) ? max($args_values) : 0;
                            }
                            break;
                        default:
                            $result = floatval($args_value);
                    }

                    // Replace the function call with its result
                    $full_func = substr($formula, $func_pos, $close_paren_pos - $func_pos + 1);
                    $formula = str_replace($full_func, $result, $formula);

                    // Debug the function evaluation
                    error_log("Function {$function}({$processed_args}) = {$result}");
                    error_log("Formula after function evaluation: {$formula}");

                    // Reset offset to start of the formula since we modified it
                    $offset = 0;

                } catch (ParseError $e) {
                    error_log("Function {$function} args parse error: " . $e->getMessage());
                    // Replace with 0 and continue
                    $full_func = substr($formula, $func_pos, $close_paren_pos - $func_pos + 1);
                    $formula = str_replace($full_func, '0', $formula);
                    error_log("Replaced function with parse error with 0: {$formula}");
                    $offset = 0;
                } catch (Exception $e) {
                    error_log("Function {$function} exception: " . $e->getMessage());
                    // Replace with 0 and continue
                    $full_func = substr($formula, $func_pos, $close_paren_pos - $func_pos + 1);
                    $formula = str_replace($full_func, '0', $formula);
                    error_log("Replaced function with exception with 0: {$formula}");
                    $offset = 0;
                }
            }
        }

        return $formula;
    }

    /**
     * Sanitize a formula to make it safe for evaluation.
     *
     * @since    1.0.0
     * @param    string   $formula    Formula to sanitize.
     * @return   string               Sanitized formula.
     */
    private function sanitize_formula($formula) {
        error_log('Sanitizing formula: ' . $formula);

        // First, preserve function names and curly braces (field references)
        $placeholders = array();
        $placeholder_index = 0;

        // Preserve field references (curly braces)
        preg_match_all('/\{[^{}]+\}/', $formula, $matches);
        foreach ($matches[0] as $match) {
            $placeholder = "___FIELD_{$placeholder_index}___";
            $placeholders[$placeholder] = $match;
            $formula = str_replace($match, $placeholder, $formula);
            $placeholder_index++;
        }

        // Preserve function names
        $functions = array('ceil', 'floor', 'round', 'min', 'max');
        foreach ($functions as $function) {
            preg_match_all('/\b' . $function . '\s*\(/i', $formula, $matches);
            foreach ($matches[0] as $match) {
                $placeholder = "___FUNC_NAME_{$placeholder_index}___";
                $placeholders[$placeholder] = $match;
                $formula = str_replace($match, $placeholder, $formula);
                $placeholder_index++;
            }
        }

        // Remove any potentially harmful code
        $formula = preg_replace('/[^0-9\+\-\*\/\(\)\.\,\?\:\<\>\=\!\&\|\%\s\_]/', '', $formula);
        error_log('After removing harmful code: ' . $formula);

        // Replace multiple operators with a single one
        $formula = preg_replace('/\+\+/', '+', $formula);
        $formula = preg_replace('/\-\-/', '+', $formula);
        $formula = preg_replace('/\+\-/', '-', $formula);
        $formula = preg_replace('/\-\+/', '-', $formula);
        error_log('After replacing multiple operators: ' . $formula);

        // Ensure proper spacing around operators for better readability and evaluation
        $formula = preg_replace('/\s*\+\s*/', ' + ', $formula);
        $formula = preg_replace('/\s*\-\s*/', ' - ', $formula);
        $formula = preg_replace('/\s*\*\s*/', ' * ', $formula);
        $formula = preg_replace('/\s*\/\s*/', ' / ', $formula);
        $formula = preg_replace('/\s*\(\s*/', ' ( ', $formula);
        $formula = preg_replace('/\s*\)\s*/', ' ) ', $formula);
        error_log('After spacing operators: ' . $formula);

        // Check for division by zero
        if (preg_match('/\/\s*0(\s|$)/', $formula)) {
            error_log('WARNING: Division by zero detected in formula: ' . $formula);
            $formula = preg_replace('/\/\s*0(\s|$)/', '/1$1', $formula);
            error_log('Fixed division by zero: ' . $formula);
        }

        // Check for empty parentheses
        if (preg_match('/\(\s*\)/', $formula)) {
            error_log('WARNING: Empty parentheses detected in formula: ' . $formula);
            $formula = preg_replace('/\(\s*\)/', '(0)', $formula);
            error_log('Fixed empty parentheses: ' . $formula);
        }

        // Check for operators at the beginning or end
        if (preg_match('/^[\+\-\*\/]/', trim($formula))) {
            error_log('WARNING: Operator at beginning of formula: ' . $formula);
            $formula = '0' . $formula;
            error_log('Fixed operator at beginning: ' . $formula);
        }

        if (preg_match('/[\+\-\*\/]$/', trim($formula))) {
            error_log('WARNING: Operator at end of formula: ' . $formula);
            $formula .= '0';
            error_log('Fixed operator at end: ' . $formula);
        }

        // Ensure proper parentheses balance and structure
        $open_count = substr_count($formula, '(');
        $close_count = substr_count($formula, ')');
        error_log("Parentheses count: open={$open_count}, close={$close_count}");

        // Check for proper nesting
        $level = 0;
        $balanced = true;
        $chars = str_split($formula);

        for ($i = 0; $i < count($chars); $i++) {
            if ($chars[$i] === '(') {
                $level++;
            } elseif ($chars[$i] === ')') {
                $level--;
                if ($level < 0) {
                    $balanced = false;
                    break;
                }
            }
        }

        if (!$balanced || $level !== 0) {
            error_log('WARNING: Unbalanced or improperly nested parentheses in formula: ' . $formula);

            // Reset formula to ensure proper structure
            $formula = preg_replace('/[\(\)]/', '', $formula);
            error_log('Removed all parentheses: ' . $formula);

            // Add parentheses around the entire formula for safety
            $formula = '( ' . $formula . ' )';
            error_log('Added parentheses around formula: ' . $formula);
        } else {
            // Add missing closing parentheses at the end
            if ($open_count > $close_count) {
                $formula .= str_repeat(' ) ', $open_count - $close_count);
                error_log('Added ' . ($open_count - $close_count) . ' closing parentheses: ' . $formula);
            }
            // Add missing opening parentheses at the beginning
            else if ($close_count > $open_count) {
                $formula = str_repeat(' ( ', $close_count - $open_count) . $formula;
                error_log('Added ' . ($close_count - $open_count) . ' opening parentheses: ' . $formula);
            }
        }

        // Restore placeholders
        foreach ($placeholders as $placeholder => $original) {
            $formula = str_replace($placeholder, $original, $formula);
        }

        $formula = trim($formula);
        error_log('Final sanitized formula: ' . $formula);
        return $formula;
    }
}
