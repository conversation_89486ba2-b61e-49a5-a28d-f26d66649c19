<?php
/**
 * Form Saver Class
 *
 * This class handles saving forms to the database, completely rewritten from scratch.
 *
 * @since      1.0.0
 * @package    PFB
 * @subpackage PFB/includes
 */

class PFB_Form_Saver {

    /**
     * Database tables
     */
    private $forms_table;
    private $fields_table;

    /**
     * Initialize the class
     */
    public function __construct() {
        global $wpdb;
        $this->forms_table = $wpdb->prefix . 'pfb_forms';
        $this->fields_table = $wpdb->prefix . 'pfb_form_fields';

        // Add AJAX handlers
        add_action('wp_ajax_pfb_save_form_new', array($this, 'ajax_save_form'));

        // Add debug endpoint
        add_action('wp_ajax_pfb_debug_form_save', array($this, 'debug_form_save'));
    }

    /**
     * AJAX handler for saving forms
     */
    public function ajax_save_form() {
        try {
            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'pfb_nonce')) {
                error_log('PFB Form Saver: Security check failed.');
                wp_send_json_error(array('message' => 'Security check failed.'));
                return;
            }

            // Check if user has permission
            if (!current_user_can('manage_options')) {
                error_log('PFB Form Saver: Permission denied.');
                wp_send_json_error(array('message' => 'You do not have permission to save forms.'));
                return;
            }

            // Get form data
            $form_data = isset($_POST['form_data']) ? $_POST['form_data'] : null;

            if (!$form_data) {
                error_log('PFB Form Saver: No form data provided.');
                wp_send_json_error(array('message' => 'No form data provided.'));
                return;
            }

            // Log the raw form data
            error_log('PFB Form Saver: Raw form data: ' . print_r($form_data, true));

            // Check if form data is properly structured
            if (!isset($form_data['title']) || !isset($form_data['fields']) || !is_array($form_data['fields'])) {
                error_log('PFB Form Saver: Form data is missing required fields.');
                wp_send_json_error(array('message' => 'Form data is missing required fields.'));
                return;
            }

            // Save form
            $form_id = $this->save_form($form_data);

            if (!$form_id) {
                error_log('PFB Form Saver: Failed to save form.');
                wp_send_json_error(array('message' => 'Failed to save form.'));
                return;
            }

            error_log('PFB Form Saver: Form saved successfully. Form ID: ' . $form_id);
            wp_send_json_success(array(
                'message' => 'Form saved successfully.',
                'form_id' => $form_id
            ));
        } catch (Exception $e) {
            error_log('PFB Form Saver Exception: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Error saving form: ' . $e->getMessage()));
        }
    }

    /**
     * Save form to database
     *
     * @param array $form_data Form data
     * @return int|false Form ID on success, false on failure
     */
    public function save_form($form_data) {
        global $wpdb;

        // Start transaction
        $wpdb->query('START TRANSACTION');

        try {
            // Validate form data
            if (!isset($form_data['title']) || empty($form_data['title'])) {
                throw new Exception('Form title is required.');
            }

            if (!isset($form_data['fields']) || !is_array($form_data['fields'])) {
                throw new Exception('Form fields are required.');
            }

            // Prepare form data
            $form = array(
                'title'       => sanitize_text_field($form_data['title']),
                'description' => isset($form_data['description']) ? sanitize_textarea_field($form_data['description']) : '',
                'status'      => isset($form_data['status']) ? sanitize_text_field($form_data['status']) : 'publish',
                'settings'    => $this->prepare_form_settings(isset($form_data['settings']) ? $form_data['settings'] : array())
            );

            error_log('PFB Form Saver: Prepared form data: ' . print_r($form, true));

            // Check if we're updating an existing form
            if (isset($form_data['id']) && !empty($form_data['id'])) {
                $form_id = intval($form_data['id']);

                // Check if form exists
                $existing_form = $wpdb->get_var($wpdb->prepare("SELECT id FROM {$this->forms_table} WHERE id = %d", $form_id));

                if (!$existing_form) {
                    throw new Exception("Form with ID {$form_id} does not exist.");
                }

                // Update form
                $result = $wpdb->update(
                    $this->forms_table,
                    $form,
                    array('id' => $form_id)
                );

                if ($result === false) {
                    throw new Exception('Failed to update form: ' . $wpdb->last_error);
                }

                error_log("PFB Form Saver: Updated form with ID {$form_id}");
            } else {
                // Insert new form
                $result = $wpdb->insert($this->forms_table, $form);

                if ($result === false) {
                    throw new Exception('Failed to insert form: ' . $wpdb->last_error);
                }

                $form_id = $wpdb->insert_id;
                error_log("PFB Form Saver: Inserted new form with ID {$form_id}");
            }

            // Save form fields
            $fields_result = $this->save_form_fields($form_id, $form_data['fields']);

            if ($fields_result === false) {
                throw new Exception('Failed to save form fields.');
            }

            // Commit transaction
            $wpdb->query('COMMIT');

            error_log("PFB Form Saver: Form saved successfully. Form ID: {$form_id}");
            return $form_id;
        } catch (Exception $e) {
            // Rollback transaction on error
            $wpdb->query('ROLLBACK');
            error_log('PFB Form Saver Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Prepare form settings
     *
     * @param array $settings Form settings
     * @return string Serialized settings
     */
    private function prepare_form_settings($settings) {
        // Ensure settings is an array
        if (!is_array($settings)) {
            $settings = array();
        }

        // Sanitize settings
        $sanitized_settings = array();

        // Template
        if (isset($settings['template'])) {
            $sanitized_settings['template'] = sanitize_text_field($settings['template']);
        }

        // Show currency selector
        if (isset($settings['show_currency_selector'])) {
            $sanitized_settings['show_currency_selector'] = sanitize_text_field($settings['show_currency_selector']);
        }

        // Submit button text
        if (isset($settings['submit_button_text'])) {
            $sanitized_settings['submit_button_text'] = sanitize_text_field($settings['submit_button_text']);
        }

        // Serialize settings
        return serialize($sanitized_settings);
    }

    /**
     * Save form fields
     *
     * @param int $form_id Form ID
     * @param array $fields Form fields
     * @return bool True on success, false on failure
     */
    private function save_form_fields($form_id, $fields) {
        global $wpdb;

        try {
            // Validate form ID
            if (!$form_id || !is_numeric($form_id)) {
                throw new Exception('Invalid form ID: ' . $form_id);
            }

            // Validate fields array
            if (!is_array($fields)) {
                throw new Exception('Fields must be an array');
            }

            error_log('PFB Form Saver: Saving ' . count($fields) . ' fields for form ID ' . $form_id);

            // Delete existing fields
            $delete_result = $wpdb->delete($this->fields_table, array('form_id' => $form_id));

            if ($delete_result === false) {
                throw new Exception('Failed to delete existing fields: ' . $wpdb->last_error);
            }

            error_log('PFB Form Saver: Deleted ' . $delete_result . ' existing fields');

            // Insert new fields
            $inserted_count = 0;
            foreach ($fields as $order => $field) {
                // Validate field data
                if (!isset($field['type']) || empty($field['type'])) {
                    error_log('PFB Form Saver: Field type is required. Skipping field at index ' . $order);
                    continue;
                }

                // Prepare field data
                $field_data = $this->prepare_field_data($field, $form_id, $order);

                // Log field data for debugging
                error_log('PFB Form Saver: Prepared field data for ' . $field['type'] . ' field: ' . print_r($field_data, true));

                // Insert field
                $result = $wpdb->insert($this->fields_table, $field_data);

                if ($result === false) {
                    throw new Exception('Failed to insert field: ' . $wpdb->last_error);
                }

                $inserted_count++;
            }

            error_log('PFB Form Saver: Successfully inserted ' . $inserted_count . ' fields');
            return true;
        } catch (Exception $e) {
            error_log('PFB Form Saver Error in save_form_fields: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Prepare field data for database
     *
     * @param array $field Field data
     * @param int $form_id Form ID
     * @param int $order Field order
     * @return array Prepared field data
     */
    private function prepare_field_data($field, $form_id, $order) {
        try {
            // Validate required field properties
            if (!isset($field['type']) || empty($field['type'])) {
                throw new Exception('Field type is required');
            }

            // Ensure label exists
            if (!isset($field['label'])) {
                $field['label'] = 'Field ' . ($order + 1);
                error_log('PFB Form Saver: Field label not provided, using default: ' . $field['label']);
            }

            // Ensure name exists
            if (!isset($field['name']) || empty($field['name'])) {
                $field['name'] = 'field_' . ($order + 1);
                error_log('PFB Form Saver: Field name not provided, using default: ' . $field['name']);
            }

            // Basic field data
            $field_data = array(
                'form_id'        => $form_id,
                'field_type'     => sanitize_text_field($field['type']),
                'field_label'    => sanitize_text_field($field['label']),
                'field_name'     => sanitize_key($field['name']),
                'field_required' => isset($field['required']) && $field['required'] ? 1 : 0,
                'field_width'    => isset($field['width']) ? intval($field['width']) : 100,
                'field_order'    => $order
            );

            // Process field options based on field type
            $field_data['field_options'] = $this->prepare_field_options($field);

            // Process conditional logic
            $field_data['conditional_logic'] = $this->prepare_conditional_logic($field);

            return $field_data;
        } catch (Exception $e) {
            error_log('PFB Form Saver Error in prepare_field_data: ' . $e->getMessage());

            // Return a minimal valid field data structure
            return array(
                'form_id'           => $form_id,
                'field_type'        => isset($field['type']) ? sanitize_text_field($field['type']) : 'text',
                'field_label'       => 'Field ' . ($order + 1),
                'field_name'        => 'field_' . ($order + 1),
                'field_required'    => 0,
                'field_width'       => 100,
                'field_order'       => $order,
                'field_options'     => serialize(array()),
                'conditional_logic' => json_encode(array('enabled' => false, 'logic_type' => 'all', 'rules' => array()))
            );
        }
    }

    /**
     * Prepare field options based on field type
     *
     * @param array $field Field data
     * @return string Serialized field options
     */
    private function prepare_field_options($field) {
        try {
            // Ensure options is an array
            if (!isset($field['options']) || !is_array($field['options'])) {
                $field['options'] = array();
                error_log('PFB Form Saver: Field options not provided or not an array, using empty array');
            }

            $options = array();

            // Process field options based on field type
            switch ($field['type']) {
                case 'select':
                case 'radio':
                case 'checkbox':
                    $options = $this->prepare_choice_field_options($field);
                    break;

                case 'number':
                    $options = $this->prepare_number_field_options($field);
                    break;

                case 'slider':
                    $options = $this->prepare_slider_field_options($field);
                    break;

                case 'price':
                case 'total':
                    $options = $this->prepare_price_field_options($field);
                    break;

                case 'subtotal':
                    $options = $this->prepare_subtotal_field_options($field);
                    break;

                case 'text':
                    $options = $this->prepare_text_field_options($field);
                    break;

                default:
                    // For unknown field types, just use the options as is
                    $options = $field['options'];
                    error_log('PFB Form Saver: Unknown field type: ' . $field['type'] . ', using options as is');
                    break;
            }

            // Add common options
            $options = $this->add_common_options($options, $field);

            // Log the options before serialization
            error_log('PFB Form Saver: Field options before serialization for ' . $field['type'] . ': ' . print_r($options, true));

            // Serialize options
            $serialized = serialize($options);

            // Verify serialization worked
            if ($serialized === false) {
                throw new Exception('Failed to serialize field options');
            }

            return $serialized;
        } catch (Exception $e) {
            error_log('PFB Form Saver Error in prepare_field_options: ' . $e->getMessage());

            // Return serialized empty array as fallback
            return serialize(array());
        }
    }

    /**
     * Prepare choice field options (select, radio, checkbox)
     *
     * @param array $field Field data
     * @return array Prepared options
     */
    private function prepare_choice_field_options($field) {
        try {
            $options = array();

            // Process items
            $items = array();

            // Check if options.items exists
            if (isset($field['options']['items']) && is_array($field['options']['items'])) {
                error_log('PFB Form Saver: Processing items array for choice field, count: ' . count($field['options']['items']));

                foreach ($field['options']['items'] as $index => $item) {
                    // Skip invalid items
                    if (!is_array($item)) {
                        error_log('PFB Form Saver: Skipping invalid item at index ' . $index . ', not an array');
                        continue;
                    }

                    // Ensure item has required properties
                    $processed_item = array(
                        'label' => isset($item['label']) ? sanitize_text_field($item['label']) : 'Option ' . ($index + 1),
                        'value' => isset($item['value']) ? sanitize_text_field($item['value']) : 'option_' . ($index + 1),
                        'variable' => isset($item['variable']) ? sanitize_text_field($item['variable']) : ''
                    );

                    $items[] = $processed_item;
                    error_log('PFB Form Saver: Processed item: ' . print_r($processed_item, true));
                }
            }
            // Check if options.choices exists (backward compatibility)
            else if (isset($field['options']['choices']) && is_array($field['options']['choices'])) {
                error_log('PFB Form Saver: Processing choices array for choice field (backward compatibility), count: ' . count($field['options']['choices']));

                foreach ($field['options']['choices'] as $index => $item) {
                    // Skip invalid items
                    if (!is_array($item)) {
                        error_log('PFB Form Saver: Skipping invalid choice at index ' . $index . ', not an array');
                        continue;
                    }

                    // Ensure item has required properties
                    $processed_item = array(
                        'label' => isset($item['label']) ? sanitize_text_field($item['label']) : 'Option ' . ($index + 1),
                        'value' => isset($item['value']) ? sanitize_text_field($item['value']) : 'option_' . ($index + 1),
                        'variable' => isset($item['variable']) ? sanitize_text_field($item['variable']) : ''
                    );

                    $items[] = $processed_item;
                    error_log('PFB Form Saver: Processed choice: ' . print_r($processed_item, true));
                }
            }

            // Ensure we have at least one item
            if (empty($items)) {
                $default_item = array(
                    'label' => 'Option 1',
                    'value' => 'option_1',
                    'variable' => ''
                );

                $items[] = $default_item;
                error_log('PFB Form Saver: No items found, adding default item: ' . print_r($default_item, true));
            }

            $options['items'] = $items;

            // Add variable if it exists
            if (isset($field['options']['variable'])) {
                $options['variable'] = sanitize_text_field($field['options']['variable']);
            }

            error_log('PFB Form Saver: Final choice field options: ' . print_r($options, true));
            return $options;
        } catch (Exception $e) {
            error_log('PFB Form Saver Error in prepare_choice_field_options: ' . $e->getMessage());

            // Return default options as fallback
            return array(
                'items' => array(
                    array(
                        'label' => 'Option 1',
                        'value' => 'option_1',
                        'variable' => ''
                    )
                )
            );
        }
    }

    /**
     * Prepare number field options
     *
     * @param array $field Field data
     * @return array Prepared options
     */
    private function prepare_number_field_options($field) {
        $options = array();

        // Set min, max, and step
        $options['min'] = isset($field['options']['min']) ? sanitize_text_field($field['options']['min']) : '0';
        $options['max'] = isset($field['options']['max']) ? sanitize_text_field($field['options']['max']) : '100';
        $options['step'] = isset($field['options']['step']) ? sanitize_text_field($field['options']['step']) : '1';

        return $options;
    }

    /**
     * Prepare slider field options
     *
     * @param array $field Field data
     * @return array Prepared options
     */
    private function prepare_slider_field_options($field) {
        $options = array();

        // Set min, max, step, and default
        $options['min'] = isset($field['options']['min']) ? intval($field['options']['min']) : 0;
        $options['max'] = isset($field['options']['max']) ? intval($field['options']['max']) : 100;
        $options['step'] = isset($field['options']['step']) ? intval($field['options']['step']) : 1;
        $options['default'] = isset($field['options']['default']) ? intval($field['options']['default']) : 50;

        return $options;
    }

    /**
     * Prepare price/total field options
     *
     * @param array $field Field data
     * @return array Prepared options
     */
    private function prepare_price_field_options($field) {
        $options = array();

        // Set formula
        $options['formula'] = isset($field['options']['formula']) ? $field['options']['formula'] : '';

        // For price fields, set price
        if ($field['type'] === 'price' && isset($field['options']['price'])) {
            $options['price'] = sanitize_text_field($field['options']['price']);
        }

        return $options;
    }

    /**
     * Prepare subtotal field options
     *
     * @param array $field Field data
     * @return array Prepared options
     */
    private function prepare_subtotal_field_options($field) {
        $options = array();

        // Process lines
        $lines = array();

        if (isset($field['options']['lines']) && is_array($field['options']['lines'])) {
            foreach ($field['options']['lines'] as $line) {
                // Ensure line has required properties
                $lines[] = array(
                    'label' => isset($line['label']) ? sanitize_text_field($line['label']) : 'Line',
                    'formula' => isset($line['formula']) ? $line['formula'] : ''
                );
            }
        }

        // Ensure we have at least one line
        if (empty($lines)) {
            $lines[] = array(
                'label' => 'Line 1',
                'formula' => ''
            );
        }

        $options['lines'] = $lines;

        // Set empty_value
        $options['empty_value'] = isset($field['options']['empty_value']) ? sanitize_text_field($field['options']['empty_value']) : '---';

        return $options;
    }

    /**
     * Prepare text field options
     *
     * @param array $field Field data
     * @return array Prepared options
     */
    private function prepare_text_field_options($field) {
        $options = array();

        // Handle hidden field properties
        if (isset($field['hidden']) && $field['hidden']) {
            $options['is_hidden'] = true;

            // Copy variable and default_value to options
            if (isset($field['variable'])) {
                $options['variable'] = sanitize_text_field($field['variable']);
            }

            if (isset($field['default_value'])) {
                $options['default_value'] = sanitize_text_field($field['default_value']);
            }
        }

        return $options;
    }

    /**
     * Add common options to field options
     *
     * @param array $options Field options
     * @param array $field Field data
     * @return array Updated options
     */
    private function add_common_options($options, $field) {
        // Add placeholder if it exists
        if (isset($field['options']['placeholder'])) {
            $options['placeholder'] = sanitize_text_field($field['options']['placeholder']);
        } else if (isset($field['placeholder'])) {
            $options['placeholder'] = sanitize_text_field($field['placeholder']);
        }

        // Add default if it exists
        if (isset($field['options']['default'])) {
            $options['default'] = sanitize_text_field($field['options']['default']);
        } else if (isset($field['default'])) {
            $options['default'] = sanitize_text_field($field['default']);
        }

        // Add description if it exists
        if (isset($field['options']['description'])) {
            $options['description'] = sanitize_textarea_field($field['options']['description']);
        } else if (isset($field['description'])) {
            $options['description'] = sanitize_textarea_field($field['description']);
        }

        return $options;
    }

    /**
     * Prepare conditional logic
     *
     * @param array $field Field data
     * @return string JSON encoded conditional logic
     */
    private function prepare_conditional_logic($field) {
        // Check if conditional logic exists
        if (!isset($field['conditional_logic']) || !is_array($field['conditional_logic'])) {
            // Return empty conditional logic
            return json_encode(array(
                'enabled' => false,
                'logic_type' => 'all',
                'rules' => array()
            ));
        }

        $conditional_logic = $field['conditional_logic'];

        // Ensure enabled is set
        if (!isset($conditional_logic['enabled'])) {
            $conditional_logic['enabled'] =
                isset($conditional_logic['rules']) &&
                is_array($conditional_logic['rules']) &&
                count($conditional_logic['rules']) > 0;
        }

        // Ensure logic_type is set
        if (!isset($conditional_logic['logic_type']) || empty($conditional_logic['logic_type'])) {
            $conditional_logic['logic_type'] = 'all';
        }

        // Ensure rules is an array
        if (!isset($conditional_logic['rules']) || !is_array($conditional_logic['rules'])) {
            $conditional_logic['rules'] = array();
        }

        // Process rules
        $processed_rules = array();

        foreach ($conditional_logic['rules'] as $rule) {
            // Skip rules with invalid field
            if (!isset($rule['field']) || empty($rule['field']) || $rule['field'] === '0' || $rule['field'] === 0) {
                continue;
            }

            // Ensure rule has required properties
            $processed_rule = array(
                'field' => sanitize_text_field($rule['field']),
                'operator' => isset($rule['operator']) ? sanitize_text_field($rule['operator']) : 'is',
                'value' => isset($rule['value']) ? sanitize_text_field($rule['value']) : ''
            );

            // Add field_name if it exists
            if (isset($rule['field_name'])) {
                $processed_rule['field_name'] = sanitize_text_field($rule['field_name']);
            }

            // Add field_label if it exists
            if (isset($rule['field_label'])) {
                $processed_rule['field_label'] = sanitize_text_field($rule['field_label']);
            }

            $processed_rules[] = $processed_rule;
        }

        $conditional_logic['rules'] = $processed_rules;

        // Log the conditional logic before encoding
        error_log('PFB Form Saver: Conditional logic before encoding: ' . print_r($conditional_logic, true));

        // Encode conditional logic
        return json_encode($conditional_logic);
    }

    /**
     * Debug form save
     */
    public function debug_form_save() {
        // Check if user has permission
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        // Get form data
        $form_data = isset($_POST['form_data']) ? $_POST['form_data'] : null;

        if (!$form_data) {
            wp_send_json_error('No form data provided');
        }

        // Process form data without saving
        $processed_data = array(
            'form' => array(
                'title' => sanitize_text_field($form_data['title']),
                'description' => sanitize_textarea_field($form_data['description']),
                'status' => sanitize_text_field($form_data['status']),
                'settings' => $this->prepare_form_settings($form_data['settings'])
            ),
            'fields' => array()
        );

        // Process fields
        if (isset($form_data['fields']) && is_array($form_data['fields'])) {
            foreach ($form_data['fields'] as $order => $field) {
                // Skip fields without type
                if (!isset($field['type']) || empty($field['type'])) {
                    continue;
                }

                // Prepare field data
                $field_data = $this->prepare_field_data($field, 0, $order);

                // Add to processed data
                $processed_data['fields'][] = $field_data;
            }
        }

        wp_send_json_success($processed_data);
    }
}
