<?php
/**
 * Create the pfb_variables table if it doesn't exist.
 * This is a compatibility script to ensure the plugin works with older versions.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Create the pfb_variables table if it doesn't exist.
 *
 * @return bool True on success, false on failure.
 */
function pfb_create_variables_table() {
    global $wpdb;
    $charset_collate = $wpdb->get_charset_collate();
    
    // Variables table (alias for price_variables for backward compatibility)
    $table_name = $wpdb->prefix . 'pfb_variables';
    
    // Check if the table already exists
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
    
    if (!$table_exists) {
        error_log('PFB: Creating pfb_variables table');
        
        $sql = "CREATE TABLE {$table_name} (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            category_id mediumint(9) NOT NULL,
            name varchar(255) NOT NULL,
            variable_key varchar(255) NOT NULL,
            type varchar(20) NOT NULL DEFAULT 'fixed',
            value decimal(15,2) DEFAULT 0,
            price decimal(15,2) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP NOT NULL,
            PRIMARY KEY  (id),
            KEY category_id (category_id)
        ) {$charset_collate};";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $result = dbDelta($sql);
        
        error_log('PFB: pfb_variables table creation result: ' . print_r($result, true));
        
        // Copy data from price_variables table if it exists
        $price_variables_table = $wpdb->prefix . 'pfb_price_variables';
        $price_variables_exists = $wpdb->get_var("SHOW TABLES LIKE '{$price_variables_table}'") === $price_variables_table;
        
        if ($price_variables_exists) {
            error_log('PFB: Copying data from pfb_price_variables to pfb_variables');
            
            // Get all price variables
            $price_variables = $wpdb->get_results("SELECT * FROM {$price_variables_table}", ARRAY_A);
            
            if (!empty($price_variables)) {
                foreach ($price_variables as $variable) {
                    // Insert into variables table
                    $wpdb->insert(
                        $table_name,
                        array(
                            'category_id' => $variable['category_id'],
                            'name' => $variable['name'],
                            'variable_key' => $variable['variable_key'],
                            'type' => $variable['price_type'],
                            'value' => $variable['price_value'],
                            'price' => $variable['price_value']
                        )
                    );
                }
                
                error_log('PFB: Copied ' . count($price_variables) . ' variables from pfb_price_variables to pfb_variables');
            }
        }
        
        return true;
    }
    
    error_log('PFB: pfb_variables table already exists');
    return true;
}
