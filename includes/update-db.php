<?php
/**
 * Update database schema for the plugin.
 *
 * This file contains functions to update the database schema when needed.
 *
 * @since      1.0.0
 */

/**
 * Add field_width column to pfb_form_fields table if it doesn't exist.
 *
 * @since    1.0.0
 * @return   bool    True on success, false on failure.
 */
function pfb_add_field_width_column() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'pfb_form_fields';

    error_log('PFB: Checking if field_width column exists in table: ' . $table_name);

    // Check if the column already exists
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'field_width'");

    error_log('PFB: Column exists check result: ' . print_r($column_exists, true));

    if (empty($column_exists)) {
        error_log('PFB: field_width column does not exist, adding it now');

        // Add the column
        $sql = "ALTER TABLE {$table_name} ADD COLUMN field_width varchar(10) DEFAULT '100' AFTER field_required";
        error_log('PFB: Running SQL: ' . $sql);

        $result = $wpdb->query($sql);

        if ($result === false) {
            error_log('PFB: Failed to add field_width column to pfb_form_fields table. Error: ' . $wpdb->last_error);
            return false;
        }

        error_log('PFB: Successfully added field_width column');

        // Update existing fields to set width from field_options
        $fields = $wpdb->get_results("SELECT id, field_options FROM {$table_name}", ARRAY_A);

        error_log('PFB: Found ' . count($fields) . ' fields to update');

        foreach ($fields as $field) {
            $options = maybe_unserialize($field['field_options']);

            error_log('PFB: Processing field ID ' . $field['id'] . ' with options: ' . print_r($options, true));

            if (is_array($options) && isset($options['width'])) {
                $width = $options['width'];
                error_log('PFB: Found width ' . $width . ' in options for field ID ' . $field['id']);

                $update_result = $wpdb->update(
                    $table_name,
                    array('field_width' => $width),
                    array('id' => $field['id'])
                );

                error_log('PFB: Updated field_width for field ID ' . $field['id'] . ': ' . ($update_result !== false ? 'success' : 'failed - ' . $wpdb->last_error));

                // Remove width from options to avoid duplication
                unset($options['width']);
                $serialized_options = maybe_serialize($options);

                $update_result = $wpdb->update(
                    $table_name,
                    array('field_options' => $serialized_options),
                    array('id' => $field['id'])
                );

                error_log('PFB: Removed width from options for field ID ' . $field['id'] . ': ' . ($update_result !== false ? 'success' : 'failed - ' . $wpdb->last_error));
            } else {
                error_log('PFB: No width found in options for field ID ' . $field['id']);
            }
        }

        error_log('PFB: Successfully added field_width column to pfb_form_fields table and updated existing fields');
        return true;
    }

    // Column already exists
    error_log('PFB: field_width column already exists');
    return true;
}

/**
 * Add conditional_logic column to pfb_form_fields table if it doesn't exist.
 *
 * @since    1.0.2
 * @return   bool    True on success, false on failure.
 */
function pfb_add_conditional_logic_column() {
    global $wpdb;

    $table_name = $wpdb->prefix . 'pfb_form_fields';

    error_log('PFB: Checking if conditional_logic column exists in table: ' . $table_name);

    // Check if the column already exists
    $column_exists = $wpdb->get_results("SHOW COLUMNS FROM {$table_name} LIKE 'conditional_logic'");

    error_log('PFB: Column exists check result: ' . print_r($column_exists, true));

    if (empty($column_exists)) {
        error_log('PFB: conditional_logic column does not exist, adding it now');

        // Add the column
        $sql = "ALTER TABLE {$table_name} ADD COLUMN conditional_logic LONGTEXT AFTER field_options";
        error_log('PFB: Running SQL: ' . $sql);

        $result = $wpdb->query($sql);

        if ($result === false) {
            error_log('PFB: Failed to add conditional_logic column to pfb_form_fields table. Error: ' . $wpdb->last_error);
            return false;
        }

        error_log('PFB: Successfully added conditional_logic column to pfb_form_fields table');
        return true;
    }

    // Column already exists
    error_log('PFB: conditional_logic column already exists');
    return true;
}
