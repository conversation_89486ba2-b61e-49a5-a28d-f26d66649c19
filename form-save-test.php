<?php
/**
 * Form Save Test Script
 * 
 * This script tests saving a form directly to the database.
 */

// Load WordPress
require_once('wp-load.php');

// Check if user is logged in and has admin privileges
if (!current_user_can('manage_options')) {
    die('You do not have permission to access this page.');
}

// Get database connection
global $wpdb;
$forms_table = $wpdb->prefix . 'pfb_forms';
$fields_table = $wpdb->prefix . 'pfb_form_fields';

// Enable error display
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Function to create a test form
function create_test_form() {
    global $wpdb, $forms_table, $fields_table;
    
    // Start transaction
    $wpdb->query('START TRANSACTION');
    
    try {
        // Insert form
        $form_data = array(
            'title' => 'Test Form ' . date('Y-m-d H:i:s'),
            'description' => 'This is a test form created by the diagnostic script',
            'status' => 'publish',
            'settings' => serialize(array(
                'template' => 'default',
                'show_currency_selector' => '0',
                'submit_button_text' => 'Submit'
            ))
        );
        
        $wpdb->insert($forms_table, $form_data);
        $form_id = $wpdb->insert_id;
        
        if (!$form_id) {
            throw new Exception('Failed to insert form: ' . $wpdb->last_error);
        }
        
        echo "<p>Form created with ID: {$form_id}</p>";
        
        // Create test fields
        $fields = array(
            // Text field
            array(
                'form_id' => $form_id,
                'field_type' => 'text',
                'field_label' => 'Text Field',
                'field_name' => 'text_field',
                'field_required' => 0,
                'field_width' => 100,
                'field_order' => 0,
                'field_options' => serialize(array(
                    'placeholder' => 'Enter text here',
                    'default' => '',
                    'description' => 'This is a text field'
                )),
                'conditional_logic' => json_encode(array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'rules' => array()
                ))
            ),
            
            // Select field
            array(
                'form_id' => $form_id,
                'field_type' => 'select',
                'field_label' => 'Select Field',
                'field_name' => 'select_field',
                'field_required' => 0,
                'field_width' => 100,
                'field_order' => 1,
                'field_options' => serialize(array(
                    'items' => array(
                        array(
                            'label' => 'Option 1',
                            'value' => 'option_1',
                            'variable' => ''
                        ),
                        array(
                            'label' => 'Option 2',
                            'value' => 'option_2',
                            'variable' => ''
                        ),
                        array(
                            'label' => 'Option 3',
                            'value' => 'option_3',
                            'variable' => ''
                        )
                    ),
                    'description' => 'This is a select field'
                )),
                'conditional_logic' => json_encode(array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'rules' => array()
                ))
            ),
            
            // Number field
            array(
                'form_id' => $form_id,
                'field_type' => 'number',
                'field_label' => 'Number Field',
                'field_name' => 'number_field',
                'field_required' => 0,
                'field_width' => 100,
                'field_order' => 2,
                'field_options' => serialize(array(
                    'min' => '0',
                    'max' => '100',
                    'step' => '1',
                    'description' => 'This is a number field'
                )),
                'conditional_logic' => json_encode(array(
                    'enabled' => false,
                    'logic_type' => 'all',
                    'rules' => array()
                ))
            )
        );
        
        // Insert fields
        foreach ($fields as $index => $field) {
            $result = $wpdb->insert($fields_table, $field);
            
            if ($result === false) {
                throw new Exception("Failed to insert field {$index}: " . $wpdb->last_error);
            }
            
            echo "<p>Field {$index} ({$field['field_type']}) inserted successfully</p>";
        }
        
        // Commit transaction
        $wpdb->query('COMMIT');
        
        echo "<p style='color: green;'>Test form created successfully!</p>";
        echo "<p><a href='admin.php?page=pfb-form-editor&form_id={$form_id}'>Edit the test form</a></p>";
        
        return $form_id;
    } catch (Exception $e) {
        // Rollback transaction
        $wpdb->query('ROLLBACK');
        
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
        return false;
    }
}

// Function to test database tables
function test_database_tables() {
    global $wpdb, $forms_table, $fields_table;
    
    echo "<h2>Database Tables Test</h2>";
    
    // Check if tables exist
    $forms_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$forms_table}'") === $forms_table;
    $fields_table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$fields_table}'") === $fields_table;
    
    echo "<p>Forms table exists: " . ($forms_table_exists ? 'Yes' : 'No') . "</p>";
    echo "<p>Fields table exists: " . ($fields_table_exists ? 'Yes' : 'No') . "</p>";
    
    if (!$forms_table_exists || !$fields_table_exists) {
        echo "<p style='color: red;'>Error: One or more required tables do not exist!</p>";
        return false;
    }
    
    // Check table structure
    echo "<h3>Forms Table Structure</h3>";
    $forms_columns = $wpdb->get_results("DESCRIBE {$forms_table}", ARRAY_A);
    echo "<pre>" . print_r($forms_columns, true) . "</pre>";
    
    echo "<h3>Fields Table Structure</h3>";
    $fields_columns = $wpdb->get_results("DESCRIBE {$fields_table}", ARRAY_A);
    echo "<pre>" . print_r($fields_columns, true) . "</pre>";
    
    return true;
}

// Function to test serialization
function test_serialization() {
    echo "<h2>Serialization Test</h2>";
    
    // Test data
    $test_data = array(
        'items' => array(
            array(
                'label' => 'Option 1',
                'value' => 'option_1',
                'variable' => ''
            ),
            array(
                'label' => 'Option 2',
                'value' => 'option_2',
                'variable' => ''
            )
        ),
        'description' => 'This is a test'
    );
    
    // Serialize
    $serialized = serialize($test_data);
    echo "<p>Serialized data: " . htmlspecialchars($serialized) . "</p>";
    
    // Unserialize
    $unserialized = unserialize($serialized);
    echo "<p>Unserialized data: </p>";
    echo "<pre>" . print_r($unserialized, true) . "</pre>";
    
    // Check if data is preserved
    $is_equal = $test_data == $unserialized;
    echo "<p>Data preserved: " . ($is_equal ? 'Yes' : 'No') . "</p>";
    
    return $is_equal;
}

// Function to test JSON encoding
function test_json_encoding() {
    echo "<h2>JSON Encoding Test</h2>";
    
    // Test data
    $test_data = array(
        'enabled' => true,
        'logic_type' => 'all',
        'rules' => array(
            array(
                'field' => 'field_1',
                'field_name' => 'field_1',
                'operator' => 'is',
                'value' => 'test'
            )
        )
    );
    
    // Encode
    $encoded = json_encode($test_data);
    echo "<p>JSON encoded data: " . htmlspecialchars($encoded) . "</p>";
    
    // Decode
    $decoded = json_decode($encoded, true);
    echo "<p>JSON decoded data: </p>";
    echo "<pre>" . print_r($decoded, true) . "</pre>";
    
    // Check if data is preserved
    $is_equal = $test_data == $decoded;
    echo "<p>Data preserved: " . ($is_equal ? 'Yes' : 'No') . "</p>";
    
    return $is_equal;
}

// Main page
echo "<!DOCTYPE html>
<html>
<head>
    <title>Form Save Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2, h3 { color: #333; }
        pre { background-color: #f5f5f5; padding: 10px; overflow: auto; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Form Save Test</h1>";

// Run tests
$db_test_result = test_database_tables();
$serialization_test_result = test_serialization();
$json_test_result = test_json_encoding();

// Create test form if requested
if (isset($_GET['create_test_form']) && $_GET['create_test_form'] === '1') {
    echo "<h2>Creating Test Form</h2>";
    $form_id = create_test_form();
} else {
    echo "<p><a href='?create_test_form=1'>Create a test form</a></p>";
}

echo "</body>
</html>";
